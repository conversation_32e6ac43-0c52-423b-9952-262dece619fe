# MyQU Mobile App

<p align="center">
  <img src="src/assets/icon/logo.svg" alt="MyQU Logo" width="200"/>
</p>

## Overview

MyQU Mobile App is a comprehensive mobile application for university students to access academic information, services, and campus resources. Built with Ionic and Angular, it provides a modern, responsive interface for students to manage their academic life.

## Features

- **Student Profile**: View and manage your student profile and ID card
- **Academic Information**: Access courses, grades, and academic schedule
- **Absences Management**: Track and submit excuses for class absences
- **Rewards & Financial Services**: Monitor financial aid and rewards
- **Appointment Booking**: Schedule appointments with university services
- **Multi-language Support**: Full support for English and Arabic
- **Theme Customization**: Light and dark mode options
- **Responsive Design**: Works seamlessly on all device sizes

## Screenshots

<p align="center">
  <img src="screenshots/home.png" alt="Home Screen" width="200"/>
  <img src="screenshots/absences.png" alt="Absences Screen" width="200"/>
  <img src="screenshots/profile.png" alt="Profile Screen" width="200"/>
</p>

## Prerequisites

- Node.js (v14.x or higher)
- npm (v6.x or higher)
- Ionic CLI (v6.x or higher)
- Angular CLI (v14.x or higher)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/myqu-mobile-app.git
   cd myqu-mobile-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   ionic serve
   ```

4. The application will be available at `http://localhost:4200/`

## Building for Production

### Web
```bash
ionic build --prod
```

### Android
```bash
ionic capacitor add android
ionic capacitor copy android
ionic capacitor open android
```

### iOS
```bash
ionic capacitor add ios
ionic capacitor copy ios
ionic capacitor open ios
```

## Project Structure

```
src/
├── app/
│   ├── components/       # Reusable UI components
│   ├── models/           # Data models and interfaces
│   ├── pages/            # Application pages/screens
│   ├── services/         # Services for data handling
│   └── utils/            # Utility functions and pipes
├── assets/
│   ├── i18n/             # Translation files
│   └── images/           # Images and icons
└── theme/                # Global styling and themes
```

## Internationalization

The app supports multiple languages through the ngx-translate library. Translation files are located in `src/assets/i18n/`.

To add a new language:
1. Create a new JSON file in the i18n directory (e.g., `fr.json`)
2. Add the language to the language service in `src/app/services/language/language.service.ts`

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Ionic Framework](https://ionicframework.com/)
- [Angular](https://angular.io/)
- [ngx-translate](https://github.com/ngx-translate/core)
