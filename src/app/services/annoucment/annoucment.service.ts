import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { computed } from '@angular/core';
import { Announcement, AnnouncementResponse } from 'src/app/models/app.models';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class AnnoucmentService {
  private apiUrl = environment.apiUrl;
  constructor(private http: HttpClient) {
    this.initializeAnnouncements();
  }

  private announcements = signal<Announcement[]>([]);

  // Public getter for the announcements, sorted by creation date (newest first)
  public sortedAnnouncements = computed(() => {
    const sortedAnnouncements = this.announcements().sort(
      (a, b) => new Date(b.created).getTime() - new Date(a.created).getTime()
    );
    return sortedAnnouncements;
  });
  private _loading = signal<boolean>(true);
  public loading = this._loading.asReadonly();

  // Initialize announcements by collecting all announcements from all courses
  private initializeAnnouncements() {
    const allAnnouncements: Announcement[] = [];
    this.getAnnouncements().subscribe((courses) => {
      courses.forEach((course) => {
        if (course.announcements && Array.isArray(course.announcements)) {
          // Map each announcement to include the courseId
          const courseAnnouncements = course.announcements.map(
            (announcement) => ({
              ...announcement,
              courseId: course.courseId,
            })
          );

          allAnnouncements.push(...courseAnnouncements);
        }
      });
    });
    this.announcements.set(allAnnouncements);
    this._loading.set(false);
  }

  private getAnnouncements() {
    return this.http.get<AnnouncementResponse[]>(
      `${this.apiUrl}/api/v2/blackboard/courses-with-announcements`
    );
  }
}
