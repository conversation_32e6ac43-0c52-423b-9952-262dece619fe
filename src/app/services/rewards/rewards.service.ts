import {Injectable, computed, resource, inject} from '@angular/core';
import {environment} from 'src/environments/environment';
import {ApiResponse, Reward} from 'src/app/models/app.models';
import {HttpClient} from "@angular/common/http";
import {lastValueFrom} from "rxjs";

@Injectable({providedIn: 'root'})
export class RewardsService {
  private apiUrl = environment.apiUrl;
  private httpClient = inject(HttpClient);

  private months = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  // Resource for fetching rewards data
  private rewardsResource = resource({
    loader: () =>
      lastValueFrom(
        this.httpClient.get<ApiResponse<Reward[]>>(`${this.apiUrl}/api/v2/rewards`)
      )
  });

  // Computed property that transforms the raw data and adds eligibility
  rewards = computed(() => {
    const data = this.rewardsResource.value();
    if (!data) return [];

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    return data.data.map((reward) => ({
      ...reward,
      isEligible:
        parseInt(reward.year) === currentYear &&
        this.months.indexOf(reward.month) >= currentMonth,
    }));
  });

  // Resource status properties for better UX
  readonly isLoading = computed(() => this.rewardsResource.isLoading());
  readonly error = computed(() => this.rewardsResource.error());
  readonly hasData = computed(() => !!this.rewardsResource.value());

  // Method to manually refresh the data
  refresh() {
    return this.rewardsResource.reload();
  }

}
