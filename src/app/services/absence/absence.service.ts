import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { Observable } from 'rxjs';
import { Absences, ApiResponse } from 'src/app/models/app.models';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root',
})
export class AbsenceService {
  private apiUrl = environment.apiUrl;

  constructor(private httpClient: HttpClient) {
    this.refreshAbsences();
  }

  absences = signal<Absences[]>([]);
  loading = signal<boolean>(true);

  /**
   * Get all absences for the current user
   * @returns Observable of absences array
   */
  private getAbsences(): Observable<ApiResponse<Absences[]>> {
    return this.httpClient.get<ApiResponse<Absences[]>>(
      `${this.apiUrl}/api/v2/absences-with-details`
    );
  }

  public refreshAbsences() {
    this.loading.set(true);
    this.getAbsences().subscribe({
      next: (res) => {
        this.absences.set(res.data);
        this.loading.set(false);
      },
    });
  }
}
