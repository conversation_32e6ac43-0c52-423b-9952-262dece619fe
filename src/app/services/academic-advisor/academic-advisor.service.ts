import { Injectable, signal } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { ApiResponse, AcademicAdvisor } from 'src/app/models/app.models';

@Injectable({
  providedIn: 'root',
})
export class AcademicAdvisorService {
  private apiUrl = environment.apiUrl;
  constructor(private httpClient: HttpClient) {
    this.getAdvisor().subscribe((res) => {
      this.advisor.set(res.data);
      this.loading.set(false);
    });
  }

  advisor = signal<AcademicAdvisor | null>(null);
  loading = signal<boolean>(true);
  private getAdvisor(): Observable<ApiResponse<AcademicAdvisor>> {
    return this.httpClient.get<ApiResponse<AcademicAdvisor>>(
      `${this.apiUrl}/api/v2/academic-advisor`
    );
  }
}
