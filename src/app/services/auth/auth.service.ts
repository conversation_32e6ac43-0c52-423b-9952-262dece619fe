import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of, throwError, delay } from 'rxjs';
import { User } from '../../models/app.models';
import { StorageService } from '../storage/storage.service';
import { ApiService } from '../api/api.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly tokenKey = 'auth_token';
  private readonly userKey = 'current_user';

  // Auth state using signals
  readonly isAuthenticated = signal<boolean>(false);
  readonly currentUser = signal<User | null>(null);

  constructor(
    private router: Router,
    private storageService: StorageService,
    private apiService: ApiService
  ) {
    this.initializeAuth();
  }

  private async initializeAuth(): Promise<void> {
    const token = await this.storageService.get(this.tokenKey);
    const user = await this.storageService.get(this.userKey);
    if (!token) return;
    this.isAuthenticated.set(true);
    if (user) this.currentUser.set(user);
    this.updateUser(); //always update user when app is opened
  }

  login(userToken: string) {
    this.storageService.set(this.tokenKey, userToken);
    this.isAuthenticated.set(true);
    this.updateUser();
  }

  async logout(): Promise<void> {
    await this.storageService.set(this.tokenKey, null);
    await this.storageService.set(this.userKey, null);
    this.isAuthenticated.set(false);
    this.currentUser.set(null);
    this.router.navigate(['/']);
  }

  getToken(): Promise<string | null> {
    return this.storageService.get(this.tokenKey);
  }

  private updateUser() {
    this.apiService.getCurrentUser().subscribe((user) => {
      if (user.success) {
        this.storageService.set(this.userKey, user.data);
        this.currentUser.set(user.data);
      }
    });
  }
}
