import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  User,
  Course,
  Grade,
  Announcement,
  Service,
  ApiResponse,
} from '../../models/app.models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // User endpoints
  getCurrentUser(): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(`${this.apiUrl}/api/v2/me`);
  }

  updateUserProfile(user: Partial<User>): Observable<User> {
    return this.http.put<User>(`${this.apiUrl}/user/profile`, user);
  }

  // Academic endpoints
  getCourses(): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/academic/courses`);
  }

  getGrades(): Observable<Grade[]> {
    return this.http.get<Grade[]>(`${this.apiUrl}/academic/grades`);
  }

  // Announcements endpoints
  getAnnouncements(): Observable<Announcement[]> {
    return this.http.get<Announcement[]>(`${this.apiUrl}/announcements`);
  }

  getAnnouncementById(id: string): Observable<Announcement> {
    return this.http.get<Announcement>(`${this.apiUrl}/announcements/${id}`);
  }

  // Services endpoints
  getServices(): Observable<Service[]> {
    return this.http.get<Service[]>(`${this.apiUrl}/services`);
  }
}
