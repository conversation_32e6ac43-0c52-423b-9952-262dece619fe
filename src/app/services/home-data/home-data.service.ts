import { Injectable, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import {
  HomePageData,
  NewsItem,
  Achievement,
  CalendarEvent,
  UniversityInfo,
  College,
  HomeAnnouncement
} from '../../models/home.models';

@Injectable({
  providedIn: 'root'
})
export class HomeDataService {

  private _loading = signal<boolean>(false);
  public loading = this._loading.asReadonly();

  constructor() {}

  // Mock data for development - replace with actual API calls
  getHomePageData(): Observable<HomePageData> {
    this._loading.set(true);

    const mockData: HomePageData = {
      announcements: this.getMockAnnouncements(),
      news: this.getMockNews(),
      achievements: this.getMockAchievements(),
      calendarEvents: this.getMockCalendarEvents(),
      universityInfo: this.getMockUniversityInfo(),
      colleges: this.getMockColleges()
    };

    // Simulate API delay
    setTimeout(() => this._loading.set(false), 1000);

    return of(mockData);
  }

  private getMockAnnouncements(): HomeAnnouncement[] {
    return [
      {
        id: '1',
        title: 'إعلان مهم: بدء التسجيل للفصل الدراسي الجديد',
        content: 'يسر جامعة القصيم أن تعلن عن بدء التسجيل للفصل الدراسي الجديد. يرجى من جميع الطلاب مراجعة النظام الأكاديمي.',
        date: '2024-01-15',
        author: 'عمادة القبول والتسجيل',
        category: 'أكاديمي',
        important: true,
        imageUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '2',
        title: 'تأجيل الامتحانات النهائية',
        content: 'تم تأجيل الامتحانات النهائية لمدة أسبوع واحد بسبب الظروف الجوية.',
        date: '2024-01-10',
        author: 'عمادة شؤون الطلاب',
        category: 'امتحانات',
        important: true,
        imageUrl: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '3',
        title: 'ورشة عمل حول البحث العلمي',
        content: 'تنظم الجامعة ورشة عمل حول منهجية البحث العلمي لطلاب الدراسات العليا.',
        date: '2024-01-08',
        author: 'عمادة البحث العلمي',
        category: 'ورش عمل',
        important: false,
        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '4',
        title: 'إعلان عن منح دراسية للطلاب المتفوقين',
        content: 'تعلن الجامعة عن توفر منح دراسية للطلاب المتفوقين أكاديمياً للعام الدراسي القادم.',
        date: '2024-01-12',
        author: 'عمادة شؤون الطلاب',
        category: 'منح دراسية',
        important: true,
        imageUrl: 'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=800&h=400&fit=crop&crop=center'
      }
    ];
  }

  private getMockNews(): NewsItem[] {
    return [
      {
        id: '1',
        title: 'جامعة القصيم تحصل على الاعتماد الأكاديمي الدولي',
        summary: 'حصلت جامعة القصيم على الاعتماد الأكاديمي الدولي من المؤسسة الأوروبية لضمان الجودة.',
        content: 'في إنجاز أكاديمي متميز، حصلت جامعة القصيم على الاعتماد الأكاديمي الدولي...',
        date: '2024-01-20',
        author: 'إدارة الإعلام',
        category: 'أخبار أكاديمية',
        imageUrl: 'https://images.unsplash.com/photo-1562774053-701939374585?w=800&h=400&fit=crop&crop=center',
        readTime: 3
      },
      {
        id: '2',
        title: 'افتتاح مختبر الذكاء الاصطناعي الجديد',
        summary: 'افتتحت الجامعة مختبراً حديثاً للذكاء الاصطناعي مجهز بأحدث التقنيات.',
        content: 'افتتح معالي مدير الجامعة مختبر الذكاء الاصطناعي الجديد...',
        date: '2024-01-18',
        author: 'كلية الحاسب الآلي',
        category: 'تقنية',
        imageUrl: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop&crop=center',
        readTime: 2
      },
      {
        id: '3',
        title: 'توقيع اتفاقية شراكة مع جامعة هارفارد',
        summary: 'وقعت جامعة القصيم اتفاقية شراكة أكاديمية مع جامعة هارفارد الأمريكية.',
        content: 'في خطوة تهدف إلى تعزيز التعاون الأكاديمي والبحثي...',
        date: '2024-01-15',
        author: 'العلاقات الدولية',
        category: 'شراكات',
        imageUrl: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=400&fit=crop&crop=center',
        readTime: 4
      },
      {
        id: '4',
        title: 'إطلاق برنامج التبادل الطلابي الدولي',
        summary: 'أطلقت الجامعة برنامجاً جديداً للتبادل الطلابي مع الجامعات العالمية المرموقة.',
        content: 'يهدف البرنامج إلى إتاحة الفرصة للطلاب للدراسة في جامعات عالمية...',
        date: '2024-01-22',
        author: 'مكتب التبادل الطلابي',
        category: 'برامج دولية',
        imageUrl: 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&h=400&fit=crop&crop=center',
        readTime: 5
      }
    ];
  }

  private getMockAchievements(): Achievement[] {
    return [
      {
        id: '1',
        title: 'المركز الأول في مسابقة البرمجة الوطنية',
        description: 'حقق فريق جامعة القصيم المركز الأول في مسابقة البرمجة الوطنية للجامعات السعودية.',
        date: '2024-01-22',
        category: 'تقنية',
        achievementType: 'academic',
        imageUrl: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '2',
        title: 'نشر 50 بحث علمي في مجلات عالمية',
        description: 'نجحت الجامعة في نشر 50 بحثاً علمياً في مجلات عالمية محكمة خلال العام الماضي.',
        date: '2024-01-20',
        category: 'بحث علمي',
        achievementType: 'research',
        imageUrl: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '3',
        title: 'بطولة الجامعات السعودية لكرة القدم',
        description: 'فاز فريق جامعة القصيم لكرة القدم ببطولة الجامعات السعودية.',
        date: '2024-01-18',
        category: 'رياضة',
        achievementType: 'sports',
        imageUrl: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '4',
        title: 'جائزة التميز في خدمة المجتمع',
        description: 'حصلت الجامعة على جائزة التميز في خدمة المجتمع من وزارة التعليم.',
        date: '2024-01-16',
        category: 'خدمة مجتمعية',
        achievementType: 'community',
        imageUrl: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '5',
        title: 'المركز الثاني في مهرجان الثقافة والفنون',
        description: 'حقق طلاب الجامعة المركز الثاني في مهرجان الثقافة والفنون على مستوى المملكة.',
        date: '2024-01-14',
        category: 'ثقافة وفنون',
        achievementType: 'cultural',
        imageUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=400&fit=crop&crop=center'
      }
    ];
  }

  private getMockCalendarEvents(): CalendarEvent[] {
    return [
      {
        id: '1',
        title: 'بداية الفصل الدراسي الثاني',
        description: 'بداية الدراسة للفصل الدراسي الثاني للعام الجامعي 1445-1446هـ',
        startDate: '2024-02-04',
        type: 'event',
        isImportant: true
      },
      {
        id: '2',
        title: 'فترة التسجيل المبكر',
        description: 'فترة التسجيل المبكر للطلاب المستمرين',
        startDate: '2024-01-28',
        endDate: '2024-02-01',
        type: 'registration',
        isImportant: true
      },
      {
        id: '3',
        title: 'الامتحانات النهائية',
        description: 'فترة الامتحانات النهائية للفصل الدراسي الأول',
        startDate: '2024-01-14',
        endDate: '2024-01-25',
        type: 'exam',
        isImportant: true
      },
      {
        id: '4',
        title: 'إجازة منتصف الفصل',
        description: 'إجازة منتصف الفصل الدراسي الثاني',
        startDate: '2024-03-10',
        endDate: '2024-03-14',
        type: 'holiday',
        isImportant: false
      }
    ];
  }

  private getMockUniversityInfo(): UniversityInfo[] {
    return [
      {
        id: '1',
        title: 'رؤية الجامعة',
        description: 'أن تكون جامعة القصيم جامعة رائدة ومتميزة في التعليم والبحث العلمي وخدمة المجتمع محلياً وإقليمياً.',
        type: 'vision',
        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '2',
        title: 'رسالة الجامعة',
        description: 'تقديم تعليم عالي الجودة وإجراء بحوث علمية متميزة وخدمة المجتمع من خلال بيئة أكاديمية محفزة.',
        type: 'mission',
        imageUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '3',
        title: 'عن الجامعة',
        description: 'تأسست جامعة القصيم عام 1423هـ وتضم 38 كلية موزعة على مختلف محافظات منطقة القصيم.',
        type: 'facility',
        imageUrl: 'https://images.unsplash.com/photo-1562774053-701939374585?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '4',
        title: 'الحرم الجامعي الذكي',
        description: 'تطوير الحرم الجامعي ليصبح حرماً ذكياً مزوداً بأحدث التقنيات والمرافق الحديثة.',
        type: 'facility',
        imageUrl: 'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=800&h=400&fit=crop&crop=center'
      }
    ];
  }

  private getMockColleges(): College[] {
    return [
      {
        id: '1',
        name: 'كلية الطب',
        description: 'تقدم برامج البكالوريوس والدراسات العليا في الطب والعلوم الطبية',
        dean: 'د. أحمد محمد السالم',
        establishedYear: 1424,
        studentsCount: 1200,
        programsCount: 8,
        imageUrl: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '2',
        name: 'كلية الهندسة',
        description: 'تضم أقسام الهندسة المدنية والكهربائية والميكانيكية والحاسب الآلي',
        dean: 'د. محمد عبدالله الخالد',
        establishedYear: 1425,
        studentsCount: 2500,
        programsCount: 12,
        imageUrl: 'https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '3',
        name: 'كلية إدارة الأعمال',
        description: 'تقدم برامج في إدارة الأعمال والمحاسبة والتسويق والموارد البشرية',
        dean: 'د. سارة أحمد المطيري',
        establishedYear: 1426,
        studentsCount: 1800,
        programsCount: 10,
        imageUrl: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '4',
        name: 'كلية علوم الحاسب الآلي',
        description: 'تقدم برامج متطورة في علوم الحاسب والذكاء الاصطناعي وأمن المعلومات',
        dean: 'د. خالد عبدالرحمن النصار',
        establishedYear: 1427,
        studentsCount: 1500,
        programsCount: 6,
        imageUrl: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop&crop=center'
      },
      {
        id: '5',
        name: 'كلية التربية',
        description: 'تعد المعلمين والمعلمات المؤهلين لجميع المراحل التعليمية',
        dean: 'د. فاطمة محمد الحربي',
        establishedYear: 1423,
        studentsCount: 3200,
        programsCount: 15,
        imageUrl: 'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?w=800&h=400&fit=crop&crop=center'
      }
    ];
  }
}
