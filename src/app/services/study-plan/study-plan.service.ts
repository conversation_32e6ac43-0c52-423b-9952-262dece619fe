import { computed, Injectable, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { StudyPlan } from '../../pages/study-plan/study-plan.interface';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class StudyPlanService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {
    this.refreshStudyPlan();
  }

  private _studyPlan = signal<StudyPlan | null>(null);
  studyPlan = computed(() => this._studyPlan());
  loading = signal<boolean>(true);

  private getStudyPlan(): Observable<StudyPlan> {
    return this.http.get<StudyPlan>(`${this.apiUrl}/api/v2/student-plan`);
  }

  public refreshStudyPlan() {
    this.loading.set(true);
    return this.getStudyPlan().subscribe((studyPlan) => {
      this._studyPlan.set(studyPlan);
      console.log(this.studyPlan());

      this.loading.set(false);
    });
  }
}
