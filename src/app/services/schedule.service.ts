import { Injectable, signal, Signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ScheduleApiResponse, ScheduleData } from '../models/schedule.model';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ScheduleService {
  private apiUrl = environment.apiUrl;

  private _scheduleData = signal<ScheduleData | null>(null);
  scheduleData = this._scheduleData.asReadonly();

  constructor(private http: HttpClient) {
    this.getScheduleData().subscribe((data) => {
      this._scheduleData.set(data.data);
    });
  }

  private getScheduleData() {
    return this.http.get<ScheduleApiResponse>(
      `${this.apiUrl}/api/v2/time-table`
    );
  }
}
