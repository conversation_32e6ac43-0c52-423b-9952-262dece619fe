import { Injectable, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import {
  AcademicTransactionsData,
  ApiResponse,
  TransactionCourse,
} from '../models/app.models';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AcademicTransactionsService {
  private apiUrl = environment.apiUrl;

  private _academicTransactionsData = signal<AcademicTransactionsData[] | null>(
    null
  );
  academicTransactionsData = this._academicTransactionsData.asReadonly();

  constructor(private http: HttpClient) {
    this.getAcademicTransactionsData().subscribe((data) => {
      const sortedData = data.data.sort((a, b) => {
        const yearA = parseInt(a.semester.substring(0, 2));
        const semTypeA = parseInt(a.semester.substring(2, 3));
        const yearB = parseInt(b.semester.substring(0, 2));
        const semTypeB = parseInt(b.semester.substring(2, 3));

        if (yearA !== yearB) {
          return yearA - yearB;
        }
        // Semester type order: 1 (Fall) < 2 (Spring) < 3 or 5 (Summer)
        // Adjust if semester types have a different meaning (e.g. 3 used for summer instead of 5)
        return semTypeA - semTypeB;
      });
      this._academicTransactionsData.set(sortedData);
    });
  }

  private getAcademicTransactionsData() {
    return this.http.get<ApiResponse<AcademicTransactionsData[]>>(
      `${this.apiUrl}/api/v2/student-academic-transactions`
    );
  }
}
