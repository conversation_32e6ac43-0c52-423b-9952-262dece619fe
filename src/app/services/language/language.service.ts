import { Injectable, signal } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { BehaviorSubject } from 'rxjs';
import { StorageService } from '../storage/storage.service';
import { TranslateService } from '@ngx-translate/core';

export interface Language {
  code: 'ar' | 'en';
  name: string;
  direction: 'rtl' | 'ltr';
  nativeName: string;
}

export const SUPPORTED_LANGUAGES: Language[] = [
  {
    code: 'ar',
    name: 'Arabic',
    direction: 'rtl',
    nativeName: 'العربية'
  },
  {
    code: 'en',
    name: 'English',
    direction: 'ltr',
    nativeName: 'English'
  }
];

const LANGUAGE_KEY = 'preferred_language';

@Injectable({
  providedIn: 'root',
  deps: [TranslateService],

})
export class LanguageService {
  private document = inject(DOCUMENT);
  private languageSubject = new BehaviorSubject<Language>(this.getDefaultLanguage());

  readonly currentLanguage = toSignal(this.languageSubject, {
    initialValue: this.getDefaultLanguage()
  });

  constructor(
    private storageService: StorageService,
    private translateService: TranslateService
  ) {
    this.initializeLanguage();
  }

  private getDefaultLanguage(): Language {
    // Default to Arabic
    return SUPPORTED_LANGUAGES[0];
  }

  private async getInitialLanguage(): Promise<Language> {
    // Check storage first
    const storedLang = await this.storageService.get(LANGUAGE_KEY);
    if (storedLang) {
      const lang = SUPPORTED_LANGUAGES.find(l => l.code === storedLang);
      if (lang) return lang;
    }

    // Check device language
    const browserLang = navigator.language.split('-')[0];
    const deviceLang = SUPPORTED_LANGUAGES.find(l => l.code === browserLang);
    if (deviceLang) return deviceLang;

    // Default to Arabic
    return SUPPORTED_LANGUAGES[0];
  }

  private async initializeLanguage(): Promise<void> {
    const language = await this.getInitialLanguage();
    this.applyLanguage(language).then(()=>{
      this.translateService.addLangs(['ar', 'en']);
      this.translateService.setDefaultLang(language.code);
      this.translateService.use(language.code);
    });
  }

  private async applyLanguage(language: Language): Promise<void> {
    // Update HTML root element
    const htmlElement = this.document.documentElement;
    htmlElement.setAttribute('dir', language.direction);
    htmlElement.setAttribute('lang', language.code);

    // Store preference
    await this.storageService.set(LANGUAGE_KEY, language.code);

    // Set language in translate service
    this.translateService.use(language.code);

    // Emit new language
    this.languageSubject.next(language);
  }

  async switchLanguage(languageCode: 'ar' | 'en'): Promise<void> {
    const newLanguage = SUPPORTED_LANGUAGES.find(l => l.code === languageCode);
    if (newLanguage && newLanguage.code !== this.currentLanguage().code) {
      await this.applyLanguage(newLanguage);
    }
  }

  getSupportedLanguages(): Language[] {
    return SUPPORTED_LANGUAGES;
  }
}
