import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ModalController } from '@ionic/angular';
import { CourseDetail } from '../../pages/study-plan/study-plan.interface'; // Adjust path as needed
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-course-detail-modal',
  templateUrl: './course-detail-modal.component.html',
  styleUrls: ['./course-detail-modal.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, TranslateModule],
})
export class CourseDetailModalComponent {
  @Input() course?: CourseDetail;
  private modalCtrl = inject(ModalController);

  constructor() {}

  dismiss() {
    this.modalCtrl.dismiss();
  }
}
