<ion-header>
  <ion-toolbar>
    <ion-title>{{ course?.code }} - {{ course?.title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="dismiss()">
        <ion-icon slot="icon-only" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="course">
    <ion-list lines="none">
      <ion-item>
        <ion-label>{{ 'STUDY_PLAN.CODE' | translate }}:</ion-label>
        <ion-text slot="end">{{ course.code }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>{{ 'STUDY_PLAN.TITLE' | translate }}:</ion-label>
        <ion-text slot="end">{{ course.title }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>{{ 'STUDY_PLAN.HOURS' | translate }}:</ion-label>
        <ion-text slot="end">{{ course.hours }}</ion-text>
      </ion-item>
      <ion-item>
        <ion-label>{{ 'STUDY_PLAN.STATUS' | translate }}:</ion-label>
        <ion-text slot="end">{{ 'STUDY_PLAN.STATUS_' + course.status.toUpperCase() | translate }}</ion-text>
      </ion-item>
      <!-- Add more details as needed -->
      <!-- Example for Arabic/English names if you add them later -->
      <!--
      <ion-item *ngIf="course.name_ar">
        <ion-label>Arabic Name:</ion-label>
        <ion-text slot="end">{{ course.name_ar }}</ion-text>
      </ion-item>
      <ion-item *ngIf="course.name_en">
        <ion-label>English Name:</ion-label>
        <ion-text slot="end">{{ course.name_en }}</ion-text>
      </ion-item>
      -->
    </ion-list>
  </div>
  <div *ngIf="!course">
    <p>{{ 'STUDY_PLAN.NO_COURSE_DATA' | translate }}</p>
  </div>
</ion-content>