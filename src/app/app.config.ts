import {ApplicationConfig, importProvidersFrom} from '@angular/core';
import {
  PreloadAllModules, provideRouter, RouteReuseStrategy, RouterModule,
  withComponentInputBinding, withPreloading, withViewTransitions
} from '@angular/router';
import {routes} from './app.routes';
import {IonApp, IonicRouteStrategy, provideIonicAngular} from '@ionic/angular/standalone';
import {provideHttpClient, withInterceptors} from '@angular/common/http';
import {HttpClient} from '@angular/common/http';
import {authInterceptor} from './utils/auth.interceptor';
import {provideAnimations} from '@angular/platform-browser/animations';
import {provideTranslateService, TranslateLoader} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {IonicModule} from "@ionic/angular";
import {IonicStorageModule} from "@ionic/storage-angular";
import {Drivers} from "@ionic/storage";
import ar from '@angular/common/locales/ar';
import {registerLocaleData} from "@angular/common";

// AoT requires an exported function for factories
export function httpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

// register ar locale from angular
registerLocaleData(ar);


export const appConfig: ApplicationConfig = {
  providers: [
    {provide: RouteReuseStrategy, useClass: IonicRouteStrategy},
    provideIonicAngular({
      mode: 'ios',
    }),
    importProvidersFrom(IonicModule.forRoot()),
    provideRouter(
      routes,
      withPreloading(PreloadAllModules),
      withComponentInputBinding()
    ),
    importProvidersFrom(
      IonicStorageModule.forRoot({
        name: '__myqu_app',
        driverOrder: [Drivers.LocalStorage],
      }),
    ),
    provideHttpClient(withInterceptors([authInterceptor])),
    provideAnimations(),
    provideTranslateService({
      defaultLanguage: 'ar',
      useDefaultLang: true,
      loader: {
        provide: TranslateLoader,
        useFactory: httpLoaderFactory,
        deps: [HttpClient],
      },
    }),
  ],
} satisfies ApplicationConfig;
