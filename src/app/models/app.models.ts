// User model

export interface User {
  id: number;
  name: string;
  name_en: string;
  gender: string;
  student_id: string;
  national_id: string;
  email: string;
  'phone-number': string;
  'total-absence-hours': number;
  dob: string;
  academic: Academic;
  major: Major;
  faculty: Faculty;
}

// Academic models
export interface Course {
  id: string;
  code: string;
  name: string;
  instructor: string;
  credits: number;
  schedule: Schedule[];
}

export interface Schedule {
  day:
    | 'sunday'
    | 'monday'
    | 'tuesday'
    | 'wednesday'
    | 'thursday'
    | 'friday'
    | 'saturday';
  startTime: string;
  endTime: string;
  location: string;
}

export interface Grade {
  courseId: string;
  courseName: string;
  grade: string;
  points: number;
  semester: string;
}

// Announcement model
export interface Announcement {
  id: string;
  title: string;
  content: string;
  date: string;
  author: string;
  category: string;
  important: boolean;
}

// Service model
export interface Service {
  id: string;
  name: string;
  description: string;
  icon: string;
  url: string;
  category: string;
}

export interface Reward {
  student_id: string;
  year: string;
  month: string;
  reward_amount: string;
  isEligible: boolean;
}

export interface AcademicAdvisor {
  instructor_name: string;
  work_email: string;
}

export interface Absences {
  cource_code: string;
  cource_name: string;
  cource_name_en: string;
  absences: {
    absence_day: string;
    absence_excused: string;
    absence_late: string;
    absence_date: string;
    start_time: string;
    end_time: string;
  }[];
  absence_all_percent: string;
  absence_excused_percent: string;
}

export interface TransactionCourse {
  course_no: string;
  status_code: string;
  letter_grade: string | null;
  letter_grade_s: string | null;
  course_code: string;
  course_name: string;
  course_name_s: string;
  crd_hrs: string;
  quality_points: string | null;
}

export interface AcademicTransactionsData {
  semester: string;
  courses: TransactionCourse[];
}

export interface Availability {
  duration?: {
    type: string;
    start: string;
    end: string | null;
  };
  available?: string;
}

export interface Announcement {
  id: string;
  title: string;
  body?: string;
  creator: string;
  draft: boolean;
  availability: Availability;
  created: string;
  modified: string;
  position: number;
  courseId: string;
}

export interface AnnouncementResponse {
  id: string;
  userId: string;
  courseId: string;
  created: string;
  modified: string;
  announcements: Announcement[];
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
