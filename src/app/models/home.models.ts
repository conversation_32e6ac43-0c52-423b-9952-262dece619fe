// Home page data models

export interface HomeAnnouncement {
  id: string;
  title: string;
  content: string;
  date: string;
  author: string;
  category: string;
  important: boolean;
  imageUrl?: string;
}

export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  content: string;
  date: string;
  author: string;
  category: string;
  imageUrl?: string;
  readTime?: number; // in minutes
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  date: string;
  category: string;
  imageUrl?: string;
  achievementType: 'academic' | 'research' | 'sports' | 'cultural' | 'community';
}

export interface CalendarEvent {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate?: string;
  type: 'exam' | 'registration' | 'holiday' | 'event' | 'deadline';
  location?: string;
  isImportant: boolean;
}

export interface UniversityInfo {
  id: string;
  title: string;
  description: string;
  type: 'vision' | 'mission' | 'college' | 'program' | 'facility';
  imageUrl?: string;
  link?: string;
}

export interface College {
  id: string;
  name: string;
  description: string;
  dean: string;
  establishedYear: number;
  studentsCount: number;
  programsCount: number;
  imageUrl?: string;
  website?: string;
}

export interface HomePageData {
  announcements: HomeAnnouncement[];
  news: NewsItem[];
  achievements: Achievement[];
  calendarEvents: CalendarEvent[];
  universityInfo: UniversityInfo[];
  colleges: College[];
}

export interface SplideOptions {
  type?: 'loop' | 'slide' | 'fade';
  perPage?: number;
  perMove?: number;
  gap?: string | number;
  autoplay?: boolean;
  interval?: number;
  pauseOnHover?: boolean;
  pauseOnFocus?: boolean;
  resetProgress?: boolean;
  arrows?: boolean;
  pagination?: boolean;
  direction?: 'ltr' | 'rtl';
  breakpoints?: {
    [key: number]: Partial<SplideOptions>;
  };
}
