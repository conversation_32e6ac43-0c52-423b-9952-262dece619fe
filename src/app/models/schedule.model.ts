export interface TimeSlot {
  original: {
    start: string;
    end: string;
  };
  formatted: {
    start: string;
    end: string;
  };
}

export interface DayTime {
  day: {
    number: number;
    name: string;
  };
  time_slot: TimeSlot;
}

export interface Course {
  campus_no: string;
  campus_desc: string;
  course_no: string;
  course_name: string;
  course_edition: string;
  activity_code: string;
  activity_desc: string;
  section_seq: string;
  section: string;
  times: DayTime[];
  // UI specific properties
  color?: string;
}

export interface ScheduleData {
  student_id: string;
  student_name: string | null;
  semester: string;
  'time-table': Course[];
}

export interface ScheduleApiResponse {
  success: boolean;
  data: ScheduleData;
  message: string;
}

