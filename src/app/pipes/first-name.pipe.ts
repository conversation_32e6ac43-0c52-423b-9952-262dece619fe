import { Pipe, PipeTransform } from '@angular/core';

/**
 * Extracts the first name from a full name
 * Usage: {{ fullName | firstName }}
 */
@Pipe({
  name: 'firstName',
  standalone: true
})
export class FirstNamePipe implements PipeTransform {
  transform(fullName: string): string {
    if (!fullName) return '';
    
    // Split the name by spaces and return the first part
    return fullName.split(' ')[0];
  }
}
