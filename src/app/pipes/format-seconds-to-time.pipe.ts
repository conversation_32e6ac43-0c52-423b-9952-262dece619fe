import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatSecondsToTime',
  standalone: true,
})
export class FormatSecondsToTimePipe implements PipeTransform {
  transform(value: string | number): string {
    const totalSeconds = parseInt(value as string, 10);

    if (isNaN(totalSeconds) || totalSeconds < 0) {
      return '';
    }

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    // Always use 24-hour format
    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = minutes.toString().padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}`;
  }
}
