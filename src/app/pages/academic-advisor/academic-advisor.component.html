<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/academic"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'ADVISOR.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">{{ 'ADVISOR.TITLE' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <div *ngIf="loading()" class="loading-state">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'ADVISOR.LOADING' | translate }}</p>
  </div>

  <div *ngIf="!loading() && advisor()" class="advisor-container">
    <!-- Intro section -->
    <div class="section-header">
      <div class="section-title-container">
        <h2 class="section-title">{{ 'ADVISOR.YOUR_ADVISOR' | translate }}</h2>
        <div class="section-underline"></div>
      </div>
    </div>

    <p class="description">
      {{ 'ADVISOR.DESCRIPTION' | translate }}
    </p>

    <!-- Advisor Profile Card -->
    <div class="advisor-card">
      <div class="advisor-profile">
        <div class="advisor-info">
          <h3 class="advisor-name">{{ advisor()?.instructor_name }}</h3>
          <p class="advisor-title">{{ 'ADVISOR.ACADEMIC_ADVISOR' | translate }}</p>
        </div>
      </div>

        <ion-button expand="block" [href]="'mailto:' + advisor()?.work_email" class="ion-margin-bottom">
          <hugeicons-icon [icon]="MailIcon" size="18" [strokeWidth]="1.5"></hugeicons-icon>
          &nbsp;
          <span style="font-size: 14px">{{ 'ADVISOR.SEND_EMAIL' | translate }}</span>
        </ion-button>


      <div class="contact-details">
        <div class="detail-item">
          <hugeicons-icon [icon]="MailIcon" size="18" [strokeWidth]="1.5"></hugeicons-icon>
          <span>{{ advisor()?.work_email }}</span>
        </div>


      </div>

      <div class="advisor-shimmer"></div>
    </div>

    <!-- FAQ Section -->
    <div class="section-header">
      <div class="section-title-container">
        <h2 class="section-title">{{ 'ADVISOR.FAQ' | translate }}</h2>
        <div class="section-underline"></div>
      </div>
    </div>

    <div class="faq-container">
      <div class="faq-item" *ngFor="let faq of faqs; let i = index" (click)="toggleFaq(i)">
        <div class="faq-question">
          <span>{{ faq.question | translate }}</span>
          <hugeicons-icon [icon]="faq.expanded ? ArrowUp01Icon : ArrowDown01Icon" size="18"
                          [strokeWidth]="1.5"></hugeicons-icon>
        </div>
        <div class="faq-answer" [class.expanded]="faq.expanded">
          <p>{{ faq.answer |translate }}</p>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="!loading() && !advisor()" class="empty-state">
    <hugeicons-icon [icon]="FileNotFoundIcon" size="48" [strokeWidth]="1.5"></hugeicons-icon>
    <h3>{{ 'ADVISOR.NO_ADVISOR_TITLE' | translate }}</h3>
    <p>{{ 'ADVISOR.NO_ADVISOR_MESSAGE' | translate }}</p>
  </div>
</ion-content>
