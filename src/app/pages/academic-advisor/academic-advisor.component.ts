import { Component, OnInit, Signal } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, finalize } from 'rxjs';
import { AcademicAdvisorService } from '../../services/academic-advisor/academic-advisor.service';
import { AcademicAdvisor } from 'src/app/models/app.models';
import { HugeiconsIconComponent } from '@hugeicons/angular';
import {
  ArrowDown01Icon,
  ArrowUp01Icon,
  Building01Icon,
  CalendarIcon,
  Clock05StrokeRounded,
  FileNotFoundIcon,
  MailIcon,
  UserIcon,
} from '@hugeicons-pro/core-stroke-rounded';
import { ToastController } from '@ionic/angular';

@Component({
  selector: 'app-academic-advisor',
  templateUrl: './academic-advisor.component.html',
  styleUrls: ['./academic-advisor.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, TranslateModule, HugeiconsIconComponent],
})
export class AcademicAdvisorComponent implements OnInit {
  constructor(
    public academicAdvisorService: AcademicAdvisorService,
    private toastController: ToastController,
    private translate: TranslateService
  ) {}

  advisor!: Signal<AcademicAdvisor | null>;
  loading!: Signal<boolean>;

  ngOnInit() {
    this.advisor = this.academicAdvisorService.advisor;
    this.loading = this.academicAdvisorService.loading;
  }

  // تحديث بيانات الأسئلة الشائعة لاستخدام مفاتيح الترجمة
  faqs = [
    {
      question: 'ADVISOR.FAQ_QUESTION_1',
      answer: 'ADVISOR.FAQ_ANSWER_1',
      expanded: false,
    },
    {
      question: 'ADVISOR.FAQ_QUESTION_2',
      answer: 'ADVISOR.FAQ_ANSWER_2',
      expanded: false,
    },
    {
      question: 'ADVISOR.FAQ_QUESTION_3',
      answer: 'ADVISOR.FAQ_ANSWER_3',
      expanded: false,
    },
    {
      question: 'ADVISOR.FAQ_QUESTION_4',
      answer: 'ADVISOR.FAQ_ANSWER_4',
      expanded: false,
    },
  ];

  // Method to toggle FAQ expansion
  toggleFaq(index: number) {
    this.faqs[index].expanded = !this.faqs[index].expanded;
  }

  scheduleMeeting() {
    this.toastController
      .create({
        message: this.translate.instant('common.coming_soon'),
        duration: 2000,
        position: 'top',
        mode: 'ios',
        color: 'primary',
        cssClass: 'toast-message',
      })
      .then((toast) => toast.present());
  }

  protected readonly ArrowUp01Icon = ArrowUp01Icon;
  protected readonly ArrowDown01Icon = ArrowDown01Icon;
  protected readonly FileNotFoundIcon = FileNotFoundIcon;
  protected readonly UserIcon = UserIcon;
  protected readonly MailIcon = MailIcon;
  protected readonly CalendarIcon = CalendarIcon;
  protected readonly Building01Icon = Building01Icon;
  protected readonly Clock05StrokeRounded = Clock05StrokeRounded;
}
