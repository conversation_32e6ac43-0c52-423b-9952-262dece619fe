// ==================
// General Styles
// ==================
ion-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-bottom: var(--ion-safe-area-bottom, 0px);
}

// ==================
// Section Header
// ==================
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 4px 4px;
  position: relative;
}

.section-title-container {
  position: relative;
  padding-bottom: 8px;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1.2;
  display: inline-block;
  position: relative;
}

.section-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(90deg,
    var(--ion-color-primary) 0%,
    var(--ion-color-primary-shade) 70%,
    rgba(var(--ion-color-primary-rgb), 0.5) 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

// Description text
.description {
  margin: 0 0 24px 0;
  line-height: 1.6;
  font-size: 0.95rem;
}

// ==================
// Advisor Container
// ==================
.advisor-container {
  padding: 16px 0;
  position: relative;
}

// ==================
// Advisor Card
// ==================
.advisor-card {
  background-color: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }
}

.advisor-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  z-index: 1;
  transform: translateX(-100%);
  animation: shimmer-wave 4s infinite;
  pointer-events: none;
}

@keyframes shimmer-wave {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.advisor-profile {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.advisor-avatar {
  width: 80px;
  height: 80px;
  background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;

  hugeicons-icon {
    color: var(--ion-color-primary);
  }
}

.advisor-info {
  flex: 1;
}

.advisor-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: var(--ion-text-color);
  line-height: 1.3;
}

.advisor-title {
  font-size: 1rem;
  color: var(--ion-text-color);
  margin: 0;
}

// ==================
// Contact Options
// ==================
.contact-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.contact-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:active {
    transform: scale(0.97);
  }

  &.email-button {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
    flex: 1;

    &:hover {
      background-color: var(--ion-color-primary-shade);
    }
  }

  &.schedule-button {
    background-color: rgba(var(--ion-color-primary-rgb), 0.15);
    color: var(--ion-color-primary);
    flex: 1;

    &:hover {
      background-color: rgba(var(--ion-color-primary-rgb), 0.25);
    }
  }
}

// ==================
// Contact Details
// ==================
.contact-details {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  hugeicons-icon {
    color: var(--ion-color-primary);
    flex-shrink: 0;
  }

  span {
    font-size: 0.95rem;
    color: var(--ion-text-color);
    word-break: break-word;
  }
}

// ==================
// FAQ Section
// ==================
.faq-container {
  margin-bottom: 24px;
}

.faq-item {
  background-color: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;

  &:hover {
  }
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  color: var(--ion-text-color);

  hugeicons-icon {
    color: var(--ion-color-medium);
    transition: transform 0.3s ease;
  }
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0 16px;

  p {
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.6;
  }

  &.expanded {
    max-height: 300px; // Adjust based on content
    padding: 0 16px 16px;
  }
}

// ==================
// Loading & Empty States
// ==================
.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 24px;
  text-align: center;

  hugeicons-icon {
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ion-text-color);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 0.95rem;
    color: var(--ion-color-medium);
    max-width: 300px;
    margin: 0 auto;
  }
}

.loading-state {
  ion-spinner {
    margin-bottom: 16px;
    width: 42px;
    height: 42px;
  }
}

// ==================
// Responsive Adjustments
// ==================
@media (min-width: 768px) {
  .advisor-card {
    padding: 32px;
    border-radius: 24px;
  }

  .advisor-profile {
    gap: 24px;
  }

  .advisor-avatar {
    width: 100px;
    height: 100px;
  }

  .advisor-name {
    font-size: 1.8rem;
  }

  .contact-options {
    gap: 16px;
  }

  .contact-button {
    padding: 14px 24px;
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .faq-container {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// ==================
// Dark Mode Adjustments
// ==================
@media (prefers-color-scheme: dark) {
  .advisor-card, .faq-item {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .contact-details {
    background-color: rgba(255, 255, 255, 0.08);
  }

  .advisor-avatar {
    background-color: rgba(var(--ion-color-primary-rgb), 0.2);
  }

  .contact-button {
    &.schedule-button {
      background-color: rgba(var(--ion-color-primary-rgb), 0.25);

      &:hover {
        background-color: rgba(var(--ion-color-primary-rgb), 0.35);
      }
    }
  }

  .advisor-shimmer {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0) 100%
    );
  }
}
