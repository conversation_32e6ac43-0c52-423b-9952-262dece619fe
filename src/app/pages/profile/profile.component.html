<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/more"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'PROFILE.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <!-- Profile Header Section -->
  <div class="ion-padding ion-text-center profile-header">
    <ion-avatar class="profile-avatar">
      <img alt="Profile picture" src="https://ionicframework.com/docs/img/demos/avatar.svg"/>
    </ion-avatar>
    <ion-text>
      <h1>{{ authService.currentUser()?.name || ('PROFILE.USER_NAME' | translate) }}</h1>
    </ion-text>
    <ion-text color="medium">
      <p>{{ authService.currentUser()?.email || '<EMAIL>' }}</p>
    </ion-text>
  </div>

  <!-- Personal Information -->
  <ion-list>
    <ion-item-group>
      <ion-item-divider>
        <ion-label>{{ 'PROFILE.PERSONAL_INFO' | translate }}</ion-label>
      </ion-item-divider>

      <ion-item>
        <hugeicons-icon [icon]="UserIcon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.FULL_NAME' | translate }}</h3>
          <p>{{ authService.currentUser()?.name || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="UserIcon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.FULL_NAME_EN' | translate }}</h3>
          <p>{{ authService.currentUser()?.name_en || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="IdIcon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.STUDENT_ID' | translate }}</h3>
          <p>{{ authService.currentUser()?.student_id || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="IdIcon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.NATIONAL_ID' | translate }}</h3>
          <p>{{ authService.currentUser()?.national_id || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="Mail01Icon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.EMAIL' | translate }}</h3>
          <p>{{ authService.currentUser()?.email || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="Call02Icon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.PHONE_NUMBER' | translate }}</h3>
          <p>{{ authService.currentUser()?.['phone-number'] || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="Calendar01Icon" slot="start" size="24" [strokeWidth]="1.5"
                        color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.DATE_OF_BIRTH' | translate }}</h3>
          <p>{{ formatDate(authService.currentUser()?.dob) || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="UserGroupIcon" slot="start" size="24" [strokeWidth]="1.5"
                        color="primary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.GENDER' | translate }}</h3>
          <p>{{ getGenderDisplay(authService.currentUser()?.gender) || 'N/A' }}</p>
        </ion-label>
      </ion-item>
    </ion-item-group>
  </ion-list>

  <!-- Academic Information -->
  <ion-list>
    <ion-item-group>
      <ion-item-divider>
        <ion-label>{{ 'PROFILE.ACADEMIC_INFO' | translate }}</ion-label>
      </ion-item-divider>

      <ion-item>
        <hugeicons-icon [icon]="BuildingIcon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.FACULTY' | translate }}</h3>
          <p>{{ authService.currentUser()?.faculty?.name || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="Book02Icon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.MAJOR' | translate }}</h3>
          <p>{{ authService.currentUser()?.major?.name || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="TrophyIcon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.CUMULATIVE_GPA' | translate }}</h3>
          <p>{{ authService.currentUser()?.academic?.cumulativeGpa || 'N/A' }}</p>
        </ion-label>
        <ion-badge slot="end" color="primary">
          {{ authService.currentUser()?.academic?.cumulativeGpa || '0.00' }}
        </ion-badge>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="TrophyIcon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.SEMESTER_GPA' | translate }}</h3>
          <p>{{ 'PROFILE.CURRENT_SEMESTER_GPA' | translate }}</p>
        </ion-label>
        <ion-badge slot="end" color="tertiary">
          {{ authService.currentUser()?.academic?.semesterGpa || '0.00' }}
        </ion-badge>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="InformationCircleStrokeRounded" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.EARNED_HOURS' | translate }}</h3>
          <p>{{ 'PROFILE.COMPLETED_CREDIT_HOURS' | translate }}</p>
        </ion-label>
        <ion-note slot="end">
          {{ authService.currentUser()?.academic?.totalEarnedHours || '0' }}
          / {{ authService.currentUser()?.academic?.totalPlanHours || '0' }}
        </ion-note>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="GraduationCapIcon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.ACADEMIC_STATUS' | translate }}</h3>
          <p>{{ getAcademicStatusDisplay(authService.currentUser()?.academic?.academicStatus) || 'N/A' }}</p>
        </ion-label>
      </ion-item>

      <ion-item>
        <hugeicons-icon [icon]="Calendar01Icon" slot="start" size="24" [strokeWidth]="1.5"
                        color="secondary"></hugeicons-icon>
        <ion-label>
          <h3>{{ 'PROFILE.EXPECTED_GRADUATION' | translate }}</h3>
          <p>{{ authService.currentUser()?.academic?.expectedGraduationSemester || 'N/A' }}</p>
        </ion-label>
      </ion-item>
    </ion-item-group>
  </ion-list>

  <!-- Action Buttons -->
  <div class="ion-padding">
    <ion-button expand="block" color="danger" (click)="logout()">
      <hugeicons-icon [icon]="Logout01Icon" slot="start" size="20" [strokeWidth]="1.5"/>
      &nbsp;
      {{ 'PROFILE.LOGOUT' | translate }}
    </ion-button>
  </div>

</ion-content>
