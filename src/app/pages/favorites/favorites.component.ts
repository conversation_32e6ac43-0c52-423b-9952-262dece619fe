import { Component } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-favorites',
  template: `
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/more"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ 'MORE.FAVORITES' | translate }}</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <ion-list>
        <ion-item-divider>
          <ion-label>{{ 'FAVORITES.SAVED' | translate }}</ion-label>
        </ion-item-divider>

        <ion-item lines="none" class="ion-text-center">
          <ion-label color="medium">
            <h3>{{ 'FAVORITES.EMPTY' | translate }}</h3>
            <p>{{ 'FAVORITES.EMPTY_MESSAGE' | translate }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-content>
  `,
  standalone: true,
  imports: [IonicModule, CommonModule, TranslateModule]
})
export class FavoritesComponent {}
