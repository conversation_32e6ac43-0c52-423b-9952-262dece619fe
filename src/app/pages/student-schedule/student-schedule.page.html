<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/academic"></ion-back-button>
      <!-- Update defaultHref to your academic page route -->
    </ion-buttons>
    <ion-title>{{ 'SCHEDULE.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="schedule-content-root">
  <div class="schedule-table">

    <!-- Fixed Column for Time Labels (Sticky Left) -->
    <div class="time-labels-col">
      <div class="time-labels-col-header-spacer"></div> <!-- Spacer for day names header height -->
      <div class="hour-row" *ngFor="let hour of hours">
        <span class="hour-text">{{ hour }}:00</span>
      </div>
    </div>

    <!-- Scrollable Area for Day Names and Event Grid (Scrolls Horizontally) -->
    <div class="scrollable-days-and-events-area">

      <!-- Day Names Header Row (Sticky Top) -->
      <div class="day-names-header">
        <div class="day-cell-header" *ngFor="let day of weekDays" [class.current-day]="isCurrentDay(day.number)">
          {{ 'DAYS.' + day.number| translate }}
        </div>
      </div>

      <!-- Event Grid Rows -->
      <div class="event-grid">
        <div class="event-grid-day-col" *ngFor="let day of weekDays" [class.current-day]="isCurrentDay(day.number)">
          <!-- Background Time Slots for grid lines -->
          <div class="event-grid-time-slot" *ngFor="let hour of hours"></div>

          <!-- Current Time Indicator -->
          <div *ngIf="isCurrentDay(day.number)" class="current-time-indicator"
            [ngStyle]="{ 'top': currentTimePosition + 'px' }">
          </div>

          <!-- Actual Course Blocks -->
          <div *ngFor="let course of getCoursesForDay(day.number)" class="course-block"
            [ngStyle]="getCourseStyle(course, day.number)">
            <div class="course-content">
              <div class="course-name" [ngStyle]="{ 'font-size': getCourceNameFontSize(course,day.number) }">
                {{ course.course_name }}
              </div>
              <div class="course-code">{{ course.section_seq }}</div>
              <div class="course-details">
                <div class="course-time">
                  {{ getFormattedTime(course, day.number) }}
                </div>
                <div class="course-info">
                  <!-- <span class="course-section">Section: {{ course.section }}</span> -->
                  <span class="course-type">{{ course.activity_desc }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>
