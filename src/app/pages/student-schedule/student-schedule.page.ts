import {
  Component,
  <PERSON><PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { CommonModule, NgClass } from '@angular/common';
import { IonicModule, IonContent } from '@ionic/angular';
import { ScheduleService } from '../../services/schedule.service';
import { Course, DayTime } from '../../models/schedule.model';
import { interval, Subscription } from 'rxjs';
import { TranslatePipe } from '@ngx-translate/core';

interface DayInfo {
  number: number;
}

const HOUR_HEIGHT_PX = 90; // Was 60px, increased by 50%
const MINUTE_SCALE_FACTOR = HOUR_HEIGHT_PX / 60; // = 1.5

@Component({
  selector: 'app-student-schedule',
  templateUrl: './student-schedule.page.html',
  styleUrls: ['./student-schedule.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, TranslatePipe],
})
export class StudentSchedulePage implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(IonContent) ionContentContext?: IonContent; // Updated variable name for clarity

  scheduleData = this.scheduleService.scheduleData;
  weekDays: DayInfo[] = [
    { number: 1 },
    { number: 2 },
    { number: 3 },
    { number: 4 },
    { number: 5 },
    { number: 6 },
    { number: 7 },
  ];
  hours: number[] = Array.from({ length: 24 }, (_, i) => i);

  // Color cache to keep colors consistent
  private courseColors: { [key: string]: string } = {};

  // Current time indicator
  currentTimePosition: number = 0;
  currentDayNumber: number = 0;
  private timeUpdateSubscription?: Subscription;

  constructor(private scheduleService: ScheduleService) {}

  ngOnInit() {
    // Update current time indicator every minute
    this.updateCurrentTimePosition();
    this.timeUpdateSubscription = interval(60000).subscribe(() => {
      this.updateCurrentTimePosition();
    });
  }

  ngAfterViewInit() {
    // Scroll to current time with a slight delay to ensure the view is ready
    setTimeout(() => {
      this.scrollToCurrentTime();
    }, 500);
  }

  ngOnDestroy() {
    if (this.timeUpdateSubscription) {
      this.timeUpdateSubscription.unsubscribe();
    }
  }

  scrollToCurrentTime() {
    if (!this.ionContentContext) return;

    const now = new Date();
    const hours = now.getHours();
    // Scroll to current hour, offset by a couple of hours to bring it into view
    const scrollPosition = Math.max(
      0,
      hours * HOUR_HEIGHT_PX - 2 * HOUR_HEIGHT_PX
    );
    this.ionContentContext.scrollToPoint(0, scrollPosition, 1000);
  }

  updateCurrentTimePosition() {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();

    this.currentTimePosition =
      hours * HOUR_HEIGHT_PX + minutes * MINUTE_SCALE_FACTOR;

    // JS getDay(): 0 (Sun) .. 6 (Sat). The data model uses 1 (Sun) .. 7 (Sat).
    const jsDayIndex = now.getDay();
    this.currentDayNumber = jsDayIndex === 0 ? 1 : jsDayIndex + 1;
  }

  getCoursesForDay(dayNumber: number): Course[] {
    if (!this.scheduleData()) return [];
    return (
      this.scheduleData()?.['time-table'].filter((course) =>
        course.times.some((time) => time.day.number === dayNumber)
      ) || []
    );
  }

  getCourseStyle(course: Course, dayNumber: number): { [key: string]: string } {
    const timeInfo = course.times.find((time) => time.day.number === dayNumber);
    if (!timeInfo) return {};

    const startTime = this.parseTime(timeInfo.time_slot.formatted.start);
    const endTime = this.parseTime(timeInfo.time_slot.formatted.end);

    const top = this.calculateTopPosition(startTime);
    const height = this.calculateHeight(startTime, endTime);
    const backgroundColor = this.getCourseColor(course.course_no);

    return {
      top: `${top}px`,
      height: `${height}px`,
      backgroundColor,
      // Additional per-course styling could be added here
    };
  }

  parseTime(timeString: string): { hours: number; minutes: number } {
    const [hoursStr, minutesStr] = timeString.split(':');
    return {
      hours: parseInt(hoursStr, 10),
      minutes: parseInt(minutesStr, 10),
    };
  }

  calculateTopPosition(time: { hours: number; minutes: number }): number {
    return time.hours * HOUR_HEIGHT_PX + time.minutes * MINUTE_SCALE_FACTOR;
  }

  calculateHeight(
    startTime: { hours: number; minutes: number },
    endTime: { hours: number; minutes: number }
  ): number {
    const startTotalMinutes = startTime.hours * 60 + startTime.minutes;
    const endTotalMinutes = endTime.hours * 60 + endTime.minutes;
    return (endTotalMinutes - startTotalMinutes) * MINUTE_SCALE_FACTOR;
  }

  getFormattedTime(course: Course, dayNumber: number): string {
    const timeInfo = course.times.find((time) => time.day.number === dayNumber);
    if (!timeInfo) return '';
    return `${timeInfo.time_slot.formatted.start} - ${timeInfo.time_slot.formatted.end}`;
  }

  getCourseColor(courseNo: string): string {
    // If we already have a color for this course, return it
    if (this.courseColors[courseNo]) {
      return this.courseColors[courseNo];
    }

    // Define a palette of colors suitable for a calendar
    const colors = [
      '#4285F4', // Blue
      '#EA4335', // Red
      '#FBBC04', // Yellow
      '#34A853', // Green
      '#8E24AA', // Purple
      '#00ACC1', // Cyan
      '#F06292', // Pink
      '#FF7043', // Deep Orange
      '#3949AB', // Indigo
      '#00897B', // Teal
    ];

    // Create a deterministic but seemingly random color based on the course number
    const hashCode = courseNo.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);

    const colorIndex = Math.abs(hashCode) % colors.length;
    const selectedColor = colors[colorIndex];

    // Save it for future reference
    this.courseColors[courseNo] = selectedColor;

    return selectedColor;
  }

  isCurrentDay(dayNumberInput: number): boolean {
    // Ensure currentDayNumber is correctly set by updateCurrentTimePosition based on JS Date().getDay()
    // JS getDay(): 0 (Sun) to 6 (Sat). Model day.number: 1 (Sun) to 7 (Sat).
    // My updateCurrentTimePosition sets this.currentDayNumber to be 1 (Sun) to 7 (Sat).
    return dayNumberInput === this.currentDayNumber;
  }
  //calculateHeight
  getCourceNameFontSize(course: Course, dayNumber: number): string {
    const timeInfo = course.times.find((time) => time.day.number === dayNumber);
    if (!timeInfo) return '';
    const startTime = this.parseTime(timeInfo.time_slot.formatted.start);
    const endTime = this.parseTime(timeInfo.time_slot.formatted.end);
    const startTotalMinutes = startTime.hours * 60 + startTime.minutes;
    const endTotalMinutes = endTime.hours * 60 + endTime.minutes;
    const totalMinutes = endTotalMinutes - startTotalMinutes;
    console.log(totalMinutes, course.course_name);

    return totalMinutes < 100 ? '9px' : '';
  }
}
