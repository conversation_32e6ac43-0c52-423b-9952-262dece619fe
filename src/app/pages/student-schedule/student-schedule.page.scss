.schedule-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.day-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--ion-background-color);
  border-bottom: 1px solid var(--ion-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.time-column {
  width: 60px;
  min-width: 60px;
  position: sticky;
  left: 0;
  z-index: 5;
  background-color: var(--ion-background-color);
  border-right: 1px solid var(--ion-color-light);
}

.day-column {
  flex: 1;
  min-width: 120px;
  position: relative;
  border-right: 1px solid var(--ion-color-light-shade);
}

.day-column.current-day {
  background-color: rgba(var(--ion-color-primary-rgb), 0.05);
}

.day-header .day-column.current-day {
  background-color: rgba(var(--ion-color-primary-rgb), 0.1);
}

.day-header .day-column.current-day .day-name {
  color: var(--ion-color-primary);
  font-weight: 700;
}

.day-name {
  padding: 12px 4px;
  text-align: center;
  font-weight: 600;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-dark);
  font-size: 14px;
}

.schedule-grid {
  display: flex;
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  position: relative;
  height: calc(100% - 50px);
  -webkit-overflow-scrolling: touch; // For smooth iOS scrolling
}

.time-slot {
  height: 60px;
  border-bottom: 1px solid var(--ion-color-light);
  position: relative;
}

.hour-label {
  position: absolute;
  top: -10px;
  right: 10px;
  font-size: 12px;
  color: var(--ion-color-medium);
}

.course-block {
  position: absolute;
  left: 2px;
  right: 2px;
  border-radius: 8px;
  padding: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  // Hover effect
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 20;
  }
}

.course-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: white;
}

.course-code {
  font-size: 12px;
  margin-bottom: 4px;
  opacity: 0.9;
  color: white;
}

.course-details {
  font-size: 12px;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.9);
}

.course-time {
  font-weight: 600;
  margin-bottom: 2px;
}

.course-info {
  display: flex;
  justify-content: space-between;
  gap: 4px;
}

.course-section, .course-type {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 11px;
}

// Media query for different screen sizes
@media (max-width: 768px) {
  .day-column {
    min-width: 140px;
  }

  .time-column {
    width: 50px;
    min-width: 50px;
  }

  .hour-label {
    font-size: 10px;
    right: 5px;
  }

  .course-block {
    padding: 6px;
  }

  .course-name {
    font-size: 12px;
  }

  .course-code, .course-details {
    font-size: 10px;
  }
}

// Zebra striping for alternate hours
.time-slot:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.01);
}

// Current time indicator
.current-time-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--ion-color-danger);
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    left: -5px;
    top: -4px;
    width: 10px;
    height: 10px;
    background-color: var(--ion-color-danger);
    border-radius: 50%;
  }
}

ion-content.schedule-content-root {
  /* Let IonContent manage vertical scrolling */
}

.schedule-table {
  display: flex;
  position: relative;
  /* Do not fix the height so it can expand and parent ion-content can scroll */
  /* Let the parent ion-content manage vertical scroll – avoid double scrollbars */
  overflow-y: visible;
  overflow-x: hidden; // Horizontal scroll handled by child
}

// Fixed Column for Time Labels (Sticky Left)
.time-labels-col {
  width: 70px; // Increased width for time labels
  min-width: 70px;
  flex-shrink: 0;
  position: sticky;
  left: 0;
  z-index: 3; // Above event grid, below main header if any overlap was possible
  background-color: var(--ion-background-color, #fff);
  border-right: 1px solid var(--ion-color-step-150, #e0e0e0);
}

.time-labels-col-header-spacer {
  height: 50px; // Matches .day-names-header height
  border-bottom: 1px solid var(--ion-color-step-150, #e0e0e0);
}

.hour-row {
  height: 90px; // Point 4: Increased from 60px (50% increase)
  display: flex;
  align-items: flex-start; // Align text to the top of the slot
  justify-content: flex-end;
  padding-top: 5px; // Point 2: Ensure label is not cut
  padding-right: 8px;
  position: relative;
  font-size: 12px;
  color: var(--ion-color-step-600, #666);

  // Horizontal grid line emanating from the time label column
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15%; // Start line away from text
    right: 0;
    height: 1px;
    background-color: var(--ion-color-step-100, #f0f0f0);
  }
}

// Scrollable Area for Day Names and Event Grid
.scrollable-days-and-events-area {
  flex-grow: 1;
  overflow-x: auto;
  overflow-y: visible; // Vertical scroll is handled by .schedule-table
  position: relative;
}

// Day Names Header Row (Sticky Top within scrollable area)
.day-names-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2; // Above event-grid content
  background-color: var(--ion-background-color, #fff);
  height: 50px;
  border-bottom: 1px solid var(--ion-color-step-150, #e0e0e0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.day-cell-header {
  min-width: 140px; // As per previous responsive style
  flex: 1 0 auto; // Allow shrinking but prefer min-width
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: var(--ion-color-step-800, #333);
  border-right: 1px solid var(--ion-color-step-100, #f0f0f0);

  &.current-day {
    background-color: rgba(var(--ion-color-primary-rgb), 0.05);
    color: var(--ion-color-primary);
    font-weight: 700;
  }

  &:last-child {
    border-right: none;
  }
}

// Event Grid Rows
.event-grid {
  display: flex;
  position: relative; // For course-block positioning
}

.event-grid-day-col {
  min-width: 140px; // Match header cell width
  flex: 1 0 auto;
  position: relative;
  border-right: 1px solid var(--ion-color-step-100, #f0f0f0);
  // Point 3: Height is determined by sum of event-grid-time-slot children

  &:last-child {
    border-right: none;
  }

  &.current-day {
    background-color: rgba(var(--ion-color-primary-rgb), 0.03);
  }
}

.event-grid-time-slot {
  height: 90px; // Point 4: Increased from 60px
  border-bottom: 1px solid var(--ion-color-step-100, #f0f0f0);
  box-sizing: border-box;

  &:last-child {
    border-bottom: none; // No bottom border for the last slot in a day column
  }
}

// Course Block Styling (mostly unchanged, check positioning context)
.course-block {
  position: absolute;
  left: 3px;
  right: 3px;
  border-radius: 6px;
  padding: 6px 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.15s ease, box-shadow 0.15s ease;
  color: white;
  font-size: 12px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    z-index: 20; // Ensure hover is on top
  }
}

.course-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-name {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 2px;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-code {
  font-size: 11px;
  margin-bottom: 3px;
  opacity: 0.85;
}

.course-details {
  font-size: 11px;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.95);
  gap: 1px;
}

.course-time {
  font-weight: 500;
}

.course-info {
  display: flex;
  justify-content: space-between;
  gap: 4px;
}

.course-section,
.course-type {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 10px;
  opacity: 0.8;
}

// Current Time Indicator (adjust positioning if needed)
.current-time-indicator {
  position: absolute;
  left: 0; // Should be on .event-grid-day-col.current-day
  right: 0;
  height: 2px;
  background-color: var(--ion-color-danger);
  z-index: 1; // Ensure it's above grid lines but below course blocks

  &::after {
    content: '';
    position: absolute;
    left: -4px; // Adjust so it's centered on the line
    top: -3px;
    width: 8px;
    height: 8px;
    background-color: var(--ion-color-danger);
    border-radius: 50%;
    box-shadow: 0 0 3px rgba(0,0,0,0.3);
  }
}

// Responsive adjustments for smaller screens
@media (max-width: 768px) {
  .day-cell-header, .event-grid-day-col {
    min-width: 120px;
  }
  .time-labels-col {
    width: 60px;
    min-width: 60px;
  }
  .hour-row {
    font-size: 11px;
    padding-right: 5px;
  }
  .course-block {
    padding: 4px 6px;
  }
  .course-name {
    font-size: 12px;
  }
  .course-code, .course-details {
    font-size: 10px;
  }
}
