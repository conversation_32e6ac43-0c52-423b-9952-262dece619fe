import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { AnnoucmentService } from '../../services/annoucment/annoucment.service';
import { Announcement } from 'src/app/models/app.models';
import { TranslateModule } from '@ngx-translate/core';
import {
  AlertCircleIcon,
  CalendarIcon,
  CancelIcon,
  NewsIcon,
} from '@hugeicons-pro/core-stroke-rounded';
import { HugeiconsIconComponent } from '@hugeicons/angular';

@Component({
  selector: 'app-announcements',
  templateUrl: './announcements.component.html',
  styleUrls: ['./announcements.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, TranslateModule, HugeiconsIconComponent],
})
export class AnnouncementsComponent {
  // Bottom sheet control
  isBottomSheetOpen = false;
  selectedAnnouncement: Announcement | undefined;

  constructor(
    public announcementService: AnnoucmentService,
    private router: Router
  ) {}

  // Format date to be more readable
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Open announcement detail in bottom sheet
  openAnnouncementDetail(announcement: Announcement) {
    this.selectedAnnouncement = announcement;
    this.isBottomSheetOpen = true;
  }

  // Close the bottom sheet
  dismissBottomSheet() {
    this.isBottomSheetOpen = false;
  }

  protected readonly CancelIcon = CancelIcon;
  protected readonly CalendarIcon = CalendarIcon;
  protected readonly NewsIcon = NewsIcon;
  protected readonly AlertCircleIcon = AlertCircleIcon;
}
