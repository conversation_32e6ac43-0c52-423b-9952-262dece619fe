.announcements-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.announcement-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.card-content {
  display: flex;
  align-items: center;
  position: relative;
  padding: 16px;
}

.status-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--ion-color-primary);
}

.announcement-info {
  flex: 1;
  padding-left: 8px;
}

.announcement-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 4px;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-meta {
  font-size: 13px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.announcement-date {
  font-size: 12px;
  color: var(--ion-color-medium);
}

.announcement-action {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
  font-size: 20px;
  padding-left: 8px;
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 64px;
  color: var(--ion-color-medium);

  ion-spinner {
    margin-bottom: 16px;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 64px 24px;
  color: var(--ion-color-medium);

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--ion-color-medium-shade);
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--ion-color-dark);
  }

  p {
    font-size: 14px;
    max-width: 260px;
  }
}

// Bottom Sheet Modal Styles
.announcement-detail-modal {
  --backdrop-opacity: 0.4;
  --border-radius: 28px 28px 0 0;
  --box-shadow: 0 -10px 50px rgba(0, 0, 0, 0.25);

  &::part(content) {
    border-radius: 28px 28px 0 0;
    box-shadow: 0 -10px 50px rgba(0, 0, 0, 0.25);
  }
}

.modal-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: var(--ion-background-color);
}

.modal-header {
  padding: 20px 20px 0;
  position: relative;
  z-index: 10;
  background-color: var(--ion-color-primary);
  color: white;
}

.primary-header {
  background-color: var(--ion-color-primary);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.announcement-title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 16px;
  max-width: 85%;
}

.modal-header .announcement-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  color: white;
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  max-width: 100%;
}

.close-button {
  margin-top: -8px;
  margin-right: -8px;
  --padding-start: 8px;
  --padding-end: 8px;
  height: 40px;
  --background-hover: rgba(255, 255, 255, 0.1);
  --ripple-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.course-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: inherit;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 4px;
  backdrop-filter: blur(4px);
  width: fit-content;
}

.status-banner {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.15);
  padding: 10px 20px;
  font-weight: 600;
  font-size: 0.95rem;
  margin: 0 -20px;
}

.modal-content {
  --background: var(--ion-background-color);
  --padding-bottom: 40px;
}

.content-container {
  padding: 24px 20px;
}

.announcement-body {
  line-height: 1.6;
  color: var(--ion-color-dark);

  ::ng-deep {
    img {
      max-width: 100%;
      height: auto;
    }

    a {
      color: var(--ion-color-primary);
      text-decoration: none;
    }

    p {
      margin-bottom: 12px;
      line-height: 1.5;
    }

    ul, ol {
      padding-left: 20px;
      margin-bottom: 12px;
    }

    h1, h2, h3, h4, h5, h6 {
      margin-top: 16px;
      margin-bottom: 8px;
    }
  }
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: 40px 20px;
  gap: 16px;

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--ion-color-medium-shade);
  }

  p {
    color: var(--ion-color-medium);
    font-size: 1rem;
    margin-bottom: 16px;
  }
}

.bottom-spacer {
  height: 100px;
}

// Responsive Adjustments
@media (min-width: 768px) {
  .announcements-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
  }
}

