<ion-header>
  <ion-toolbar>
    <ion-title>{{'TABS.ANNOUNCEMENTS' | translate}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- Loading state -->
  <div *ngIf="announcementService.loading()" class="loading-state ion-padding">
    <ion-spinner name="crescent"></ion-spinner>
    <p>
      {{ 'LOADING' | translate }}
    </p>
  </div>

  <!-- Announcements list -->
  <div *ngIf="announcementService.sortedAnnouncements().length > 0">
    <div class="announcements-container">
      <div *ngFor="let announcement of announcementService.sortedAnnouncements()" class="announcement-card"
        (click)="openAnnouncementDetail(announcement)">
        <div class="card-content">
          <!-- Colored bar on the left -->
          <div class="status-indicator"></div>

          <!-- Announcement main info -->
          <div class="announcement-info">
            <div class="announcement-title">{{ announcement.title }}</div>
            <div class="announcement-meta">Course ID: {{ announcement.courseId }}</div>
            <div class="announcement-date">{{ formatDate(announcement.created) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty state -->
  <div *ngIf="announcementService.sortedAnnouncements().length === 0 && !announcementService.loading()"
    class="empty-state">
    <hugeicons-icon [icon]="NewsIcon" size="48" [strokeWidth]="1.5"></hugeicons-icon>
    <h3>
      {{ 'ANNOUNCEMENTS.NO_ANNOUNCEMENTS' | translate }}
    </h3>
    <p>
      {{ 'ANNOUNCEMENTS.NO_ANNOUNCEMENTS_DESCRIPTION' | translate }}
    </p>
  </div>
</ion-content>

<!-- Announcement Details Bottom Sheet -->
<ion-modal [isOpen]="isBottomSheetOpen" (didDismiss)="dismissBottomSheet()" [breakpoints]="[0, 0.95]"
  [initialBreakpoint]="0.95" [backdropBreakpoint]="0" class="announcement-detail-modal">
  <ng-template>
    <div class="modal-wrapper">
      <!-- Header with background color -->
      <div class="modal-header" [ngClass]="'primary-header'">
        <div class="header-content">
          <div class="announcement-title-section">
            <!-- Course info with badge -->
            <div class="course-badge">
              <span>{{ selectedAnnouncement?.courseId }}</span>
            </div>
            <h1 class="announcement-title">{{ selectedAnnouncement?.title }}</h1>
          </div>
          <ion-button (click)="dismissBottomSheet()" fill="clear" class="close-button">
            <hugeicons-icon [icon]="CancelIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
          </ion-button>
        </div>

        <!-- Date banner -->
        <div class="status-banner">
          <hugeicons-icon [icon]="CalendarIcon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
          <span>{{ selectedAnnouncement ? formatDate(selectedAnnouncement.created) : '' }}</span>
        </div>
      </div>

      <!-- Scrollable content -->
      <ion-content class="modal-content">
        <div *ngIf="selectedAnnouncement" class="content-container">
          <div class="announcement-body" [innerHTML]="selectedAnnouncement.body"></div>
        </div>

        <!-- No data message -->
        <div *ngIf="!selectedAnnouncement" class="no-data-container">
          <hugeicons-icon [icon]="AlertCircleIcon" size="48" [strokeWidth]="1.5"></hugeicons-icon>
          <p>No announcement data available</p>
          <ion-button fill="outline" (click)="dismissBottomSheet()">
            اغلاق
          </ion-button>
        </div>

        <!-- Spacer for safe area -->
        <div class="bottom-spacer"></div>
      </ion-content>
    </div>
  </ng-template>
</ion-modal>
