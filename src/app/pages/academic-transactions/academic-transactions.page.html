<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/academic"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'ACADEMIC.TRANSACTIONS' | translate }}</ion-title>
  </ion-toolbar>

  <!-- Semester tabs -->
  <ion-toolbar *ngIf="(semesters()?.length ?? 0) > 0">
    <ion-segment scrollable="true" [value]="selectedSemester()">
      <ion-segment-button *ngFor="let sem of semesters()" [value]="sem.semester" (click)="selectSemester(sem.semester)">
        <ion-label>{{ sem.semester }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">{{ 'ACADEMIC.TRANSACTIONS' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading state -->
  <div *ngIf="!semesters()" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
    <ion-text color="medium">
      <p class="ion-margin-top">{{ 'LOADING' | translate }}</p>
    </ion-text>
  </div>

  <!-- Courses list using ion-list -->
  <div *ngIf="selectedSemester() && coursesForSelected().length > 0">
    <ion-list>
      <ion-item-group>
        <ion-item-divider>
          <ion-label>{{ 'ACADEMIC.COURSES' | translate }} - {{ selectedSemester() }}</ion-label>
        </ion-item-divider>

        <ion-item
          *ngFor="let course of coursesForSelected()"
          button
          (click)="showCourseDetails(course)">

          <!-- Status badge -->
          <ion-badge
            slot="start"
            class="ion-padding-horizontal"
            [color]="getBadgeColor(course)">
            {{ course.status_code === '3' ? "ع" : course.letter_grade }}
          </ion-badge>

          <ion-label>
            <h2>{{ course.course_code }}</h2>
            <h3>{{ getDisplayCourseName(course) }}</h3>
            <ion-text color="medium">
              <p>
                <hugeicons-icon [icon]="InformationCircleIcon" size="16" [strokeWidth]="1.5"></hugeicons-icon>
                {{ 'ACADEMIC.HOURS' | translate }}: {{ course.crd_hrs }}  •
                <hugeicons-icon [icon]="DocumentValidationIcon" size="16" [strokeWidth]="1.5"></hugeicons-icon>
                {{ 'ACADEMIC.TOTAL_POINTS' | translate }}: {{ calculatePoints(course) }}
              </p>
            </ion-text>
          </ion-label>
        </ion-item>
      </ion-item-group>
    </ion-list>
  </div>

  <!-- Empty state -->
  <div *ngIf="selectedSemester() && coursesForSelected().length === 0" class="ion-text-center ion-padding">
    <hugeicons-icon [icon]="Folder01Icon" size="64" [strokeWidth]="1.5" color="medium"></hugeicons-icon>
    <ion-text>
      <h2>{{ 'NO_COURSES' | translate }}</h2>
    </ion-text>
    <ion-text color="medium">
      <p>{{ 'NO_COURSES_MESSAGE' | translate }}</p>
    </ion-text>
  </div>
</ion-content>

<!-- Course Details Modal -->
<ion-modal
  [isOpen]="isBottomSheetOpen"
  (didDismiss)="dismissBottomSheet()"
  [breakpoints]="[0, 0.95]"
  [initialBreakpoint]="0.95">

  <ng-template>
    <ion-header>
      <ion-toolbar [color]="getToolbarColor(selectedCourse)">
        <ion-title>{{ 'ACADEMIC.COURSE_DETAILS' | translate }}</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismissBottomSheet()">
            <hugeicons-icon [icon]="Cancel01Icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div *ngIf="selectedCourse">

        <!-- Course header info as ion-item -->
        <ion-list>
          <ion-item-group>
            <ion-item-divider>
              <ion-label>{{ 'academic.courseInfo' | translate }}</ion-label>
            </ion-item-divider>

            <ion-item>
              <hugeicons-icon [icon]="Book02Icon" slot="start" size="24" [strokeWidth]="1.5" [color]="getBadgeColor(selectedCourse)"></hugeicons-icon>
              <ion-label>
                <h2>{{ selectedCourse.course_code }}</h2>
                <h3>{{ getDisplayCourseName(selectedCourse) }}</h3>
              </ion-label>

              <ion-badge slot="end" [color]="getBadgeColor(selectedCourse)">
                {{ selectedCourse.status_code === '3' ? 'ع' : selectedCourse.letter_grade }}
              </ion-badge>
            </ion-item>
          </ion-item-group>
        </ion-list>

        <!-- Course details -->
        <ion-list>
          <ion-item-group>
            <ion-item-divider>
              <ion-label>{{ 'ACADEMIC.COURSE_DETAILS' | translate }}</ion-label>
            </ion-item-divider>

            <ion-item>
              <hugeicons-icon [icon]="Clock01Icon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
              <ion-label>
                <h3>{{ 'ACADEMIC.HOURS' | translate }}</h3>
                <p>
                  @if ( +selectedCourse.crd_hrs === 1 ){
                    ساعة واحدة
                  } @else if ( +selectedCourse.crd_hrs === 2 ){
                    ساعتين
                  } @else if ( +selectedCourse.crd_hrs === 3 ){
                    ثلاث ساعات
                  } @else if ( +selectedCourse.crd_hrs === 4 ){
                    أربع ساعات
                  } @else if ( +selectedCourse.crd_hrs === 5 ){
                    خمس ساعات
                  } @else if ( +selectedCourse.crd_hrs === 6 ){
                    ست ساعات
                  } @else if ( +selectedCourse.crd_hrs === 7 ){
                    سبعة ساعات
                  } @else if ( +selectedCourse.crd_hrs === 8 ){
                    ثمان ساعات
                  } @else if ( +selectedCourse.crd_hrs === 9 ){
                    تسعة ساعات
                  } @else {
                    {{ selectedCourse.crd_hrs }}
                  }
                </p>
              </ion-label>

              <ion-note slot="end">{{ selectedCourse.crd_hrs }}</ion-note>
            </ion-item>

            <ion-item>
              <hugeicons-icon [icon]="Book02Icon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
              <ion-label>
                <h3>{{ 'ACADEMIC.LETTER_GRADE' | translate }}</h3>
                <p>{{ 'ACADEMIC.LETTER_GRADE_DESC' | translate }}</p>
              </ion-label>

              <ion-note slot="end" [color]="getBadgeColor(selectedCourse)">
                {{ selectedCourse.letter_grade }}
              </ion-note>
            </ion-item>

            <ion-item>
              <hugeicons-icon [icon]="Calendar01Icon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
              <ion-label>
                <h3>{{ 'ACADEMIC.SEMESTER' | translate }}</h3>
                <p>{{ 'ACADEMIC.SEMESTER_DESC' | translate }}</p>
              </ion-label>

              <ion-note slot="end">{{ selectedSemester() }}</ion-note>
            </ion-item>

            <ion-item>
              <hugeicons-icon [icon]="DocumentValidationIcon" slot="start" size="24" [strokeWidth]="1.5" color="primary"></hugeicons-icon>
              <ion-label>
                <h3>{{ 'ACADEMIC.TOTAL_POINTS' | translate }}</h3>
                <p>{{ 'ACADEMIC.CALCULATED_POINTS' | translate }}</p>
              </ion-label>

              <ion-note slot="end" color="primary">{{ calculatePoints(selectedCourse) }}</ion-note>
            </ion-item>
          </ion-item-group>
        </ion-list>

        <!-- Additional course info if available -->
        <ion-list *ngIf="selectedCourse.course_name_s">
          <ion-item-group>
            <ion-item-divider>
              <ion-label>
                <hugeicons-icon [icon]="InformationCircleIcon" size="16" [strokeWidth]="1.5"></hugeicons-icon>
                {{ 'ACADEMIC.ADDITIONAL_INFO' | translate }}
              </ion-label>
            </ion-item-divider>

            <ion-item>
              <hugeicons-icon [icon]="InformationCircleIcon" slot="start" size="24" [strokeWidth]="1.5" color="tertiary"></hugeicons-icon>
              <ion-label class="ion-text-wrap">
                <h3>{{ 'ACADEMIC.COURSE_DESCRIPTION' | translate }}</h3>
                <p>{{ selectedCourse.course_name_s }}</p>
              </ion-label>
            </ion-item>
          </ion-item-group>
        </ion-list>

      </div>

      <!-- No data message -->
      <div *ngIf="!selectedCourse" class="ion-text-center ion-padding">
        <hugeicons-icon [icon]="InformationCircleIcon" size="64" [strokeWidth]="1.5" color="medium"></hugeicons-icon>
        <ion-text color="medium">
          <h3>{{ 'ACADEMIC.NO_COURSE_DATA' | translate }}</h3>
        </ion-text>
        <ion-button fill="outline" (click)="dismissBottomSheet()">
          {{ 'COMMON.CLOSE' | translate }}
        </ion-button>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>
