// Only minimal customizations needed
ion-content {
  --padding-bottom: var(--ion-safe-area-bottom, 0px);
}

// Ensure proper spacing for loading and empty states
.ion-text-center {
  ion-spinner {
    margin-bottom: 1rem;
  }

  ion-icon[size="large"] {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
}

// Small adjustment for course meta info alignment
ion-text p {
  display: flex;
  align-items: center;
  gap: 0.25rem;

  ion-icon {
    font-size: 0.9rem;
  }
}
