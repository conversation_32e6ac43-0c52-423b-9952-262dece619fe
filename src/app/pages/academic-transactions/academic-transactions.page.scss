// Only minimal customizations needed
ion-content {
  --padding-bottom: var(--ion-safe-area-bottom, 0px);
}

// Ensure proper spacing for loading and empty states
.ion-text-center {
  ion-spinner {
    margin-bottom: 1rem;
  }

  hugeicons-icon[size="64"] {
    margin-bottom: 1rem;
  }
}

// Small adjustment for course meta info alignment
ion-text p {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

// Align icons with text in card titles
ion-card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ion-badge {
  width: 50px;
}