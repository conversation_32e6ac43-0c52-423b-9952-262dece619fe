import {
  Component,
  OnInit,
  signal,
  WritableSignal,
  inject,
  ChangeDetectorRef,
  effect,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AcademicTransactionsService } from '../../services/academic-transactions.service';
import { TransactionCourse } from '../../models/app.models';

@Component({
  selector: 'app-academic-transactions',
  templateUrl: './academic-transactions.page.html',
  styleUrls: ['./academic-transactions.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, TranslateModule],
})
export class AcademicTransactionsPage implements OnInit {
  private cdr = inject(ChangeDetectorRef);

  constructor(
    private academicTransactionsService: AcademicTransactionsService,
    private translateService: TranslateService
  ) {}

  readonly semesters =
    this.academicTransactionsService.academicTransactionsData;

  selectedSemester: WritableSignal<string | null> = signal(null);

  // Bottom sheet state
  public isBottomSheetOpen = false;
  public selectedCourse: TransactionCourse | null = null;

  private selectedSemesterEffect = effect(() => {
    const semestersValue = this.semesters();
    if (
      semestersValue &&
      semestersValue.length > 0 &&
      this.selectedSemester() == null
    ) {
      this.selectedSemester.set(
        semestersValue[semestersValue.length - 1].semester
      );
    }
  });

  ngOnInit(): void {
    const semestersValue = this.semesters();
    if (semestersValue && semestersValue.length > 0) {
      this.selectedSemester.set(
        semestersValue[semestersValue.length - 1].semester
      );
    }
  }

  selectSemester(semesterCode: string): void {
    this.selectedSemester.set(semesterCode);
  }

  coursesForSelected(): TransactionCourse[] {
    const semesterData = this.semesters()?.find(
      (s) => s.semester === this.selectedSemester()
    );
    return semesterData ? semesterData.courses : [];
  }

  calculatePoints(course: TransactionCourse): number {
    const hours = parseFloat(course.crd_hrs ?? '0');
    const quality = parseFloat(course.quality_points ?? '0');
    const points = hours * quality;
    return isNaN(points) ? 0 : parseFloat(points.toFixed(2));
  }

  getDisplayCourseName(course: TransactionCourse): string {
    if (this.translateService.currentLang === 'en' && course.course_name_s) {
      return course.course_name_s;
    }
    return course.course_name;
  }

  /**
   * Returns Ionic color names for badges and toolbars
   */
  getBadgeColor(course: TransactionCourse): string {
    const letter = course.letter_grade_s?.toUpperCase() ?? '';
    if (['F'].includes(letter)) {
      return 'danger';
    }
    if (['D', 'D+'].includes(letter)) {
      return 'warning';
    }
    if (['C', 'C+'].includes(letter)) {
      return 'tertiary';
    }
    if (['B', 'B+'].includes(letter)) {
      return 'secondary';
    }

    if (letter == '' && course.status_code === '3') {
      return 'medium';
    }

    if (letter == '' && course.status_code === '1') {
      return 'dark';
    }

    return 'primary';
  }

  /**
   * Returns appropriate toolbar color for modal
   */
  getToolbarColor(course: TransactionCourse | null): string {
    if (!course) return 'primary';
    return this.getBadgeColor(course);
  }

  showCourseDetails(course: TransactionCourse) {
    this.selectedCourse = course;
    this.isBottomSheetOpen = true;
    this.cdr.detectChanges();
  }

  dismissBottomSheet() {
    this.isBottomSheetOpen = false;
    this.selectedCourse = null;
  }
}
