import { Component, On<PERSON>ni<PERSON>, On<PERSON><PERSON><PERSON>, inject, signal, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonicModule, ToastController } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Splide } from '@splidejs/splide';
import { HugeiconsIconComponent } from '@hugeicons/angular';

// Hugeicons imports
import {
  File02Icon,
  HierarchySquare01Icon,
  ShoppingCart01Icon,
  Wallet01Icon,
  UserGroupIcon,
  Calendar03Icon,
  NewsIcon,
  SchoolIcon,
  ArrowLeft01Icon
} from '@hugeicons-pro/core-stroke-rounded';

// Services
import { HomeDataService } from '../../services/home-data/home-data.service';
import { AuthService } from '../../services/auth/auth.service';
import { LanguageService } from '../../services/language/language.service';

// Models
import {
  HomePageData,
  NewsItem,
  Achievement,
  CalendarEvent,
  UniversityInfo,
  College,
  HomeAnnouncement,
  SplideOptions
} from '../../models/home.models';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, TranslateModule, HugeiconsIconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HomeComponent implements OnInit, OnDestroy {
  private homeDataService = inject(HomeDataService);
  private authService = inject(AuthService);
  private languageService = inject(LanguageService);
  private router = inject(Router);
  private toastController = inject(ToastController);

  // Hugeicons
  readonly File02Icon = File02Icon;
  readonly HierarchySquare01Icon = HierarchySquare01Icon;
  readonly ShoppingCart01Icon = ShoppingCart01Icon;
  readonly Wallet01Icon = Wallet01Icon;
  readonly UserGroupIcon = UserGroupIcon;
  readonly Calendar03Icon = Calendar03Icon;
  readonly NewsIcon = NewsIcon;
  readonly SchoolIcon = SchoolIcon;
  readonly ArrowLeft01Icon = ArrowLeft01Icon;

  // Academic Services data based on Qassim University website
  readonly academicServices = [
    { key: 'colleges', label: 'الكليات', icon: this.SchoolIcon, active: true },
    { key: 'admission', label: 'القبول', icon: this.File02Icon, active: false },
    { key: 'research', label: 'البحث والابتكار', icon: this.NewsIcon, active: false },
    { key: 'campus-life', label: 'الحياة الجامعية', icon: this.UserGroupIcon, active: false },
    { key: 'graduate-studies', label: 'الدراسات العليا', icon: this.Wallet01Icon, active: false },
    { key: 'health-services', label: 'الخدمات الصحية', icon: this.HierarchySquare01Icon, active: false },
    { key: 'housing', label: 'السكن والإقامة', icon: this.Calendar03Icon, active: false },
    { key: 'library', label: 'المكتبات العلمية', icon: this.ShoppingCart01Icon, active: false }
  ];

  // Quick Access Services
  readonly quickServices = [
    { key: 'student-portal', label: 'البوابة الطلابية', icon: this.UserGroupIcon },
    { key: 'academic-calendar', label: 'التقويم الأكاديمي', icon: this.Calendar03Icon },
    { key: 'grades', label: 'النتائج والدرجات', icon: this.Wallet01Icon },
    { key: 'schedule', label: 'الجداول الدراسية', icon: this.File02Icon }
  ];

  // Signals for reactive data
  homeData = signal<HomePageData | null>(null);
  loading = signal<boolean>(true);
  error = signal<string | null>(null);

  // Auth state
  readonly isAuthenticated = this.authService.isAuthenticated;
  readonly currentLanguage = this.languageService.currentLanguage;

  // Splide instances
  private splideInstances: Splide[] = [];
  private subscription?: Subscription;

  // Splide configuration
  readonly splideOptions: SplideOptions = {
    type: 'loop',
    perPage: 1,
    perMove: 1,
    gap: '1rem',
    autoplay: true,
    interval: 5000,
    pauseOnHover: true,
    pauseOnFocus: true,
    arrows: false,
    pagination: true,
    direction: this.currentLanguage().direction === 'rtl' ? 'rtl' : 'ltr'
  };

  constructor() {}

  ngOnInit() {
    this.loadHomeData();
  }

  ngOnDestroy() {
    this.destroySplideInstances();
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private loadHomeData() {
    this.loading.set(true);
    this.error.set(null);

    this.subscription = this.homeDataService.getHomePageData().subscribe({
      next: (data) => {
        this.homeData.set(data);
        this.loading.set(false);
        // Initialize Splide after data is loaded and DOM is updated
        setTimeout(() => this.initializeSplideInstances(), 100);
      },
      error: (error) => {
        console.error('Error loading home data:', error);
        this.error.set('Failed to load home page data');
        this.loading.set(false);
      }
    });
  }

  private initializeSplideInstances() {
    this.destroySplideInstances();

    const splideElements = [
      '#hero-splide'
    ];

    splideElements.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) {
        try {
          const splide = new Splide(selector, this.splideOptions);
          splide.mount();
          this.splideInstances.push(splide);
        } catch (error) {
          console.warn(`Failed to initialize Splide for ${selector}:`, error);
        }
      }
    });
  }

  private destroySplideInstances() {
    this.splideInstances.forEach(splide => {
      try {
        splide.destroy();
      } catch (error) {
        console.warn('Error destroying Splide instance:', error);
      }
    });
    this.splideInstances = [];
  }

  // Navigation methods
  onLoginClick() {
    // Navigate to login or show login modal
    this.router.navigate(['/login']);
  }

  onAnnouncementClick(announcement: HomeAnnouncement) {
    // Navigate to announcement details
    this.router.navigate(['/announcements', announcement.id]);
  }

  onNewsClick(news: NewsItem) {
    // Navigate to news details
    this.router.navigate(['/news', news.id]);
  }

  onAchievementClick(achievement: Achievement) {
    // Navigate to achievement details
    this.router.navigate(['/achievements', achievement.id]);
  }

  onCalendarEventClick(event: CalendarEvent) {
    // Navigate to calendar event details
    this.router.navigate(['/calendar', event.id]);
  }

  onUniversityInfoClick(info: UniversityInfo) {
    // Navigate to university info details
    this.router.navigate(['/university-info', info.id]);
  }

  onCollegeClick(college: College) {
    // Navigate to college details
    this.router.navigate(['/colleges', college.id]);
  }

  onViewAllClick(section: string) {
    // Navigate to full section view
    this.router.navigate([`/${section}`]);
  }

  onServiceClick(service: string) {
    // Navigate to specific service
    console.log('Service clicked:', service);
    // You can add navigation logic here based on the service
    switch(service) {
      case 'academic':
        this.router.navigate(['/academic-services']);
        break;
      case 'registration':
        this.router.navigate(['/registration']);
        break;
      case 'schedule':
        this.router.navigate(['/schedule']);
        break;
      case 'grades':
        this.router.navigate(['/grades']);
        break;
      case 'library':
        this.router.navigate(['/library']);
        break;
      case 'facilities':
        this.router.navigate(['/facilities']);
        break;
      case 'support':
        this.router.navigate(['/student-support']);
        break;
      case 'research':
        this.router.navigate(['/research']);
        break;
      default:
        console.log('Service not implemented:', service);
    }
  }

  onRetry() {
    this.loadHomeData();
  }

  // Utility methods
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString(this.currentLanguage().code === 'ar' ? 'ar-SA' : 'en-US');
  }

  getEventTypeTranslation(type: string): string {
    return `HOME.EVENT_TYPE.${type.toUpperCase()}`;
  }

  getAchievementTypeTranslation(type: string): string {
    return `HOME.ACHIEVEMENT_TYPE.${type.toUpperCase()}`;
  }

  // Handle image load errors
  onImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.style.display = 'none';
    }
  }
}
