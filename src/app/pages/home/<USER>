<ion-content class="home-content">
  <!-- Loading State -->
  <div *ngIf="loading()" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'HOME.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error() && !loading()" class="error-container">
    <ion-icon name="alert-circle-outline" size="large"></ion-icon>
    <h3>{{ 'HOME.ERROR' | translate }}</h3>
    <p>{{ error() }}</p>
    <ion-button fill="outline" (click)="onRetry()">
      {{ 'HOME.RETRY' | translate }}
    </ion-button>
  </div>

  <!-- Main Content -->
  <div *ngIf="homeData() && !loading() && !error()" class="home-container">

    <!-- Hero Banner Section -->
    <div class="hero-banner">
      <div class="splide hero-splide" id="hero-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li class="splide__slide">
              <div class="hero-card quran-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=100&h=100&fit=crop&crop=center" alt="القرآن الكريم" />
                  </div>
                  <div class="hero-text">
                    <h3>المسابقة السنوية</h3>
                    <p>لحفظ القرآن الكريم</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    سجل الآن
                  </ion-button>
                </div>
              </div>
            </li>
            <li class="splide__slide">
              <div class="hero-card scholarship-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=100&h=100&fit=crop&crop=center" alt="المنح الدراسية" />
                  </div>
                  <div class="hero-text">
                    <h3>المنح الدراسية</h3>
                    <p>للطلاب المتفوقين</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    اعرف المزيد
                  </ion-button>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Services Grid Section -->
    <div class="services-section">
      <div class="section-header">
        <h2 class="section-title">منتجات الصندوق وخدماته</h2>
        <ion-button fill="clear" size="small" class="view-all-button">
          عرض الكل
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="services-grid">
        <div class="service-item" (click)="onServiceClick('documents')">
          <div class="service-icon">
            <hugeicons-file-02 variant="stroke" size="24"></hugeicons-file-02>
          </div>
          <span class="service-label">الوثائق</span>
        </div>

        <div class="service-item" (click)="onServiceClick('organizational')">
          <div class="service-icon">
            <hugeicons-hierarchy-square-01 variant="stroke" size="24"></hugeicons-hierarchy-square-01>
          </div>
          <span class="service-label">الهيكل التنظيمي</span>
        </div>

        <div class="service-item active" (click)="onServiceClick('ecommerce')">
          <div class="service-icon">
            <hugeicons-shopping-cart-01 variant="stroke" size="24"></hugeicons-shopping-cart-01>
          </div>
          <span class="service-label">التجارة الإلكترونية</span>
        </div>

        <div class="service-item" (click)="onServiceClick('activities')">
          <div class="service-icon">
            <hugeicons-running variant="stroke" size="24"></hugeicons-running>
          </div>
          <span class="service-label">كليا نشاطي</span>
        </div>

        <div class="service-item" (click)="onServiceClick('admin')">
          <div class="service-icon">
            <hugeicons-user-group variant="stroke" size="24"></hugeicons-user-group>
          </div>
          <span class="service-label">الهيكل الإداري</span>
        </div>

        <div class="service-item" (click)="onServiceClick('reservations')">
          <div class="service-icon">
            <hugeicons-calendar-03 variant="stroke" size="24"></hugeicons-calendar-03>
          </div>
          <span class="service-label">حجز القاعة</span>
        </div>

        <div class="service-item" (click)="onServiceClick('publications')">
          <div class="service-icon">
            <hugeicons-news variant="stroke" size="24"></hugeicons-news>
          </div>
          <span class="service-label">الإصدارات</span>
        </div>

        <div class="service-item" (click)="onServiceClick('programs')">
          <div class="service-icon">
            <hugeicons-school variant="stroke" size="24"></hugeicons-school>
          </div>
          <span class="service-label">البرامج والأنشطة</span>
        </div>
      </div>
    </div>

    <!-- Programs and Activities Section -->
    <div class="programs-section">
      <div class="section-header">
        <h2 class="section-title">البرامج والأنشطة</h2>
        <ion-button fill="clear" size="small" class="view-all-button">
          عرض الكل
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="programs-grid">
        <div class="program-card">
          <div class="program-date">
            <div class="day">14</div>
            <div class="month">September</div>
          </div>
          <div class="program-location">JEDDAH</div>
          <div class="program-time">1 pm - 4 pm</div>
          <div class="program-title">فعاليات معارض الكتب</div>
          <div class="program-subtitle">المعرض مفتوح</div>
          <ion-button fill="outline" size="small" class="program-button">
            معلومات إضافية
          </ion-button>
        </div>

        <div class="program-card">
          <div class="program-date">
            <div class="day">15</div>
            <div class="month">September</div>
          </div>
          <div class="program-location">RIYADH</div>
          <div class="program-time">2 pm - 5 pm</div>
          <div class="program-title">فعاليات معارض الكتب</div>
          <div class="program-subtitle">المعرض مفتوح</div>
          <ion-button fill="outline" size="small" class="program-button">
            معلومات إضافية
          </ion-button>
        </div>
      </div>
    </div>

    <!-- News Section -->
    <section class="home-section news-section">
      <div class="section-header">
        <h2 class="section-title">أهم الأخبار</h2>
        <ion-button fill="clear" size="small" class="view-all-button">
          عرض الكل
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="announcements-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let announcement of homeData()?.announcements" class="splide__slide">
              <ion-card
                class="announcement-card"
                [class.important]="announcement.important"
                (click)="onAnnouncementClick(announcement)">
                <div *ngIf="announcement.imageUrl" class="card-image">
                  <img
                    [src]="announcement.imageUrl"
                    [alt]="announcement.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge
                      *ngIf="announcement.important"
                      color="danger"
                      class="important-badge">
                      {{ 'HOME.IMPORTANT' | translate }}
                    </ion-badge>
                    <ion-badge color="medium" class="category-badge">
                      {{ announcement.category }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ announcement.title }}</h3>
                  <p class="card-content">{{ announcement.content }}</p>
                  <div class="card-meta">
                    <span class="card-author">{{ announcement.author }}</span>
                    <span class="card-date">{{ formatDate(announcement.date) }}</span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="home-section news-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.NEWS_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('news')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="news-list">
        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop&crop=center" alt="اعتماد الخطة الاستراتيجية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">اعتماد الخطة الاستراتيجية لوقفية جامعة الملك عبدالعزيز</h3>
            <p class="news-summary">صدرت موافقة مجلس الوقف الأعلى على الخطة الاستراتيجية للوقفية للأعوام القادمة...</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=300&h=200&fit=crop&crop=center" alt="وزيرة الخارجية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">وزيرة الخارجية تبحث مع الدول عن التعاون...</h3>
            <p class="news-summary">استمرت وزيرة الخارجية بحثها مع الدول الشقيقة والصديقة حول سبل تعزيز التعاون...</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&crop=center" alt="دراسة استطلاعية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">دراسة استطلاعية تؤكد تعزيز الوقفية الثقافية لدور الرجال السعودي</h3>
            <p class="news-summary">أكدت دراسة استطلاعية حديثة أن الوقفية الثقافية تلعب دوراً مهماً في تعزيز...</p>
          </div>
        </div>
      </div>
    </section>

  </div>
</ion-content>