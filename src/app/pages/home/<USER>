

// ==========================================
// Home Component Styles - Mobile First
// ==========================================

.home-content {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  background: #f8f9fa;
}

// ==========================================
// Loading and Error States
// ==========================================

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 50vh;
  text-align: center;

  ion-spinner {
    margin-bottom: 1rem;
    --color: var(--ion-color-primary);
  }

  ion-icon {
    margin-bottom: 1rem;
    color: var(--ion-color-danger);
  }

  h3 {
    margin: 0.5rem 0;
    color: var(--ion-color-dark);
    font-weight: 600;
  }

  p {
    margin: 0.5rem 0 1.5rem 0;
    color: var(--ion-color-medium);
    max-width: 300px;
  }
}

// ==========================================
// Main Container
// ==========================================

.home-container {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

// ==========================================
// Hero Banner
// ==========================================

.hero-banner {
  padding: 1rem;
  background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
  margin-bottom: 1rem;

  .hero-splide {
    .splide__track {
      border-radius: 16px;
      overflow: hidden;
    }

    .splide__slide {
      padding: 0;
    }

    .splide__pagination {
      bottom: 1rem;

      .splide__pagination__page {
        background: rgba(255, 255, 255, 0.5);

        &.is-active {
          background: white;
        }
      }
    }
  }

  .hero-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-height: 140px;
    display: flex;
    align-items: center;

    &.quran-card {
      background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
      color: white;
    }

    &.scholarship-card {
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      color: white;
    }
  }

  .hero-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
  }

  .hero-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .hero-text {
    flex: 1;

    h3 {
      margin: 0 0 0.25rem 0;
      font-size: 1.1rem;
      font-weight: 700;
      line-height: 1.3;
    }

    p {
      margin: 0;
      font-size: 0.9rem;
      opacity: 0.9;
      line-height: 1.4;
    }
  }

  .hero-button {
    --background: rgba(255, 255, 255, 0.2);
    --color: white;
    --border-radius: 8px;
    --padding-start: 1rem;
    --padding-end: 1rem;
    height: 32px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

// ==========================================
// Services Section
// ==========================================

.services-section {
  padding: 1rem;
  background: white;
  margin-bottom: 1rem;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 700;
      color: #1a1a1a;
    }

    .view-all-button {
      --color: #007bff;
      font-size: 0.85rem;
      font-weight: 600;
    }
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem 0.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      background: #f8f9fa;
      transform: translateY(-2px);
    }

    &.active {
      background: #e8f5e8;

      .service-icon {
        background: #2d5a27;
        color: white;
      }
    }

    .service-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;

      hugeicons-file-02,
      hugeicons-hierarchy-square-01,
      hugeicons-shopping-cart-01,
      hugeicons-running,
      hugeicons-user-group,
      hugeicons-calendar-03,
      hugeicons-news,
      hugeicons-school {
        color: #666;
        transition: color 0.3s ease;
      }
    }

    .service-label {
      font-size: 0.75rem;
      font-weight: 600;
      color: #333;
      line-height: 1.2;
    }
  }
}

// ==========================================
// Programs Section
// ==========================================

.programs-section {
  padding: 1rem;
  background: white;
  margin-bottom: 1rem;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 700;
      color: #1a1a1a;
    }

    .view-all-button {
      --color: #007bff;
      font-size: 0.85rem;
      font-weight: 600;
    }
  }

  .programs-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .program-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ff6b6b, #feca57);
    }

    .program-date {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 0.5rem;

      .day {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        line-height: 1;
      }

      .month {
        font-size: 0.7rem;
        font-weight: 600;
        color: #666;
        text-transform: uppercase;
      }
    }

    .program-location {
      font-size: 0.8rem;
      font-weight: 700;
      color: #007bff;
      margin-bottom: 0.25rem;
    }

    .program-time {
      font-size: 0.75rem;
      color: #666;
      margin-bottom: 0.5rem;
    }

    .program-title {
      font-size: 0.9rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 0.25rem;
      line-height: 1.3;
    }

    .program-subtitle {
      font-size: 0.8rem;
      color: #666;
      margin-bottom: 0.75rem;
    }

    .program-button {
      --border-color: #007bff;
      --color: #007bff;
      --border-radius: 6px;
      height: 28px;
      font-size: 0.7rem;
      font-weight: 600;
    }
  }
}

// ==========================================
// News Section
// ==========================================

.news-section {
  padding: 1rem;
  background: white;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 700;
      color: #1a1a1a;
    }

    .view-all-button {
      --color: #007bff;
      font-size: 0.85rem;
      font-weight: 600;
    }
  }

  .news-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .news-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #e9ecef;
      transform: translateY(-1px);
    }

    .news-image {
      width: 80px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .news-content {
      flex: 1;

      .news-title {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        font-weight: 700;
        color: #333;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .news-summary {
        margin: 0;
        font-size: 0.8rem;
        color: #666;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}

// ==========================================
// Splide Customization
// ==========================================

.splide {
  padding: 0 1.25rem 1.5rem 1.25rem;

  .splide__track {
    overflow: visible;
    border-radius: 12px;
  }

  .splide__list {
    align-items: stretch;
  }

  .splide__slide {
    display: flex;
    align-items: stretch;
    padding: 0 0.5rem;
  }

  // Custom arrow styles
  .splide__arrow {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    opacity: 0.9;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);

    &:hover {
      opacity: 1;
      transform: scale(1.05);
      background: rgba(0, 0, 0, 0.8);
    }

    &:disabled {
      opacity: 0.3;
    }

    svg {
      fill: white;
      width: 14px;
      height: 14px;
    }
  }

  .splide__arrow--prev {
    left: 1rem;
    z-index: 2;
  }

  .splide__arrow--next {
    right: 1rem;
    z-index: 2;
  }

  // Custom pagination styles
  .splide__pagination {
    bottom: -1rem;
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 0.25rem;

    .splide__pagination__page {
      background: #d1d5db;
      border: none;
      border-radius: 50%;
      width: 6px;
      height: 6px;
      margin: 0;
      transition: all 0.3s ease;

      &.is-active {
        background: #007bff;
        transform: scale(1.3);
      }
    }
  }
}

// ==========================================
// Card Base Styles
// ==========================================

.announcement-card,
.news-card,
.achievement-card,
.calendar-card,
.university-info-card,
.college-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #e0e0e0;
  }

  &:active {
    transform: translateY(0) scale(0.99);
  }

  .card-image {
    position: relative;
    height: 140px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }

  ion-card-content {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .card-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    margin-bottom: 0.75rem;

    ion-badge {
      font-size: 0.7rem;
      font-weight: 600;
      padding: 0.2rem 0.6rem;
      border-radius: 8px;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }
  }

  .card-title {
    margin: 0 0 0.5rem 0;
    font-size: 0.95rem;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .card-content,
  .card-summary,
  .card-description {
    margin: 0 0 0.75rem 0;
    color: #6b7280;
    line-height: 1.5;
    font-size: 0.8rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
  }

  .card-meta {
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #9ca3af;

    .card-author,
    .card-date {
      font-weight: 500;
    }

    .card-date {
      color: #6b7280;
    }
  }
}

// ==========================================
// Specific Card Styles
// ==========================================

.announcement-card {
  &.important {
    border-left: 3px solid #dc3545;

    .card-title {
      color: #dc3545;
    }
  }

  .important-badge {
    background: #dc3545 !important;
    color: white !important;
    animation: pulse 2s infinite;
  }
}

.calendar-card {
  &.important {
    border-left: 3px solid #ffc107;
  }

  .event-dates {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    color: #1a1a1a;
    font-size: 0.75rem;
  }

  .event-location {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
    font-size: 0.7rem;
    color: #6b7280;

    ion-icon {
      font-size: 0.8rem;
    }
  }
}

.college-card {
  .college-stats {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #f3f4f6;

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.375rem;
      font-size: 0.7rem;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-label {
        color: #9ca3af;
        font-weight: 500;
      }

      .stat-value {
        color: #1a1a1a;
        font-weight: 600;
      }
    }
  }
}

// ==========================================
// Animations
// ==========================================

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// ==========================================
// Responsive Design
// ==========================================

@media (min-width: 768px) {
  .home-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .hero-banner {
    padding: 1.5rem;
  }

  .services-section,
  .programs-section,
  .news-section {
    padding: 1.5rem;
  }

  .services-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  .programs-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .news-item {
    .news-image {
      width: 120px;
      height: 80px;
    }
  }
}

@media (min-width: 1024px) {
  .home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .splide {
    .splide__slide {
      max-width: 400px;
    }
  }
}

// ==========================================
// Dark Mode Support
// ==========================================

@media (prefers-color-scheme: dark) {
  .home-content {
    background: #1a1a1a;
  }

  .home-container {
    background: #1a1a1a;
  }

  .services-section,
  .programs-section,
  .news-section {
    background: #2d2d2d;

    .section-title {
      color: #ffffff;
    }
  }

  .service-item {
    &:hover {
      background: #404040;
    }

    &.active {
      background: #2d5a27;
    }

    .service-icon {
      background: #404040;

      ion-icon {
        color: #ffffff;
      }
    }

    .service-label {
      color: #ffffff;
    }
  }

  .program-card {
    background: #404040;

    .program-title,
    .day {
      color: #ffffff;
    }

    .month,
    .program-time,
    .program-subtitle {
      color: #b0b0b0;
    }
  }

  .news-item {
    background: #404040;

    &:hover {
      background: #505050;
    }

    .news-title {
      color: #ffffff;
    }

    .news-summary {
      color: #b0b0b0;
    }
  }
}

// ==========================================
// RTL Support
// ==========================================

[dir="rtl"] {
  .splide__arrow--prev {
    right: 0.5rem;
    left: auto;
  }

  .splide__arrow--next {
    left: 0.5rem;
    right: auto;
  }

  .card-meta {
    .card-author,
    .card-date {
      text-align: right;
    }
  }

  .event-location {
    ion-icon {
      margin-inline-start: 0;
      margin-inline-end: 0.25rem;
    }
  }
}
