<ion-content class="home-content">
  <!-- Loading State -->
  <div *ngIf="loading()" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'HOME.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error() && !loading()" class="error-container">
    <ion-icon name="alert-circle-outline" size="large"></ion-icon>
    <h3>{{ 'HOME.ERROR' | translate }}</h3>
    <p>{{ error() }}</p>
    <ion-button fill="outline" (click)="onRetry()">
      {{ 'HOME.RETRY' | translate }}
    </ion-button>
  </div>

  <!-- Main Content -->
  <div *ngIf="homeData() && !loading() && !error()" class="home-container">

    <!-- Hero Banner Section -->
    <div class="hero-banner">
      <div class="splide hero-splide" id="hero-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li class="splide__slide">
              <div class="hero-card quran-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=100&h=100&fit=crop&crop=center" alt="القرآن الكريم" />
                  </div>
                  <div class="hero-text">
                    <h3>التسجيل مفتوح</h3>
                    <p>للفصل الدراسي الجديد</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    سجل الآن
                  </ion-button>
                </div>
              </div>
            </li>
            <li class="splide__slide">
              <div class="hero-card scholarship-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=100&h=100&fit=crop&crop=center" alt="المنح الدراسية" />
                  </div>
                  <div class="hero-text">
                    <h3>المنح الدراسية</h3>
                    <p>للطلاب المتفوقين</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    تقدم الآن
                  </ion-button>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Quick Access Services -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>الخدمات السريعة</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="6" *ngFor="let service of quickServices">
              <ion-button
                fill="outline"
                expand="block"
                color="primary"
                (click)="onServiceClick(service.key)"
                class="quick-service-button">
                <div class="service-content">
                  <hugeicons-icon [icon]="service.icon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
                  <ion-label class="service-label">{{ service.label }}</ion-label>
                </div>
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>

    <!-- Academic Services Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>الخدمات الأكاديمية والإدارية</ion-card-title>
        <ion-button fill="clear" size="small" slot="end">
          عرض الكل
          <hugeicons-icon [icon]="ArrowLeft01Icon" size="16" [strokeWidth]="1.5" slot="end"></hugeicons-icon>
        </ion-button>
      </ion-card-header>

      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="3" *ngFor="let service of academicServices">
              <ion-button
                fill="clear"
                expand="block"
                [color]="service.active ? 'success' : 'medium'"
                (click)="onServiceClick(service.key)"
                class="service-button">
                <div class="service-content">
                  <hugeicons-icon [icon]="service.icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                  <ion-label class="service-label">{{ service.label }}</ion-label>
                </div>
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>

    <!-- Programs and Activities Section -->
    <div class="programs-section">
      <div class="section-header">
        <h2 class="section-title">الفعاليات والأنشطة الجامعية</h2>
        <ion-button fill="clear" size="small" class="view-all-button">
          عرض الكل
          <hugeicons-icon [icon]="ArrowLeft01Icon" size="16" [strokeWidth]="1.5" slot="end"></hugeicons-icon>
        </ion-button>
      </div>

      <div class="programs-grid">
        <div class="program-card">
          <div class="program-date">
            <div class="day">14</div>
            <div class="month">September</div>
          </div>
          <div class="program-location">قاعة المؤتمرات</div>
          <div class="program-time">10:00 ص - 12:00 م</div>
          <div class="program-title">ندوة البحث العلمي والابتكار</div>
          <div class="program-subtitle">مفتوحة لجميع الطلاب</div>
          <ion-button fill="outline" size="small" class="program-button">
            التفاصيل والتسجيل
          </ion-button>
        </div>

        <div class="program-card">
          <div class="program-date">
            <div class="day">15</div>
            <div class="month">September</div>
          </div>
          <div class="program-location">المسرح الجامعي</div>
          <div class="program-time">2:00 م - 5:00 م</div>
          <div class="program-title">معرض مشاريع التخرج</div>
          <div class="program-subtitle">عرض مشاريع الطلاب</div>
          <ion-button fill="outline" size="small" class="program-button">
            التفاصيل والتسجيل
          </ion-button>
        </div>
      </div>
    </div>

    <!-- News Section -->
    <section class="home-section news-section">
      <div class="section-header">
        <h2 class="section-title">أخبار الجامعة</h2>
        <ion-button fill="clear" size="small" class="view-all-button">
          عرض الكل
          <hugeicons-icon [icon]="ArrowLeft01Icon" size="16" [strokeWidth]="1.5" slot="end"></hugeicons-icon>
        </ion-button>
      </div>

      <div class="splide" id="announcements-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let announcement of homeData()?.announcements" class="splide__slide">
              <ion-card
                class="announcement-card"
                [class.important]="announcement.important"
                (click)="onAnnouncementClick(announcement)">
                <div *ngIf="announcement.imageUrl" class="card-image">
                  <img
                    [src]="announcement.imageUrl"
                    [alt]="announcement.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge
                      *ngIf="announcement.important"
                      color="danger"
                      class="important-badge">
                      {{ 'HOME.IMPORTANT' | translate }}
                    </ion-badge>
                    <ion-badge color="medium" class="category-badge">
                      {{ announcement.category }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ announcement.title }}</h3>
                  <p class="card-content">{{ announcement.content }}</p>
                  <div class="card-meta">
                    <span class="card-author">{{ announcement.author }}</span>
                    <span class="card-date">{{ formatDate(announcement.date) }}</span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="home-section news-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.NEWS_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('news')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="news-list">
        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop&crop=center" alt="اعتماد الخطة الاستراتيجية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">جامعة الملك عبدالعزيز تحصل على الاعتماد الأكاديمي الدولي</h3>
            <p class="news-summary">حصلت الجامعة على الاعتماد الأكاديمي من المجلس الدولي للتعليم العالي...</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=300&h=200&fit=crop&crop=center" alt="وزيرة الخارجية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">افتتاح مركز الابتكار وريادة الأعمال الجديد</h3>
            <p class="news-summary">افتتحت الجامعة مركزاً جديداً لدعم الطلاب في مجال ريادة الأعمال والابتكار...</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-image">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&crop=center" alt="دراسة استطلاعية" />
          </div>
          <div class="news-content">
            <h3 class="news-title">طلاب الجامعة يحققون المركز الأول في مسابقة البرمجة الدولية</h3>
            <p class="news-summary">حقق فريق من طلاب كلية الحاسب الآلي المركز الأول في المسابقة الدولية للبرمجة...</p>
          </div>
        </div>
      </div>
    </section>

  </div>
</ion-content>