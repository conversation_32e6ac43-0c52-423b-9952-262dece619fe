// Import Splide CSS
@import '@splidejs/splide/css';

// ==========================================
// Home Component Styles - Mobile First
// ==========================================

.home-content {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  background: #f8f9fa;
}

// ==========================================
// Loading and Error States
// ==========================================

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 50vh;
  text-align: center;

  ion-spinner {
    margin-bottom: 1rem;
    --color: var(--ion-color-primary);
  }

  ion-icon {
    margin-bottom: 1rem;
    color: var(--ion-color-danger);
  }

  h3 {
    margin: 0.5rem 0;
    color: var(--ion-color-dark);
    font-weight: 600;
  }

  p {
    margin: 0.5rem 0 1.5rem 0;
    color: var(--ion-color-medium);
    max-width: 300px;
  }
}

// ==========================================
// Main Container
// ==========================================

.home-container {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

// ==========================================
// Login CTA Banner
// ==========================================

.login-cta-banner {
  padding: 1rem;
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);

  .login-cta-card {
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(var(--ion-color-primary-rgb), 0.3);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);

    ion-card-content {
      padding: 1.5rem;
    }
  }

  .login-cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
  }

  .login-cta-text {
    flex: 1;

    h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--ion-color-dark);
    }

    p {
      margin: 0;
      color: var(--ion-color-medium);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .login-cta-button {
    --border-radius: 12px;
    --padding-start: 1.5rem;
    --padding-end: 1.5rem;
    height: 44px;
    font-weight: 600;

    @media (max-width: 768px) {
      width: 100%;
      margin-top: 1rem;
    }
  }
}

// ==========================================
// Section Styles
// ==========================================

.home-section {
  margin-bottom: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  &:last-child {
    margin-bottom: 2rem;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.25rem 0.75rem 1.25rem;
  background: white;

  .section-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #1a1a1a;
    flex: 1;
    line-height: 1.3;
  }

  .view-all-button {
    --color: #007bff;
    --padding-start: 0.75rem;
    --padding-end: 0.75rem;
    --border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    height: 32px;

    ion-icon {
      margin-inline-start: 0.25rem;
      font-size: 0.9rem;
    }
  }
}

// ==========================================
// Splide Customization
// ==========================================

.splide {
  padding: 0 1.25rem 1.5rem 1.25rem;

  .splide__track {
    overflow: visible;
    border-radius: 12px;
  }

  .splide__list {
    align-items: stretch;
  }

  .splide__slide {
    display: flex;
    align-items: stretch;
    padding: 0 0.5rem;
  }

  // Custom arrow styles
  .splide__arrow {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    opacity: 0.9;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);

    &:hover {
      opacity: 1;
      transform: scale(1.05);
      background: rgba(0, 0, 0, 0.8);
    }

    &:disabled {
      opacity: 0.3;
    }

    svg {
      fill: white;
      width: 14px;
      height: 14px;
    }
  }

  .splide__arrow--prev {
    left: 1rem;
    z-index: 2;
  }

  .splide__arrow--next {
    right: 1rem;
    z-index: 2;
  }

  // Custom pagination styles
  .splide__pagination {
    bottom: -1rem;
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 0.25rem;

    .splide__pagination__page {
      background: #d1d5db;
      border: none;
      border-radius: 50%;
      width: 6px;
      height: 6px;
      margin: 0;
      transition: all 0.3s ease;

      &.is-active {
        background: #007bff;
        transform: scale(1.3);
      }
    }
  }
}

// ==========================================
// Card Base Styles
// ==========================================

.announcement-card,
.news-card,
.achievement-card,
.calendar-card,
.university-info-card,
.college-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #e0e0e0;
  }

  &:active {
    transform: translateY(0) scale(0.99);
  }

  .card-image {
    position: relative;
    height: 140px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.02);
    }
  }

  ion-card-content {
    padding: 1.25rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .card-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;

    ion-badge {
      font-size: 0.75rem;
      font-weight: 500;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
    }
  }

  .card-title {
    margin: 0 0 0.75rem 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--ion-color-dark);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .card-content,
  .card-summary,
  .card-description {
    margin: 0 0 1rem 0;
    color: var(--ion-color-medium-shade);
    line-height: 1.5;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
  }

  .card-meta {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--ion-color-light-shade);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--ion-color-medium);

    .card-author,
    .card-date {
      font-weight: 500;
    }

    .card-date {
      color: var(--ion-color-medium-shade);
    }
  }
}

// ==========================================
// Specific Card Styles
// ==========================================

.announcement-card {
  &.important {
    border-left: 4px solid var(--ion-color-danger);

    .card-title {
      color: var(--ion-color-danger-shade);
    }
  }

  .important-badge {
    animation: pulse 2s infinite;
  }
}

.calendar-card {
  &.important {
    border-left: 4px solid var(--ion-color-warning);
  }

  .event-dates {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    color: var(--ion-color-dark);
  }

  .event-location {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.5rem;

    ion-icon {
      font-size: 0.9rem;
    }
  }
}

.college-card {
  .college-stats {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--ion-color-light-shade);

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
      font-size: 0.85rem;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-label {
        color: var(--ion-color-medium);
        font-weight: 500;
      }

      .stat-value {
        color: var(--ion-color-dark);
        font-weight: 600;
      }
    }
  }
}

// ==========================================
// Animations
// ==========================================

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// ==========================================
// Responsive Design
// ==========================================

@media (min-width: 768px) {
  .home-container {
    padding: 0 1rem;
  }

  .login-cta-banner {
    padding: 1.5rem;
  }

  .section-header {
    padding: 2rem 1.5rem 1.5rem 1.5rem;

    .section-title {
      font-size: 1.5rem;
    }
  }

  .splide {
    padding: 1.5rem;

    .splide__arrow {
      width: 48px;
      height: 48px;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  .announcement-card,
  .news-card,
  .achievement-card,
  .calendar-card,
  .university-info-card,
  .college-card {
    .card-image {
      height: 200px;
    }

    ion-card-content {
      padding: 1.5rem;
    }

    .card-title {
      font-size: 1.2rem;
    }

    .card-content,
    .card-summary,
    .card-description {
      font-size: 1rem;
    }
  }
}

@media (min-width: 1024px) {
  .home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .splide {
    .splide__slide {
      max-width: 400px;
    }
  }
}

// ==========================================
// Dark Mode Support
// ==========================================

@media (prefers-color-scheme: dark) {
  .home-container {
    background: var(--ion-color-dark);
  }

  .home-section {
    background: var(--ion-color-step-50);
  }

  .login-cta-card {
    background: rgba(var(--ion-color-step-100-rgb), 0.95) !important;
  }

  .announcement-card,
  .news-card,
  .achievement-card,
  .calendar-card,
  .university-info-card,
  .college-card {
    background: var(--ion-color-step-50);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }
  }
}

// ==========================================
// RTL Support
// ==========================================

[dir="rtl"] {
  .splide__arrow--prev {
    right: 0.5rem;
    left: auto;
  }

  .splide__arrow--next {
    left: 0.5rem;
    right: auto;
  }

  .card-meta {
    .card-author,
    .card-date {
      text-align: right;
    }
  }

  .event-location {
    ion-icon {
      margin-inline-start: 0;
      margin-inline-end: 0.25rem;
    }
  }
}
