<ion-header [translucent]="true"
            style="background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);">
  <ion-toolbar>
    <ion-title>{{ 'جامعة القصيم' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="home-content">
  <ion-header collapse="condense" >
    <ion-toolbar style="background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);">
      <ion-title color="light" style="background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);" class="ion-text-start ion-no-padding ion-padding-start"
                 slot="start">{{ 'مرحبا بك في جامعة القصيم' | translate }}
      </ion-title>
    </ion-toolbar>
  </ion-header>
  <!-- Loading State -->
  <div *ngIf="loading()" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'HOME.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error() && !loading()" class="error-container">
    <ion-icon name="alert-circle-outline" size="large"></ion-icon>
    <h3>{{ 'HOME.ERROR' | translate }}</h3>
    <p>{{ error() }}</p>
    <ion-button fill="outline" (click)="onRetry()">
      {{ 'HOME.RETRY' | translate }}
    </ion-button>
  </div>

  <!-- Main Content -->
  <div *ngIf="homeData() && !loading() && !error()" class="home-wrapper">

    <!-- Custom Header -->
    <div class="custom-header">
      <div class="header-background">
        <!-- Decorative circles -->
        <div class="header-decoration">
          <div class="circle-1"></div>
          <div class="circle-2"></div>
        </div>

        <!-- Hero cards container -->
        <div class="hero-cards-container">
          <div class="splide hero-splide" id="hero-splide">
            <div class="splide__track">
              <ul class="splide__list">
                <li class="splide__slide" *ngFor="let oneAd of ads">
                  <div class="hero-card" [ngClass]="{'university-card': true}">
                    <div class="card-content">
                      <div class="card-text">
                        <div class="card-title">{{oneAd.title || 'Untitled'}}</div>
                        <div class="card-subtitle" *ngIf="oneAd.subtitle">{{oneAd.subtitle | truncate:50}}</div>
                      </div>
                      <div class="card-image">
                        <img [src]="oneAd.image" [alt]="oneAd.title"
                             onerror="this.src='assets/default-placeholder.png'">
                      </div>
                    </div>
                    <div class="card-button" *ngIf="oneAd.cta && oneAd.link">
                      <ion-button fill="solid" size="small" class="register-button"
                                  [routerLink]="oneAd.link.startsWith('http') ? null : oneAd.link"
                                  [href]="oneAd.link.startsWith('http') ? oneAd.link : null">
                        {{oneAd.cta}}
                      </ion-button>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div class="splide__pagination hero-pagination"></div>
          </div>
        </div>

      </div>
    </div>

    <!-- Vision & Mission Section -->
    <div class="vision-mission-section">
      <div class="splide vision-mission-splide" id="vision-mission-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li class="splide__slide">
              <ion-card color="primary">
                <ion-card-content>
                  <div class="vision-mission-content">
                    <div class="vision-mission-icon">
                      <hugeicons-icon [icon]="NewsIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                    </div>
                    <div class="vision-mission-text">
                      <h3>رؤية الجامعة</h3>
                      <p>أن تكون جامعة القصيم جامعة رائدة في التعليم والبحث العلمي وخدمة المجتمع محلياً وإقليمياً</p>
                    </div>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
            <li class="splide__slide">
              <ion-card color="success">
                <ion-card-content>
                  <div class="vision-mission-content">
                    <div class="vision-mission-icon">
                      <hugeicons-icon [icon]="SchoolIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                    </div>
                    <div class="vision-mission-text">
                      <h3>رسالة الجامعة</h3>
                      <p>تقديم تعليم عالي الجودة وإجراء بحوث علمية متميزة وخدمة المجتمع في بيئة أكاديمية محفزة للإبداع والابتكار</p>
                    </div>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Main Content Container -->
    <div class="main-content">

      <!-- Services Section -->
      <div *ngIf="false" class="services-section">
        <div class="section-header">
          <h2>جامعة القصيم</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الزيد
          </ion-button>
        </div>

        <div class="services-grid">
          <div *ngFor="let oneAcademicService of academicServices"
               class="service-item"
               (click)="onServiceClick('electronic-store')">
            <div class="service-icon ">
              <hugeicons-icon [icon]="oneAcademicService.icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
            </div>
            <span class="service-label">{{ oneAcademicService.label }}</span>
          </div>
        </div>
      </div>

      <!-- Programs and Activities Section -->
      <div class="programs-section">
        <div class="section-header">
          <h2>الإنجازات</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الزيد
          </ion-button>
        </div>

        <div class="programs-grid">
          <div class="program-card riyadh-card">
            <div class="program-image">
              <img src="https://images.unsplash.com/photo-1580418827493-f2b22c0a76cb?w=400&h=200&fit=crop&crop=center"
                   alt="الرياض"/>
              <div class="program-overlay">
                <div class="program-location">RIYADH</div>
                <div class="program-title">فعاليات معارض الكتب</div>
                <div class="program-details">
                  <div class="program-date">
                    <ion-icon name="calendar-outline"></ion-icon>
                    <span>Saturday 14 September</span>
                  </div>
                  <div class="program-time">1 pm - 4 pm</div>
                </div>
                <div class="program-actions">
                  <span class="participant-count">
                    <ion-icon name="people-outline"></ion-icon>
                    12
                  </span>
                  <ion-button fill="outline" size="small" color="light">
                    شاهد التفاصيل
                  </ion-button>
                </div>
              </div>
            </div>
          </div>

          <div class="program-card jeddah-card">
            <div class="program-image">
              <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=200&fit=crop&crop=center"
                   alt="جدة"/>
              <div class="program-overlay">
                <div class="program-location">JEDDAH</div>
                <div class="program-title">فعاليات معارض الكتب</div>
                <div class="program-details">
                  <div class="program-date">
                    <ion-icon name="calendar-outline"></ion-icon>
                    <span>Saturday 14 September</span>
                  </div>
                  <div class="program-time">1 pm - 4 pm</div>
                </div>
                <div class="program-actions">
                  <span class="participant-count">
                    <ion-icon name="people-outline"></ion-icon>
                    12
                  </span>
                  <ion-button fill="outline" size="small" color="light">
                    شاهد التفاصيل
                  </ion-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- News Section -->
      <div class="news-section">
        <div class="section-header">
          <h2>أهم الأخبار</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الزيد
          </ion-button>
        </div>

        <div class="news-list">
          <div class="news-item" (click)="onNewsClick(1)">
            <div class="news-content">
              <h3>اعتماد الخطة الاستراتيجية لوثيقة بناء الجسور في مكة</h3>
              <p>شهدت النسخة الثانية لتدشين موسوعة الوقف الفكري الإسلامي</p>
            </div>
            <div class="news-image">
              <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=120&h=80&fit=crop&crop=center"
                   alt="خبر"/>
            </div>
          </div>

          <div class="news-item" (click)="onNewsClick(2)">
            <div class="news-content">
              <h3>وزير الخارجية السعودي: المملكة مستمرة بعملها مع الدول عبر التحالف</h3>
              <p>قال إنه سيتم رصد جامعة القضية الفلسطينية عبر شعائرها وطوائفها...</p>
            </div>
            <div class="news-image">
              <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=120&h=80&fit=crop&crop=center"
                   alt="خبر"/>
            </div>
          </div>

          <div class="news-item" (click)="onNewsClick(3)">
            <div class="news-content">
              <h3>دراسة استطلاعية تؤكد تعزيز الرؤية الشمولية لرمز الريال السعودي</h3>
              <p>أكد %68 شارك في استطلاع اباي بلدي لإبراعي استطلاع الريب...</p>
            </div>
            <div class="news-image">
              <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=80&fit=crop&crop=center"
                   alt="خبر"/>
              <div class="news-logo">
                <img src="/assets/icon/logo.svg" alt="شعار"/>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Academic Calendar Section -->
      <div class="academic-calendar-section">
        <div class="section-header">
          <h2>التقويم الأكاديمي</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الكل
          </ion-button>
        </div>

        <div class="calendar-grid">
          <ion-card color="success">
            <ion-card-content>
              <div class="calendar-item">
                <div class="calendar-icon">
                  <hugeicons-icon [icon]="Calendar03Icon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="calendar-content">
                  <h4>بداية الفصل الدراسي الأول</h4>
                  <p>بداية الدراسة لجميع الكليات - 15 سبتمبر</p>
                  <ion-note>قريباً</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="warning">
            <ion-card-content>
              <div class="calendar-item">
                <div class="calendar-icon">
                  <hugeicons-icon [icon]="File02Icon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="calendar-content">
                  <h4>آخر موعد للتسجيل</h4>
                  <p>انتهاء فترة التسجيل والحذف والإضافة - 20 سبتمبر</p>
                  <ion-note>مهم</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="primary">
            <ion-card-content>
              <div class="calendar-item">
                <div class="calendar-icon">
                  <hugeicons-icon [icon]="SchoolIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="calendar-content">
                  <h4>بداية الاختبارات النهائية</h4>
                  <p>اختبارات الفصل الدراسي الأول - 25 ديسمبر</p>
                  <ion-note>اختبارات</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>

      <!-- Achievements Section -->
      <div class="achievements-section">
        <div class="section-header">
          <h2>الإنجازات</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الكل
          </ion-button>
        </div>

        <div class="achievements-grid">
          <ion-card color="success">
            <ion-card-content>
              <div class="achievement-item">
                <div class="achievement-icon">
                  <hugeicons-icon [icon]="SchoolIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="achievement-content">
                  <h4>الاعتماد الأكاديمي الدولي</h4>
                  <p>حصلت الجامعة على الاعتماد من المجلس الدولي للتعليم العالي</p>
                  <ion-note>2024</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="primary">
            <ion-card-content>
              <div class="achievement-item">
                <div class="achievement-icon">
                  <hugeicons-icon [icon]="NewsIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="achievement-content">
                  <h4>جائزة التميز في البحث العلمي</h4>
                  <p>فوز الجامعة بجائزة التميز في البحث العلمي على مستوى المملكة</p>
                  <ion-note>2024</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="warning">
            <ion-card-content>
              <div class="achievement-item">
                <div class="achievement-icon">
                  <hugeicons-icon [icon]="UserGroupIcon" size="32" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="achievement-content">
                  <h4>أفضل جامعة في خدمة المجتمع</h4>
                  <p>تصنيف الجامعة كأفضل جامعة في خدمة المجتمع المحلي</p>
                  <ion-note>2023</ion-note>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>

      <!-- Colleges Section -->
      <div class="colleges-section">
        <div class="section-header">
          <h2>الكليات</h2>
          <ion-button fill="clear" size="small" class="view-all-button" color="primary">
            عرض الكل
          </ion-button>
        </div>

        <div class="colleges-grid">
          <ion-card color="primary" button (click)="onCollegeClick('engineering')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="HierarchySquare01Icon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية الهندسة</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="danger" button (click)="onCollegeClick('medicine')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="File02Icon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية الطب</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="secondary" button (click)="onCollegeClick('computer-science')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="ShoppingCart01Icon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية الحاسب الآلي</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="warning" button (click)="onCollegeClick('business')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="Wallet01Icon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية إدارة الأعمال</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="success" button (click)="onCollegeClick('education')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="SchoolIcon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية التربية</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>

          <ion-card color="tertiary" button (click)="onCollegeClick('science')">
            <ion-card-content>
              <div class="college-item">
                <div class="college-icon">
                  <hugeicons-icon [icon]="NewsIcon" size="28" [strokeWidth]="1.5"></hugeicons-icon>
                </div>
                <div class="college-content">
                  <h4>كلية العلوم</h4>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>

    </div>
  </div>
</ion-content>
