import {Component, OnInit} from '@angular/core';
import {IonicModule, ToastController} from '@ionic/angular';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {StudentCardComponent} from '../../components/student-card/student-card.component';
import {HomeActionCardComponent} from '../../components/home-action-card/home-action-card.component';
import {Router} from '@angular/router';
import {FirstNamePipe} from '../../pipes/first-name.pipe';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, TranslateModule, StudentCardComponent, HomeActionCardComponent, FirstNamePipe]
})
export class HomeComponent {
  constructor(
    private router: Router,
    private toastController: ToastController
  ) {
  }

}
