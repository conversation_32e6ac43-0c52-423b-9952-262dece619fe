// ==========================================
// Home Component - Redesigned to Match Attachment
// ==========================================

.home-content {
  --background: #f8f9fa;
}

.home-wrapper {
  min-height: 100vh;
  background: #f8f9fa;
}

// ==========================================
// Custom Header
// ==========================================
.custom-header {
  position: relative;
  margin-bottom: 0;
}

.header-background {
  background: linear-gradient( 135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
  padding: 3rem 1.5rem 4rem 1.5rem;
  position: relative;
  overflow: hidden;
  border-bottom-left-radius: 24px;
  border-bottom-right-radius: 24px;
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;

  .circle-1, .circle-2, .circle-3 {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
  }

  .circle-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -50px;
  }

  .circle-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -30px;
  }

  .circle-3 {
    width: 80px;
    height: 80px;
    top: 50%;
    right: 30%;
    opacity: 0.6;
  }
}

.header-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  .menu-button {
    --color: white;
    --background: rgba(255, 255, 255, 0.15);
    --border-radius: 12px;
    width: 44px;
    height: 44px;
  }
}

.header-center {
  text-align: center;
  color: white;

  .time-display {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .user-greeting {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
  }

  .user-name {
    font-size: 1rem;
    font-weight: 600;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .notification-button {
    --color: white;
    --background: rgba(255, 255, 255, 0.15);
    --border-radius: 12px;
    width: 40px;
    height: 40px;
    position: relative;
  }

  .notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #ffc107;
    border-radius: 50%;
    border: 2px solid white;
  }

  .profile-avatar {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

// ==========================================
// Main Content
// ==========================================
.main-content {
  padding: 1.5rem;
  margin-top: -2rem;
  position: relative;
  z-index: 1;
}

// ==========================================
// Hero Section
// ==========================================
.hero-section {
  margin-bottom: 2rem;

  .hero-splide {
    .splide__track {
      padding: 0;
    }

    .splide__list {
      gap: 1rem;
    }
  }
}

.hero-card {
  background: linear-gradient( 135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &.university-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
  }

  .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .hero-text {
      flex: 1;

      h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        line-height: 1.2;
      }

      h4 {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
      }
    }

    .hero-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      img {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
      }

      &.university-icon {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .hero-button {
    --background: rgba(255, 255, 255, 0.2);
    --color: white;
    --border-radius: 12px;
    --padding-start: 1.5rem;
    --padding-end: 1.5rem;
    height: 36px;
    font-weight: 600;
    align-self: flex-start;
  }
}

.custom-pagination {
  bottom: -2rem !important;

  .splide__pagination__page {
    background: #e0e0e0;
    opacity: 1;
    width: 8px;
    height: 8px;
    margin: 0 4px;

    &.is-active {
      background: #4a7c59;
    }
  }
}

// ==========================================
// Sections
// ==========================================
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  h2 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: #333;
  }

  .view-all-button {
    --color: #4a7c59;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

// ==========================================
// Services Section
// ==========================================
.services-section {
  margin-bottom: 2rem;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.active {
    .service-icon {
      background: #4a7c59;
      color: white;
    }
  }

  .service-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    color: #666;
    transition: all 0.2s ease;

    &.electronic-store {
      background: #4a7c59;
      color: white;
    }
  }

  .service-label {
    font-size: 0.75rem;
    text-align: center;
    line-height: 1.2;
    color: #666;
    font-weight: 500;
  }
}

// ==========================================
// Programs Section
// ==========================================
.programs-section {
  margin-bottom: 2rem;
}

.programs-grid {
  display: grid;
  gap: 1rem;
}

.program-card {
  border-radius: 16px;
  overflow: hidden;
  background: white;

  .program-image {
    position: relative;
    height: 180px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .program-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
      color: white;
      padding: 1rem;

      .program-location {
        font-size: 0.8rem;
        opacity: 0.9;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .program-title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0.25rem 0 0.5rem 0;
      }

      .program-details {
        margin-bottom: 0.75rem;

        .program-date {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.8rem;
          opacity: 0.9;
          margin-bottom: 0.25rem;

          ion-icon {
            font-size: 0.8rem;
          }
        }

        .program-time {
          font-size: 0.8rem;
          opacity: 0.9;
        }
      }

      .program-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .participant-count {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.8rem;
          opacity: 0.9;

          ion-icon {
            font-size: 0.8rem;
          }
        }

        ion-button {
          --border-color: rgba(255, 255, 255, 0.5);
          --color: white;
          height: 32px;
          font-size: 0.75rem;
        }
      }
    }
  }
}

// ==========================================
// News Section
// ==========================================
.news-section {
  margin-bottom: 2rem;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.news-item {
  background: white;
  border-radius: 16px;
  padding: 1rem;
  display: flex;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  .news-content {
    flex: 1;

    h3 {
      font-size: 0.95rem;
      font-weight: 600;
      line-height: 1.4;
      margin: 0 0 0.5rem 0;
      color: #333;
    }

    p {
      font-size: 0.85rem;
      color: #666;
      line-height: 1.4;
      margin: 0;
    }
  }

  .news-image {
    width: 80px;
    height: 60px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .news-logo {
      position: absolute;
      bottom: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 12px;
        height: 12px;
        object-fit: contain;
      }
    }
  }
}

// ==========================================
// Loading and Error States
// ==========================================
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: #666;

  ion-spinner {
    margin-bottom: 1rem;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: #666;

  ion-icon {
    margin-bottom: 1rem;
    color: #e74c3c;
  }

  h3 {
    margin-bottom: 0.5rem;
    color: #333;
  }

  p {
    margin-bottom: 1.5rem;
  }
}

// ==========================================
// Responsive Design
// ==========================================
@media (min-width: 768px) {
  .main-content {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 1.5rem 2rem;
  }

  .services-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .programs-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .header-content {
    .header-center {
      .time-display {
        font-size: 1rem;
      }

      .user-greeting,
      .user-name {
        font-size: 0.8rem;
      }
    }
  }

  .main-content {
    padding: 1rem;
  }

  .hero-card {
    padding: 1.25rem;
    min-height: 140px;

    .hero-content {
      .hero-icon {
        width: 60px;
        height: 60px;

        img {
          width: 35px;
          height: 35px;
        }
      }

      .hero-text {
        h3 {
          font-size: 1rem;
        }

        h4 {
          font-size: 1.2rem;
        }
      }
    }
  }

  .services-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
  }

  .service-item {
    padding: 0.75rem 0.25rem;

    .service-icon {
      width: 40px;
      height: 40px;
    }

    .service-label {
      font-size: 0.7rem;
    }
  }
}
// ==========================================
// Custom Header - Exact Match to Design
// ==========================================
.custom-header {
  position: relative;
  margin-bottom: 0;
}

.header-background {
  background: linear-gradient( 135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
  padding: 0;
  position: relative;
  overflow: hidden;
  border-bottom-left-radius: 32px;
  border-bottom-right-radius: 32px;
  min-height: 420px;
  display: flex;
  flex-direction: column;
}

// Status Bar
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px 8px 24px;
  position: relative;
  z-index: 10;

  .status-left {
    .time {
      color: white;
      font-size: 17px;
      font-weight: 600;
    }
  }

  .status-right {
    display: flex;
    align-items: center;
    gap: 6px;

    .signal-bars {
      display: flex;
      align-items: flex-end;
      gap: 2px;
      height: 12px;

      .bar {
        background: white;
        border-radius: 1px;
        width: 3px;

        &:nth-child(1) { height: 4px; }
        &:nth-child(2) { height: 6px; }
        &:nth-child(3) { height: 8px; }
        &:nth-child(4) { height: 10px; }
      }
    }

    .wifi-icon {
      color: white;
      font-size: 16px;
      margin-left: 2px;
    }

    .battery {
      width: 24px;
      height: 12px;
      border: 1px solid white;
      border-radius: 2px;
      position: relative;
      margin-left: 2px;

      &::after {
        content: '';
        position: absolute;
        right: -3px;
        top: 3px;
        width: 2px;
        height: 6px;
        background: white;
        border-radius: 0 1px 1px 0;
      }

      .battery-level {
        background: white;
        height: 100%;
        width: 90%;
        border-radius: 1px;
      }
    }
  }
}

// Decorative circles
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;

  .circle-1 {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.08);
    top: 130px;
    right: -100px;
  }

  .circle-2 {
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.06);
    bottom: 100px;
    left: -60px;
  }
}

// Header main content
.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px 20px 24px;
  position: relative;
  z-index: 5;

  .header-left {
    .profile-avatar {
      width: 50px;
      height: 50px;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }
  }

  .header-center {
    text-align: center;
    color: white;
    flex: 1;

    .greeting-text {
      font-size: 16px;
      font-weight: 400;
      margin-bottom: 4px;
      opacity: 0.9;
    }

    .user-name {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .header-right {
    .notification-container {
      position: relative;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;

      .notification-icon {
        color: white;
        font-size: 24px;
      }

      .notification-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        width: 8px;
        height: 8px;
        background: #ffc107;
        border-radius: 50%;
        border: 2px solid white;
      }
    }
  }
}

// Hero cards container
.hero-cards-container {
  flex: 1;
  position: relative;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin: auto;
  max-width: 600px;

  .hero-splide {
    .splide__track {
      padding-bottom: 40px;
    }

    .splide__list {
      display: flex;
      gap: 16px;
    }

    .splide__slide {
      flex: none;
      width: 100%;
    }
  }
}

// Hero cards styling
.hero-card {
  border-radius: 24px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &.quran-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    color: #333;
  }

  &.university-card {
    background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
    color: white;
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .card-text {
      flex: 1;

      .card-title {
        font-size: 18px;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 4px;
      }

      .card-subtitle {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.2;
        opacity: 0.9;
      }
    }

    .card-image {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      background: rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .card-icon-container {
      .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        display: flex;
        align-items: center;
        justify-content: center;

        ion-icon {
          font-size: 28px;
          color: white;
        }
      }
    }
  }

  .card-button {
    align-self: flex-start;

    .register-button {
      --background: #ffc107;
      --color: #333;
      --border-radius: 12px;
      --padding-start: 20px;
      --padding-end: 20px;
      height: 36px;
      font-weight: 600;
      font-size: 14px;

      &.dark {
        --background: rgba(255, 255, 255, 0.2);
        --color: white;
      }
    }
  }
}

// Custom pagination
.hero-pagination {
  position: absolute !important;
  bottom: 8px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  gap: 6px !important;

  .splide__pagination__page {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.4) !important;
    opacity: 1 !important;
    margin: 0 !important;

    &.is-active {
      background: white !important;
      width: 24px !important;
      border-radius: 4px !important;
    }
  }
}

// Main content spacing adjustment
.main-content {
  padding: 24px;
  margin-top: 0;
  position: relative;
  z-index: 1;
}

// Responsive adjustments
@media (max-width: 480px) {
  .header-background {
    min-height: 300px;
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;
  }

  .status-bar {
    padding: 8px 16px 4px 16px;

    .status-left .time {
      font-size: 16px;
    }
  }

  .header-main {
    padding: 12px 16px 16px 16px;

    .header-left .profile-avatar {
      width: 44px;
      height: 44px;
    }

    .header-center {
      .greeting-text {
        font-size: 14px;
      }
      .user-name {
        font-size: 16px;
      }
    }

    .header-right .notification-container {
      width: 44px;
      height: 44px;

      .notification-icon {
        font-size: 20px;
      }
    }
  }


  .hero-card {
    padding: 20px;
    min-height: 120px;

    .card-content {
      .card-text {
        .card-title {
          font-size: 16px;
        }
        .card-subtitle {
          font-size: 14px;
        }
      }

      .card-image,
      .card-icon-container .card-icon {
        width: 50px;
        height: 50px;

        img {
          width: 30px;
          height: 30px;
        }

        ion-icon {
          font-size: 24px;
        }
      }
    }

    .card-button .register-button {
      height: 32px;
      font-size: 13px;
      --padding-start: 16px;
      --padding-end: 16px;
    }
  }

  .main-content {
    padding: 16px;
  }
}
// Remove the previous .hero-cards-container styles and replace with:

.hero-cards-container {
  width: 100%;
  max-width: 90%;
  margin: 0 auto;
  padding-bottom: 40px;
}

.hero-splide {
  .splide__track {
    padding: 0;
    overflow: visible;
  }

  .splide__list {
    display: flex;
    gap: 16px;
  }

  .splide__slide {
    width: 100% !important; // Force full width
    opacity: 1 !important;
  }
}

.hero-card {
  background: white;
  border-radius: 16px;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &.university-card {
    background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
    color: white;
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-bottom: 16px;
  }

  .card-text {
    flex: 1;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }
  }

  .card-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 12px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.hero-pagination {
  bottom: 0 !important;
  padding: 16px 0;

  .splide__pagination__page {
    margin: 0 4px;
    width: 8px;
    height: 8px;
    background: rgba(255,255,255,0.4);

    &.is-active {
      background: white;
      transform: none;
    }
  }
}
