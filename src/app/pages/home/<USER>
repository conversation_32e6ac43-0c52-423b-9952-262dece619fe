// ==========================================
// Home Component - Minimal Ionic Styles
// ==========================================

.home-container {
  padding: 1rem;
  gap: 1rem;
  display: flex;
  flex-direction: column;
}

// Hero content layout
.hero-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  
  .hero-text {
    flex: 1;
    
    h3 {
      margin: 0 0 0.25rem 0;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      opacity: 0.8;
    }
  }
}

// Service button content
.service-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  
  .service-label {
    font-size: 0.75rem;
    text-align: center;
    line-height: 1.2;
  }
}

// Quick service buttons
.quick-service-button {
  height: auto;
  min-height: 60px;
  
  .service-content {
    gap: 0.25rem;
    
    .service-label {
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

// Responsive adjustments
@media (min-width: 768px) {
  .home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem;
  }
}
