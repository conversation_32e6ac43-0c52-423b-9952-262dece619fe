<ion-content class="home-content">
  <!-- Loading State -->
  <div *ngIf="loading()" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'HOME.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error() && !loading()" class="error-container">
    <ion-icon name="alert-circle-outline" size="large"></ion-icon>
    <h3>{{ 'HOME.ERROR' | translate }}</h3>
    <p>{{ error() }}</p>
    <ion-button fill="outline" (click)="onRetry()">
      {{ 'HOME.RETRY' | translate }}
    </ion-button>
  </div>

  <!-- Main Content -->
  <div *ngIf="homeData() && !loading() && !error()" class="home-container">

    <!-- Hero Banner Section -->
    <div class="hero-banner">
      <div class="splide hero-splide" id="hero-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li class="splide__slide">
              <div class="hero-card quran-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=100&h=100&fit=crop&crop=center" alt="القرآن الكريم" />
                  </div>
                  <div class="hero-text">
                    <h3>المسابقة السنوية</h3>
                    <p>لحفظ القرآن الكريم</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    سجل الآن
                  </ion-button>
                </div>
              </div>
            </li>
            <li class="splide__slide">
              <div class="hero-card scholarship-card">
                <div class="hero-content">
                  <div class="hero-icon">
                    <img src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=100&h=100&fit=crop&crop=center" alt="المنح الدراسية" />
                  </div>
                  <div class="hero-text">
                    <h3>المنح الدراسية</h3>
                    <p>للطلاب المتفوقين</p>
                  </div>
                  <ion-button fill="solid" size="small" class="hero-button">
                    اعرف المزيد
                  </ion-button>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Announcements Section -->
    <section class="home-section announcements-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.ANNOUNCEMENTS_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('announcements')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="announcements-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let announcement of homeData()?.announcements" class="splide__slide">
              <ion-card
                class="announcement-card"
                [class.important]="announcement.important"
                (click)="onAnnouncementClick(announcement)">
                <div *ngIf="announcement.imageUrl" class="card-image">
                  <img
                    [src]="announcement.imageUrl"
                    [alt]="announcement.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge
                      *ngIf="announcement.important"
                      color="danger"
                      class="important-badge">
                      {{ 'HOME.IMPORTANT' | translate }}
                    </ion-badge>
                    <ion-badge color="medium" class="category-badge">
                      {{ announcement.category }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ announcement.title }}</h3>
                  <p class="card-content">{{ announcement.content }}</p>
                  <div class="card-meta">
                    <span class="card-author">{{ announcement.author }}</span>
                    <span class="card-date">{{ formatDate(announcement.date) }}</span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="home-section news-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.NEWS_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('news')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="news-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let news of homeData()?.news" class="splide__slide">
              <ion-card class="news-card" (click)="onNewsClick(news)">
                <div *ngIf="news.imageUrl" class="card-image">
                  <img
                    [src]="news.imageUrl"
                    [alt]="news.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge color="primary" class="category-badge">
                      {{ news.category }}
                    </ion-badge>
                    <ion-badge
                      *ngIf="news.readTime"
                      color="medium"
                      class="read-time-badge">
                      {{ news.readTime }} {{ 'HOME.READ_TIME' | translate }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ news.title }}</h3>
                  <p class="card-summary">{{ news.summary }}</p>
                  <div class="card-meta">
                    <span class="card-author">{{ news.author }}</span>
                    <span class="card-date">{{ formatDate(news.date) }}</span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Achievements Section -->
    <section class="home-section achievements-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.ACHIEVEMENTS_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('achievements')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="achievements-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let achievement of homeData()?.achievements" class="splide__slide">
              <ion-card class="achievement-card" (click)="onAchievementClick(achievement)">
                <div *ngIf="achievement.imageUrl" class="card-image">
                  <img
                    [src]="achievement.imageUrl"
                    [alt]="achievement.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge
                      color="success"
                      class="achievement-type-badge">
                      {{ getAchievementTypeTranslation(achievement.achievementType) | translate }}
                    </ion-badge>
                    <ion-badge color="medium" class="category-badge">
                      {{ achievement.category }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ achievement.title }}</h3>
                  <p class="card-description">{{ achievement.description }}</p>
                  <div class="card-meta">
                    <span class="card-date">{{ formatDate(achievement.date) }}</span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Academic Calendar Section -->
    <section class="home-section calendar-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.CALENDAR_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('calendar')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="calendar-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let event of homeData()?.calendarEvents" class="splide__slide">
              <ion-card
                class="calendar-card"
                [class.important]="event.isImportant"
                (click)="onCalendarEventClick(event)">
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge
                      [color]="event.isImportant ? 'danger' : 'primary'"
                      class="event-type-badge">
                      {{ getEventTypeTranslation(event.type) | translate }}
                    </ion-badge>
                    <ion-badge
                      *ngIf="event.isImportant"
                      color="warning"
                      class="important-badge">
                      {{ 'HOME.IMPORTANT' | translate }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ event.title }}</h3>
                  <p class="card-description">{{ event.description }}</p>
                  <div class="card-meta">
                    <div class="event-dates">
                      <span class="start-date">{{ formatDate(event.startDate) }}</span>
                      <span *ngIf="event.endDate" class="end-date">
                        - {{ formatDate(event.endDate) }}
                      </span>
                    </div>
                    <span *ngIf="event.location" class="event-location">
                      <ion-icon name="location-outline"></ion-icon>
                      {{ event.location }}
                    </span>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- University Information Section -->
    <section class="home-section university-info-section">
      <div class="section-header">
        <h2 class="section-title">{{ 'HOME.UNIVERSITY_INFO_TITLE' | translate }}</h2>
        <ion-button
          fill="clear"
          size="small"
          (click)="onViewAllClick('university-info')"
          class="view-all-button">
          {{ 'HOME.VIEW_ALL' | translate }}
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="splide" id="university-info-splide">
        <div class="splide__track">
          <ul class="splide__list">
            <li *ngFor="let info of homeData()?.universityInfo" class="splide__slide">
              <ion-card class="university-info-card" (click)="onUniversityInfoClick(info)">
                <div *ngIf="info.imageUrl" class="card-image">
                  <img
                    [src]="info.imageUrl"
                    [alt]="info.title"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge color="tertiary" class="info-type-badge">
                      {{ info.type === 'vision' ? ('HOME.VISION' | translate) :
                         info.type === 'mission' ? ('HOME.MISSION' | translate) :
                         ('HOME.ABOUT_UNIVERSITY' | translate) }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ info.title }}</h3>
                  <p class="card-description">{{ info.description }}</p>
                </ion-card-content>
              </ion-card>
            </li>
            <!-- Colleges Information -->
            <li *ngFor="let college of homeData()?.colleges" class="splide__slide">
              <ion-card class="college-card" (click)="onCollegeClick(college)">
                <div *ngIf="college.imageUrl" class="card-image">
                  <img
                    [src]="college.imageUrl"
                    [alt]="college.name"
                    loading="lazy"
                    (error)="onImageError($event)" />
                </div>
                <ion-card-content>
                  <div class="card-badges">
                    <ion-badge color="secondary" class="college-badge">
                      {{ 'HOME.COLLEGES' | translate }}
                    </ion-badge>
                  </div>
                  <h3 class="card-title">{{ college.name }}</h3>
                  <p class="card-description">{{ college.description }}</p>
                  <div class="college-stats">
                    <div class="stat-item">
                      <span class="stat-label">{{ 'HOME.DEAN' | translate }}:</span>
                      <span class="stat-value">{{ college.dean }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">{{ 'HOME.ESTABLISHED' | translate }}:</span>
                      <span class="stat-value">{{ college.establishedYear }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">{{ 'HOME.STUDENTS_COUNT' | translate }}:</span>
                      <span class="stat-value">{{ college.studentsCount }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">{{ 'HOME.PROGRAMS_COUNT' | translate }}:</span>
                      <span class="stat-value">{{ college.programsCount }}</span>
                    </div>
                  </div>
                </ion-card-content>
              </ion-card>
            </li>
          </ul>
        </div>
      </div>
    </section>

  </div>
</ion-content>