<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/academic"></ion-back-button>
      <!-- <ion-menu-button></ion-menu-button> -->
    </ion-buttons>
    <ion-title>{{ 'STUDY_PLAN.TITLE' | translate }}</ion-title>
  </ion-toolbar>
  <ion-toolbar *ngIf="levels().length > 0">
    <ion-segment scrollable="true" [value]="selectedLevelId()">
      <ion-segment-button *ngFor="let level of levels()" [value]="level.id" (click)="selectLevel(level.id)">
        <ion-label>{{ getLevelTitle(level) }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">{{ 'STUDY_PLAN.TITLE' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <div *ngIf="loading()" class="ion-text-center ion-padding-top">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'STUDY_PLAN.LOADING' | translate }}</p>
  </div>

  <div *ngIf="!loading() && levels().length === 0" class="ion-text-center ion-padding-top">
    <ion-icon name="sad-outline" size="large"></ion-icon>
    <p>{{ 'STUDY_PLAN.NO_DATA' | translate }}</p>
  </div>

  <div *ngIf="!loading() && levels().length > 0">
    <div *ngFor="let level of levels(); trackBy: trackLevelById" [id]="'level-' + level.id" #levelElement
      class="level-section">
      <div class="level-header">
        <div class="level-title-container">
          <h2 class="level-title">{{ getLevelTitle(level) }}</h2>
          <div class="level-underline"></div>
        </div>
        <div class="level-progress">{{ calculateLevelProgress(level) }}</div>
      </div>
      <div class="courses-flex-container shimmer-container">
        <ion-card *ngFor="let course of level.details; trackBy: trackCourseByCode"
          class="course-card course-card-compact" [ngClass]="getCardClass(course.status)"
          (click)="showCourseDetails(course)">
          <div class="card-content">
            <div class="status-indicator"></div>
            <div class="course-info">
              <div class="course-code">{{ course.code }}</div>
              <div class="course-hours">
                <hugeicons-icon [icon]="Clock05Icon" size="18" [strokeWidth]="1.5"></hugeicons-icon>
                <span style="margin-inline-start: 4px;">
                  @if ( +course.hours === 1 ){
                  ساعة واحدة
                  } @else if ( +course.hours === 2 ){
                  ساعتين
                  } @else if ( +course.hours === 3 ){
                  ثلاث ساعات
                  } @else if ( +course.hours === 4 ){
                  أربع ساعات
                  } @else if ( +course.hours === 5 ){
                  خمس ساعات
                  } @else if ( +course.hours === 6 ){
                  ست ساعات
                  } @else if ( +course.hours === 7 ){
                  سبعة ساعات
                  } @else if ( +course.hours === 8 ){
                  ثمان ساعات
                  } @else if ( +course.hours === 9 ){
                  تسعة ساعات
                  }
                </span>
              </div>
            </div>
            <div class="shimmer-effect"></div>
          </div>
        </ion-card>
      </div>
    </div>
  </div>

</ion-content>

<!-- Modern Course Details Modal with College Info -->
<ion-modal [isOpen]="isBottomSheetOpen" (didDismiss)="dismissBottomSheet()" [breakpoints]="[0, 0.95]"
  [initialBreakpoint]="0.95" [backdropBreakpoint]="0" class="course-detail-modal">
  <ng-template>
    <div class="modal-wrapper">
      <!-- Header with background color based on status -->
      <div class="modal-header" [ngClass]="'status-' + selectedCourse?.status">
        <div class="header-content">
          <div class="course-title-section">
            <div class="course-code-badge">{{ selectedCourse?.code }}</div>
            <h1 class="course-title">{{ selectedCourse?.title }}</h1>

            <!-- College info with building icon -->
            <div class="course-college">
              <hugeicons-icon [icon]="Building01Icon" size="18" [strokeWidth]="1.5"></hugeicons-icon>
              <span>{{ 'College of Computer Science' | titlecase}}</span>
            </div>
          </div>
          <ion-button (click)="dismissBottomSheet()" fill="clear" class="close-button">
            <hugeicons-icon [icon]="Cancel01Icon" size="24" [strokeWidth]="1.5" color="white"></hugeicons-icon>
          </ion-button>
        </div>

        <!-- Status banner -->
        <div class="status-banner">
          <hugeicons-icon [icon]="getCardIcon(selectedCourse?.status ?? 'passed')" size="20"
            [strokeWidth]="1.5"></hugeicons-icon>
          <span>{{ 'STUDY_PLAN.STATUS_' + selectedCourse?.status?.toUpperCase() | translate }}</span>
        </div>
      </div>

      <!-- Scrollable content -->
      <ion-content class="modal-content">
        <div *ngIf="selectedCourse" class="content-container">

          <!-- Key Info Cards -->
          <div class="info-cards">
            <!-- Credit Hours -->
            <div class="info-card">
              <div class="info-card-title">{{ 'STUDY_PLAN.HOURS' | translate }}</div>
              <div class="info-card-value">
                <hugeicons-icon [icon]="Clock05Icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                {{ selectedCourse.hours }}
              </div>
            </div>


            <!-- Only show grade cards if the course is passed -->
            <div *ngIf="selectedCourse.status === 'passed'" class="info-card">
              <div class="info-card-title">{{ 'STUDY_PLAN.LETTER_GRADE' | translate }}</div>
              <div class="info-card-value grade-value">
                <hugeicons-icon [icon]="GraduationScrollIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                {{'A+' }}
              </div>
            </div>


            <!-- Show status info for non-passed courses -->
            <div *ngIf="selectedCourse.status !== 'passed'" class="info-card status-card"
              [ngClass]="'status-' + selectedCourse?.status">
              <div class="info-card-title">{{ 'STUDY_PLAN.STATUS' | translate }}</div>
              <div class="info-card-value">
                <hugeicons-icon [icon]="getCardIcon(selectedCourse?.status ?? 'remaining')" size="24"
                  [strokeWidth]="1.5"></hugeicons-icon>
                {{ 'STUDY_PLAN.STATUS_' + selectedCourse?.status?.toUpperCase() | translate }}
              </div>
            </div>
          </div>

          <!-- Description Section -->
          <div class="section-container">
            <h2 class="section-title">
              <hugeicons-icon [icon]="Book02Icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
              {{ 'STUDY_PLAN.DESCRIPTION' | translate }}
            </h2>
            <p class="section-content">
              {{ ('STUDY_PLAN.DEFAULT_DESCRIPTION' | translate | titlecase) }}
            </p>
          </div>

          <!-- Learning Outcomes -->
          <div class="section-container">
            <h2 class="section-title">
              <hugeicons-icon [icon]="DocumentValidationIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
              {{ 'STUDY_PLAN.LEARNING_OUTCOMES' | translate }}
            </h2>
            <p *ngFor="let outcome of getDefaultOutcomes()">
              {{ outcome }}
            </p>
          </div>

          <!-- Prerequisites (if any) -->
          <div class="section-container">
            <h2 class="section-title">
              <hugeicons-icon [icon]="NetworkNode" size="24" [strokeWidth]="1.5"></hugeicons-icon>
              {{ 'STUDY_PLAN.PREREQUISITES' | translate }}
            </h2>
            <div class="prerequisites-container">
              <div class="no-prerequisites">
                {{ 'STUDY_PLAN.NO_PREREQUISITES' | translate }}
              </div>
              <div *ngFor="let prerequisite of [].constructor(3)" class="prerequisite-chip"
                (click)="showPrerequisiteCourse(prerequisite?.code || 'SWE101')">
                {{ prerequisite?.code || 'SWE101' }}
              </div>
            </div>
          </div>

          <!-- Bottom actions -->
          <div class="action-buttons">
            <ion-button fill="outline" class="action-button">
              <hugeicons-icon [icon]="Book02Icon" size="24" [strokeWidth]="1.5" slot="start"></hugeicons-icon>
              <span slot="end">{{ 'STUDY_PLAN.SYLLABUS' | translate | titlecase }}</span>
            </ion-button>
            <ion-button fill="outline" class="action-button">
              <hugeicons-icon [icon]="Calendar01Icon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
              {{ 'STUDY_PLAN.SCHEDULE' | translate | titlecase}}
            </ion-button>
          </div>
        </div>

        <!-- No data message -->
        <div *ngIf="!selectedCourse" class="no-data-container">
          <hugeicons-icon [icon]="AlertCircleIcon" size="48" [strokeWidth]="1.5"></hugeicons-icon>
          <p>{{ 'STUDY_PLAN.NO_COURSE_DATA' | translate }}</p>
          <ion-button fill="outline" (click)="dismissBottomSheet()">
            {{ 'COMMON.CLOSE' | translate }}
          </ion-button>
        </div>

        <!-- Spacer for safe area -->
        <div class="bottom-spacer"></div>
      </ion-content>
    </div>
  </ng-template>
</ion-modal>
