import {
  Component,
  OnInit,
  inject,
  signal,
  WritableSignal,
  ElementRef,
  QueryList,
  ViewChildren,
  AfterViewInit,
  <PERSON><PERSON><PERSON><PERSON>,
  ChangeDetectorRef,
  computed,
  effect,
  Injector,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonicModule,
  RefresherCustomEvent,
  ModalController,
  IonRouterOutlet,
} from '@ionic/angular';
import { StudyPlanService } from '../../services/study-plan/study-plan.service';
import { Level, CourseDetail } from './study-plan.interface';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { HugeiconsIconComponent, IconSvgObject } from '@hugeicons/angular';
import {
  AlertCircleIcon,
  ArrowRightIcon,
  Book02Icon,
  Building01Icon,
  Calendar01Icon,
  Cancel01Icon,
  CellularNetworkOfflineIcon,
  CheckmarkCircleIcon,
  Clock01Icon,
  Clock02Icon,
  Clock03StrokeRounded,
  Clock05Icon,
  ClosedCaptionAltIcon,
  DocumentValidationIcon,
  GraduationScrollIcon,
  GraduationScrollStrokeRounded,
  InformationCircleIcon,
  NodeMoveUpIcon,
  PieChart01Icon,
  TextCheckIcon,
  Time01StrokeRounded,
  TimeIcon,
} from '@hugeicons-pro/core-stroke-rounded';

@Component({
  selector: 'app-study-plan',
  templateUrl: './study-plan.component.html',
  styleUrls: ['./study-plan.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    TranslateModule,
    HugeiconsIconComponent,
  ],
})
export class StudyPlanComponent implements AfterViewInit, OnDestroy {
  private cdr = inject(ChangeDetectorRef);

  @ViewChildren('levelElement') levelElements!: QueryList<
    ElementRef<HTMLDivElement>
  >;
  private observer?: IntersectionObserver;
  private currentlyScrolledByClick = false;
  private clickScrollTimeout: any;
  private internalSetSelectedLevelId = false;

  public levels = computed(
    () =>
      this.studyPlanService.studyPlan()?.levels.sort((a, b) => a.id - b.id) ??
      []
  );
  public selectedLevelId: WritableSignal<number | null> = signal(null);

  // Bottom sheet state
  public isBottomSheetOpen = false;
  public selectedCourse: CourseDetail | null = null;

  studyPlan = this.studyPlanService.studyPlan;
  loading = this.studyPlanService.loading;

  // icons:
  NetworkNode = NodeMoveUpIcon;

  constructor(
    private studyPlanService: StudyPlanService,
    private injector: Injector
  ) {
    effect(
      () => {
        const currentStudyPlanData = this.studyPlan();
        if (currentStudyPlanData && !this.loading()) {
          this.cdr.detectChanges();

          let levelIdToAutoScroll: number | null = null;
          const currentLevels = currentStudyPlanData.levels;

          if (currentLevels && currentLevels.length > 0) {
            for (let i = currentLevels.length - 1; i >= 0; i--) {
              const level = currentLevels[i];
              const hasNonRemainingCourse = level.details.some(
                (course) => course.status !== 'remaining'
              );
              if (hasNonRemainingCourse) {
                levelIdToAutoScroll = level.id;
                break;
              }
            }
            if (levelIdToAutoScroll === null) {
              levelIdToAutoScroll = currentLevels[0].id;
            }
          }

          if (levelIdToAutoScroll !== null) {
            setTimeout(() => {
              if (
                levelIdToAutoScroll !== null &&
                this.levels().find((l) => l.id === levelIdToAutoScroll)
              ) {
                this.selectLevel(levelIdToAutoScroll);
              }
            }, 150);
          }

          setTimeout(() => {
            this.observeLevels();
          }, 400);
        }
      },
      { injector: this.injector }
    );
  }

  ngAfterViewInit() {
    this.levelElements.changes.subscribe(() => {
      if (this.levels() && this.levels().length > 0) {
        this.observeLevels();
      }
    });
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.clickScrollTimeout) {
      clearTimeout(this.clickScrollTimeout);
    }
  }

  observeLevels() {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (
      !this.levelElements ||
      this.levelElements.length === 0 ||
      !this.levels() ||
      this.levels().length === 0
    ) {
      return;
    }

    const headerHeight = 115;
    const options = {
      root: null,
      rootMargin: `-${headerHeight}px 0px -70% 0px`,
      threshold: 0.01,
    };

    this.observer = new IntersectionObserver((entries) => {
      if (this.currentlyScrolledByClick) {
        return;
      }

      const intersectingEntries = entries.filter(
        (entry) => entry.isIntersecting
      );

      if (intersectingEntries.length > 0) {
        intersectingEntries.sort(
          (a, b) =>
            a.target.getBoundingClientRect().top -
            b.target.getBoundingClientRect().top
        );

        const topmostEntry = intersectingEntries[0];
        const levelId = parseInt(
          topmostEntry.target.id.replace('level-', ''),
          10
        );

        if (this.selectedLevelId() !== levelId) {
          this.internalSetSelectedLevelId = true;
          this.selectedLevelId.set(levelId);
          this.cdr.detectChanges();
          this.internalSetSelectedLevelId = false;
        }
      }
    }, options);

    this.levelElements.forEach((el) =>
      this.observer?.observe(el.nativeElement)
    );
  }

  selectLevel(levelId: number) {
    if (this.internalSetSelectedLevelId) {
      return;
    }

    this.currentlyScrolledByClick = true;
    this.selectedLevelId.set(levelId);

    const element = document.getElementById('level-' + levelId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    if (this.clickScrollTimeout) {
      clearTimeout(this.clickScrollTimeout);
    }
    this.clickScrollTimeout = setTimeout(() => {
      this.currentlyScrolledByClick = false;
    }, 1000);
  }

  getLevelTitle(level: Level): string {
    switch (level.id) {
      case -1:
        return 'اختياري قسم';
      case -2:
        return 'اختياري كلية';
      default:
        return `${level.title}`;
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'passed':
        return 'checkmark-circle';
      case 'remaining':
        return 'ellipse-outline';
      case 'student_schedule':
        return 'calendar-outline';
      default:
        return 'help-circle-outline';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'passed':
        return 'success';
      case 'remaining':
        return 'medium';
      case 'student_schedule':
        return 'primary';
      default:
        return 'light';
    }
  }
  getCardClass(status: string): string {
    switch (status) {
      case 'passed':
        return 'course-card-passed';
      case 'remaining':
        return 'course-card-remaining';
      case 'student_schedule':
        return 'course-card-scheduled';
      default:
        return 'course-card-default';
    }
  }
  getCardIcon(status: string): IconSvgObject {
    switch (status) {
      case 'passed':
        return CheckmarkCircleIcon; // or 'CheckCircle' - represents completion/success
      case 'remaining':
        return TimeIcon; // or 'Clock' - represents pending/remaining time
      case 'student_schedule':
        return Calendar01Icon; // or 'CalendarSchedule' - represents scheduled events
      default:
        return InformationCircleIcon; // default information icon
    }
  }

  trackLevelById(index: number, level: Level): number {
    return level.id;
  }

  trackCourseByCode(index: number, course: CourseDetail): string {
    return course.code;
  }

  calculateLevelProgress(level: Level): string {
    if (!level || !level.details || level.details.length === 0) {
      return '0%';
    }

    const passedCourses = level.details.filter(
      (course) => course.status === 'passed'
    ).length;
    const totalCourses = level.details.length;
    const percentage =
      totalCourses > 0 ? (passedCourses / totalCourses) * 100 : 0;

    return `${percentage.toFixed(0)}%`;
  }

  async showCourseDetails(course: CourseDetail) {
    this.selectedCourse = course;
    this.isBottomSheetOpen = true;
    this.cdr.detectChanges();
  }

  dismissBottomSheet() {
    this.isBottomSheetOpen = false;
    this.selectedCourse = null;
  }

  // Helper method for default learning outcomes (if none provided)
  getDefaultOutcomes(): string[] {
    return ['نواتج التعلم'];
  }

  // Method to handle clicking on a prerequisite course
  showPrerequisiteCourse(courseCode: string): void {
    // Implement logic to show the selected prerequisite course
    console.log('Show prerequisite course:', courseCode);
    // You might want to fetch the course details and show them
  }

  protected readonly TextCheckIcon = TextCheckIcon;
  protected readonly DocumentValidationIcon = DocumentValidationIcon;
  protected readonly Book02Icon = Book02Icon;
  protected readonly GraduationScrollStrokeRounded =
    GraduationScrollStrokeRounded;
  protected readonly Time01StrokeRounded = Time01StrokeRounded;
  protected readonly Clock01Icon = Clock01Icon;
  protected readonly GraduationScrollIcon = GraduationScrollIcon;
  protected readonly Clock02Icon = Clock02Icon;
  protected readonly Clock03StrokeRounded = Clock03StrokeRounded;
  protected readonly Clock05Icon = Clock05Icon;
  protected readonly Calendar01Icon = Calendar01Icon;
  protected readonly Building01Icon = Building01Icon;
  protected readonly ClosedCaptionAltIcon = ClosedCaptionAltIcon;
  protected readonly ArrowRightIcon = ArrowRightIcon;
  protected readonly PieChart01Icon = PieChart01Icon;
  protected readonly AlertCircleIcon = AlertCircleIcon;
  protected readonly Cancel01Icon = Cancel01Icon;
}
