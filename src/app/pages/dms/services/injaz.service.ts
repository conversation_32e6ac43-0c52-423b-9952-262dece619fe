import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Transaction, TransactionResult } from '../models/transaction.model';

@Injectable({
  providedIn: 'root'
})
export class InjazService {
  private apiUrl = 'https://api.qu.edu.sa/api/v1/webhook/capture/1OnyrjfJgqyYPc2Vz5KzLB4y9WjhhGmYBb6+n0RoX+8=';
  private storageKey = 'injazTransactions';

  // Hijri years from 1438 to 1446
  hijriYears: string[] = ['1446', '1445', '1444', '1443', '1442', '1441', '1440', '1439', '1438'];

  // Mailbox options
  mailboxes = [
    { id: '1', name: 'صندوق البريد الداخلي' },
    { id: '2', name: 'صندوق البريد الخارجي' }
  ];

  constructor(private http: HttpClient) { }

  /**
   * Get transaction details from the API
   */
  getTransactionDetails(docNo: string, docYear: string, docOrgnSrc: string): Observable<TransactionResult> {
    const params = new HttpParams()
      .set('docNo', docNo)
      .set('docYear', docYear)
      .set('docOrgnSrc', docOrgnSrc);

    return this.http.get(this.apiUrl, { params }).pipe(
      map((response: any) => {
        if (response && response.Response) {
          const apiResponse = response.Response;

          // Check for error in response
          if (apiResponse.ErrorCode || apiResponse.ErrorDesc) {
            throw new Error(apiResponse.ErrorDesc || 'Unknown error');
          }

          // Determine status based on StatusDesc
          let status: 'completed' | 'in-progress' | 'pending' = 'pending';
          if (apiResponse.StatusDesc === 'منتهي') {
            status = 'completed';
          } else if (apiResponse.StatusDesc === 'قيد التنفيذ' || apiResponse.StatusDesc === 'قيد المعالجة') {
            status = 'in-progress';
          }

          // Create result object
          const result: TransactionResult = {
            status: status,
            assignedTo: apiResponse.AssignToDeptDesc || apiResponse.AssignToDept || '',
            date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
            source: apiResponse.SelDesc || '',
            DocNo: apiResponse.DocNo,
            completionDate: apiResponse.OutDocDate || undefined,
            // Create a basic timeline based on available data
            timeline: [
              {
                date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
                time: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[1] || '' : '',
                title: 'تم استلام المعاملة',
                description: 'تم استلام المعاملة من قبل نظام إنجاز',
                active: true
              },
              {
                date: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[0] || '' : '',
                time: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[1] || '' : '',
                title: status === 'completed' ? 'اكتملت المعاملة' : 'قيد المعالجة',
                description: status === 'completed' ? 'تم الانتهاء من جميع الإجراءات المتعلقة بالمعاملة' : undefined,
                active: status === 'completed'
              }
            ]
          };

          return result;
        } else {
          throw new Error('Invalid response from server');
        }
      }),
      catchError((error: HttpErrorResponse | Error) => {
        if (error instanceof HttpErrorResponse) {
          if (error.status === 0) {
            return throwError(() => new Error('Network error. Please check your internet connection.'));
          } else if (error.status === 404) {
            return throwError(() => new Error('Transaction not found. Please check your input data.'));
          } else {
            return throwError(() => new Error('Server error. Please try again later.'));
          }
        } else {
          return throwError(() => error);
        }
      })
    );
  }

  /**
   * Get transaction by ID from local storage
   */
  getTransactionById(docNumber: string, year?: string, mailbox?: string): Observable<Transaction | null> {
    const transactions = this.getSavedTransactions();

    // If year and mailbox are provided, find the exact transaction
    if (year && mailbox) {
      const transaction = transactions.find(t =>
        t.transactionNumber === docNumber &&
        t.year === year &&
        t.mailbox === mailbox
      );
      return of(transaction || null);
    }

    // Otherwise, find the first transaction with matching docNumber
    const transaction = transactions.find(t => t.transactionNumber === docNumber);
    return of(transaction || null);
  }

  /**
   * Get all saved transactions from local storage
   */
  getSavedTransactions(): Transaction[] {
    const savedTransactions = localStorage.getItem(this.storageKey);
    return savedTransactions ? JSON.parse(savedTransactions) : [];
  }

  /**
   * Save a transaction to local storage
   */
  saveTransaction(transaction: Transaction): void {
    const transactions = this.getSavedTransactions();

    // Check if transaction already exists
    const index = transactions.findIndex(
      t => t.transactionNumber === transaction.transactionNumber &&
        t.year === transaction.year &&
        t.mailbox === transaction.mailbox
    );

    // Update or add the transaction
    if (index !== -1) {
      transactions[index] = transaction;
    } else {
      // Add to the beginning of the array (most recent first)
      transactions.unshift(transaction);
    }

    // Save to local storage
    localStorage.setItem(this.storageKey, JSON.stringify(transactions));
  }

  /**
   * Remove a transaction from local storage
   */
  removeTransaction(index: number): void {
    const transactions = this.getSavedTransactions();
    if (index >= 0 && index < transactions.length) {
      transactions.splice(index, 1);
      localStorage.setItem(this.storageKey, JSON.stringify(transactions));
    }
  }

  /**
   * Clear all transactions from local storage
   */
  clearTransactions(): void {
    localStorage.setItem(this.storageKey, JSON.stringify([]));
  }

  /**
   * Get mailbox name by ID
   */
  getMailboxName(mailboxId: string): string {
    const mailbox = this.mailboxes.find(box => box.id === mailboxId);
    return mailbox ? mailbox.name : mailboxId;
  }
}
