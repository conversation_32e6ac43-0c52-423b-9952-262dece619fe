import { Component, OnInit } from '@angular/core';
import { IonicModule, ToastController } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { HttpClient, HttpParams, HttpErrorResponse, HttpClientModule } from '@angular/common/http';
import {
  Add01StrokeRounded, Alert01StrokeRounded,
  AlertCircleStrokeRounded, ArrowRightIcon,
  BuildingIconStrokeRounded, CalendarIconStrokeRounded, CancelIconStrokeRounded,
  CheckmarkCircleIconStrokeRounded,
  Clock05StrokeRounded, MailboxStrokeRounded, RefreshIcon, RemoveCircleIcon,
  SearchIcon, SearchIconStrokeRounded, TimeIconStrokeRounded
} from "@hugeicons-pro/core-stroke-rounded";
import {HugeiconsIconComponent} from "@hugeicons/angular";


interface Transaction {
  transactionNumber: string;
  year: string;
  mailbox: string;
  searchDate: Date;
  result?: TransactionResult | null;
}

interface TransactionResult {
  status: 'completed' | 'in-progress' | 'pending';
  assignedTo: string;
  date: string;
  source: string;
  DocNo?: string;
  completionDate?: string;
  timeline?: TimelineEvent[];
}

interface TimelineEvent {
  date: string;
  time?: string;
  title: string;
  description?: string;
  active: boolean;
}

interface Mailbox {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dms',
  templateUrl: './dms.component.html',
  styleUrls: ['./dms.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    HugeiconsIconComponent,
  ],
})
export class DmsComponent implements OnInit {
  transactionForm: FormGroup;
  isLoading = false;
  showNoResults = false;
  searchResult: TransactionResult | null = null;
  recentTransactions: Transaction[] = [];

  // Icons
  SearchIcon = SearchIcon;
  CalendarIcon = CalendarIconStrokeRounded;
  ClockIcon = Clock05StrokeRounded;
  BuildingIcon = BuildingIconStrokeRounded;
  MailboxIcon = MailboxStrokeRounded;
  CheckCircleIcon = CheckmarkCircleIconStrokeRounded;
  CloseCircleIcon = CancelIconStrokeRounded;
  TimelineIcon = TimeIconStrokeRounded;
  AlertCircleIcon = AlertCircleStrokeRounded;
  TrashIcon = RemoveCircleIcon;
  ChevronRightIcon = ArrowRightIcon;
  RefreshIcon = RefreshIcon;

  // Hijri years from 1438 to 1446
  hijriYears: string[] = ['1446', '1445', '1444', '1443', '1442', '1441', '1440', '1439', '1438'];

  // Mailbox options
  mailboxes: Mailbox[] = [
    { id: '1', name: 'صندوق البريد الداخلي' },
    { id: '2', name: 'صندوق البريد الخارجي' }
  ];

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private toastController: ToastController,
    private translateService: TranslateService,
    private http: HttpClient
  ) {
    this.transactionForm = this.formBuilder.group({
      transactionNumber: [null, [Validators.required, Validators.pattern(/^\d+$/)]],
      year: [null, Validators.required],
      mailbox: [null, Validators.required]
    });
  }

  ngOnInit() {
    this.loadSavedTransactions();
  }

  dismiss() {
    this.router.navigate(['/services']);
  }

  // Form validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.transactionForm.get(fieldName);
    return field ? (field.invalid && (field.dirty || field.touched)) : false;
  }

  // Search for transaction
  searchTransaction() {
    if (this.transactionForm.invalid) {
      return;
    }

    this.isLoading = true;
    this.showNoResults = false;
    this.searchResult = null;

    const formValue = this.transactionForm.value;

    // Set up API endpoint and parameters
    const apiUrl = 'https://api.qu.edu.sa/api/v1/webhook/capture/1OnyrjfJgqyYPc2Vz5KzLB4y9WjhhGmYBb6+n0RoX+8=';
    const params = new HttpParams()
      .set('docNo', formValue.transactionNumber)
      .set('docYear', formValue.year)
      .set('docOrgnSrc', formValue.mailbox);

    // Make the HTTP request
    this.http.get(apiUrl, { params })
      .subscribe({
        next: (response: any) => {
          if (response && response.Response) {
            // Map API response to TransactionResult interface
            const apiResponse = response.Response;

            // Check for error in response
            if (apiResponse.ErrorCode || apiResponse.ErrorDesc) {
              this.showErrorToast(this.translateService.instant('INJAZ.NOT_FOUND'));
              this.showNoResults = true;
              this.isLoading = false;
              return;
            }

            // Determine status based on StatusDesc
            let status: 'completed' | 'in-progress' | 'pending' = 'pending';
            if (apiResponse.StatusDesc === 'منتهي' || apiResponse.StatusDesc === 'حفظ في الارشيف') {
              status = 'completed';
            } else if (apiResponse.StatusDesc === 'تحت الإجراء' || apiResponse.StatusDesc === 'قيد المعالجة') {
            } else if (apiResponse.StatusDesc === 'تحت الإجراء' || apiResponse.StatusDesc === 'قيد المعالجة') {
              status = 'in-progress';
            }

            // Create result object
            this.searchResult = {
              status: status,
              assignedTo: apiResponse.AssignToDeptDesc || apiResponse.AssignToDept || '',
              date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
              source: apiResponse.SelDesc || '',
              DocNo: apiResponse.DocNo || '',
              completionDate: apiResponse.OutDocDate || undefined,
              // Create a basic timeline based on available data
              timeline: [
                {
                  date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
                  time: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[1] || '' : '',
                  title: 'تم استلام المعاملة',
                  description: 'تم استلام المعاملة من قبل نظام إنجاز',
                  active: true
                },
                {
                  date: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[0] || '' : '',
                  time: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[1] || '' : '',
                  title: status === 'completed' ? 'اكتملت المعاملة' : 'تحت الإجراء',
                  description: status === 'completed' ? 'تم الانتهاء من جميع الإجراءات المتعلقة بالمعاملة' : undefined,
                  active: status === 'completed'
                }
              ]
            };

            // Save the transaction to history
            this.saveTransaction(formValue, this.searchResult);

            // Navigate to transaction details page
            this.router.navigate(['/services/dms', formValue.transactionNumber], {
              queryParams: {
                year: formValue.year,
                mailbox: formValue.mailbox
              }
            });
          } else {
            // Handle empty or invalid response
            this.showNoResults = true;
            this.showErrorToast(this.translateService.instant('INJAZ.INVALID_RESPONSE'));
          }
          this.isLoading = false;
        },
        error: (error: HttpErrorResponse) => {
          console.error('API Error:', error);
          this.isLoading = false;
          this.showNoResults = true;

          // Handle different types of errors
          if (error.status === 0) {
            // Network error
            this.showErrorToast(this.translateService.instant('INJAZ.NETWORK_ERROR'));
          } else if (error.status === 404) {
            // Not found
            this.showErrorToast(this.translateService.instant('INJAZ.NOT_FOUND'));
          } else {
            // Other errors
            this.showErrorToast(this.translateService.instant('INJAZ.SERVER_ERROR'));
          }
        }
      });
  }

  // Helper method to show error toast
  private async showErrorToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      position: 'bottom',
      color: 'danger',
      buttons: [{
        text: this.translateService.instant('common.ok'),
        role: this.translateService.instant('common.cancel')
      }]
    });
    await toast.present();
  }

  // Helper method to show success toast
  private async showSuccessToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom',
      color: 'success',
      buttons: [{
        text: this.translateService.instant('common.ok'),
        role: this.translateService.instant('common.cancel')
      }]
    });
    await toast.present();
  }

  // Update transaction status
  async updateTransactionStatus(transaction: Transaction, index: number) {
    if (!transaction || this.updatingIndex !== null) {
      return;
    }

    this.updatingIndex = index;

    // Set up API endpoint and parameters
    const apiUrl = 'https://api.qu.edu.sa/api/v1/webhook/capture/1OnyrjfJgqyYPc2Vz5KzLB4y9WjhhGmYBb6+n0RoX+8=';
    const params = new HttpParams()
      .set('docNo', transaction.transactionNumber)
      .set('docYear', transaction.year)
      .set('docOrgnSrc', transaction.mailbox);

    // Make the HTTP request
    this.http.get(apiUrl, { params })
      .subscribe({
        next: (response: any) => {
          if (response && response.Response) {
            // Map API response to TransactionResult interface
            const apiResponse = response.Response;

            // Check for error in response
            if (apiResponse.ErrorCode || apiResponse.ErrorDesc) {
              this.showErrorToast(apiResponse.ErrorDesc || this.translateService.instant('INJAZ.UNKNOWN_ERROR'));
              this.updatingIndex = null;
              return;
            }

            // Determine status based on StatusDesc
            let status: 'completed' | 'in-progress' | 'pending' = 'pending';
            if (apiResponse.StatusDesc === 'منتهي') {
              status = 'completed';
            } else if (apiResponse.StatusDesc === 'تحت الإجراء' || apiResponse.StatusDesc === 'قيد المعالجة') {
              status = 'in-progress';
            }

            // Create result object
            const updatedResult: TransactionResult = {
              status: status,
              assignedTo: apiResponse.AssignToDeptDesc || apiResponse.AssignToDept || '',
              date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
              source: apiResponse.SelDesc || '',
              DocNo: apiResponse.DocNo || '',
              completionDate: apiResponse.OutDocDate || undefined,
              // Create a basic timeline based on available data
              timeline: [
                {
                  date: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[0] || '' : '',
                  time: apiResponse.DocDate ? apiResponse.DocDate.split(' ')[1] || '' : '',
                  title: 'تم استلام المعاملة',
                  description: 'تم استلام المعاملة من قبل نظام إنجاز',
                  active: true
                },
                {
                  date: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[0] || '' : '',
                  time: apiResponse.OutDocDate ? apiResponse.OutDocDate.split(' ')[1] || '' : '',
                  title: status === 'completed' ? 'اكتملت المعاملة' : 'تحت الإجراء',
                  description: status === 'completed' ? 'تم الانتهاء من جميع الإجراءات المتعلقة بالمعاملة' : undefined,
                  active: status === 'completed'
                }
              ]
            };

            // Update the transaction in the list
            if (this.recentTransactions[index]) {
              this.recentTransactions[index].result = updatedResult;

              // Save updated transactions to local storage
              localStorage.setItem('recentTransactions', JSON.stringify(this.recentTransactions));

              // Show success message
              this.showSuccessToast(this.translateService.instant('INJAZ.STATUS_UPDATED'));
            }
          } else {
            // Handle empty or invalid response
            this.showErrorToast(this.translateService.instant('INJAZ.INVALID_RESPONSE'));
          }
          this.updatingIndex = null;
        },
        error: (error: HttpErrorResponse) => {
          console.error('API Error:', error);
          this.updatingIndex = null;

          // Handle different types of errors
          if (error.status === 0) {
            // Network error
            this.showErrorToast(this.translateService.instant('INJAZ.NETWORK_ERROR'));
          } else if (error.status === 404) {
            // Not found
            this.showErrorToast(this.translateService.instant('INJAZ.NOT_FOUND'));
          } else {
            // Other errors
            this.showErrorToast(this.translateService.instant('INJAZ.SERVER_ERROR'));
          }
        }
      });
  }

  // Save transaction to local storage
  saveTransaction(formValue: any, result: TransactionResult | null) {
    const transaction: Transaction = {
      transactionNumber: formValue.transactionNumber,
      year: formValue.year,
      mailbox: formValue.mailbox,
      searchDate: new Date(),
      result: result
    };

    // Load existing transactions
    this.loadSavedTransactions();

    // Check if this transaction already exists
    const existingIndex = this.recentTransactions.findIndex(
      t => t.transactionNumber === transaction.transactionNumber &&
        t.year === transaction.year &&
        t.mailbox === transaction.mailbox
    );

    if (existingIndex !== -1) {
      // Update existing transaction
      this.recentTransactions[existingIndex] = transaction;
    } else {
      // Add new transaction
      this.recentTransactions.unshift(transaction);

      // Keep only the most recent 10 transactions
      if (this.recentTransactions.length > 10) {
        this.recentTransactions = this.recentTransactions.slice(0, 10);
      }
    }

    // Save to localStorage
    localStorage.setItem('injazTransactions', JSON.stringify(this.recentTransactions));
  }

  // Load saved transactions from localStorage
  loadSavedTransactions() {
    const savedTransactions = localStorage.getItem('injazTransactions');
    if (savedTransactions) {
      this.recentTransactions = JSON.parse(savedTransactions);
    }
  }

  // Load a previously saved transaction
  loadSavedTransaction(transaction: Transaction) {
    // Update search date
    const updatedTransaction = {
      ...transaction,
      searchDate: new Date()
    };

    // Update in storage
    const index = this.recentTransactions.findIndex(
      t => t.transactionNumber === transaction.transactionNumber &&
        t.year === transaction.year &&
        t.mailbox === transaction.mailbox
    );

    if (index !== -1) {
      this.recentTransactions[index] = updatedTransaction;
      localStorage.setItem('injazTransactions', JSON.stringify(this.recentTransactions));
    }

    // Navigate to transaction details page
    this.router.navigate(['/services/dms', transaction.transactionNumber], {
      queryParams: {
        year: transaction.year,
        mailbox: transaction.mailbox
      }
    });
  }


  // Reset form for a new search
  newSearch() {
    this.searchResult = null;
    this.showNoResults = false;
    this.activeSegment = 'search';
    this.transactionForm.reset({
      transactionNumber: '',
      year: '',
      mailbox: ''
    });
  }

  // Get mailbox name by ID
  getMailboxName(mailboxId: string): string {
    const mailbox = this.mailboxes.find(box => box.id === mailboxId);
    return mailbox ? mailbox.name : mailboxId;
  }

  // Get status icon
  getStatusIcon(status?: string): any {
    if (!status) return AlertCircleStrokeRounded;

    switch (status) {
      case 'completed':
        return CheckmarkCircleIconStrokeRounded;
      case 'in-progress':
        return TimeIconStrokeRounded;
      case 'pending':
        return TimeIconStrokeRounded;
      default:
        return AlertCircleStrokeRounded;
    }
  }


  // Get color class for status badges
  getStatusColor(status?: string): string {
    if (!status) return 'medium';

    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'pending':
        return 'primary';
      default:
        return 'medium';
    }
  }

  protected readonly SearchIconStrokeRounded = SearchIconStrokeRounded;

  // Navigation and state handling
  activeSegment = 'recent';

  // Status update tracking
  updatingIndex: number | null = null;

  // Change segment
  segmentChanged(event: any) {
    this.activeSegment = event.detail.value;
  }

  // Submit search form
  submitSearchForm() {
    if (this.transactionForm.valid) {
      this.searchTransaction();
      // Switch to recent transactions tab after search
      this.activeSegment = 'recent';
    }
  }

  removeTransaction(index: number) {
    if (index >= 0 && index < this.recentTransactions.length) {
      this.recentTransactions.splice(index, 1);
      localStorage.setItem('injazTransactions', JSON.stringify(this.recentTransactions));
      this.showSuccessToast(this.translateService.instant('INJAZ.TRANSACTION_REMOVED'));
    }
  }

  // Clear transaction history
  clearHistory() {
    this.recentTransactions = [];
    localStorage.setItem('injazTransactions', JSON.stringify(this.recentTransactions));
    this.showSuccessToast(this.translateService.instant('INJAZ.HISTORY_CLEARED'));
  }

  protected readonly CancelIconStrokeRounded = CancelIconStrokeRounded;
  protected readonly Add01StrokeRounded = Add01StrokeRounded;
  protected readonly AlertCircleStrokeRounded = AlertCircleStrokeRounded;
  protected readonly Alert01StrokeRounded = Alert01StrokeRounded;
}
