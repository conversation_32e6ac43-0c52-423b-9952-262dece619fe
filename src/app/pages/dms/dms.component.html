<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/services"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'INJAZ.TITLE' | translate }}</ion-title>
  </ion-toolbar>

  <!-- Segment tabs -->
  <ion-toolbar>
    <ion-segment [value]="activeSegment" (ionChange)="segmentChanged($event)">
      <ion-segment-button value="recent">
        <ion-label>{{ 'INJAZ.RECENT_TRANSACTIONS' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="search">
        <ion-label>{{ 'INJAZ.NEW_SEARCH' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <!-- No Results View -->
  <div *ngIf="showNoResults" class="no-results-container">
        <div class="ion-text-center">
          <hugeicons-icon [icon]="Alert01StrokeRounded" size="48" [strokeWidth]="1.5" color="#c5000f"></hugeicons-icon>
          <h3>{{ 'INJAZ.NO_RESULTS_TITLE' | translate }}</h3>
          <p>{{ 'INJAZ.NO_RESULTS_MESSAGE' | translate }}</p>
          <ion-button color="primary" (click)="newSearch()">
            <hugeicons-icon [icon]="SearchIcon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
            <span class="ion-padding-start">{{ 'INJAZ.TRY_AGAIN' | translate }}</span>
          </ion-button>
        </div>
  </div>

  <!-- Recent Transactions Tab -->
  <div *ngIf="!showNoResults && activeSegment === 'recent'" class="main-view">
    <!-- Empty State -->
    <div *ngIf="recentTransactions.length === 0" class="empty-state">

      <div class="ion-text-center">
        <hugeicons-icon [icon]="SearchIconStrokeRounded" size="56" [strokeWidth]="1.5"
                        class="empty-icon"></hugeicons-icon>
        <h3>{{ 'INJAZ.EMPTY_TITLE' | translate }}</h3>
        <p>{{ 'INJAZ.EMPTY_MESSAGE' | translate }}</p>
        <ion-button color="primary" (click)="activeSegment = 'search'">
          <hugeicons-icon [icon]="SearchIcon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
          <span class="ion-padding-start">{{ 'INJAZ.START_SEARCH' | translate }}</span>
        </ion-button>
      </div>
    </div>

    <!-- Recent Transactions List -->
    <div *ngIf="recentTransactions.length > 0" class="transactions-container">
      <div class="list-header-actions">
        <ion-button fill="clear" color="medium" size="small" (click)="clearHistory()" slot="end">
          <hugeicons-icon [icon]="TrashIcon" size="16" [strokeWidth]="1.5" class="ion-mar"/>
          &nbsp;
          {{ 'INJAZ.CLEAR_HISTORY' | translate }}
        </ion-button>
      </div>

      <ion-list class="transactions-list">
        <ion-item-sliding *ngFor="let transaction of recentTransactions; let i = index">
          <ion-item button detail="false" (click)="loadSavedTransaction(transaction)" class="transaction-item">
            <ion-label>
              <div class="transaction-header">
                <h2 class="transaction-number">{{ transaction.transactionNumber }} / {{ transaction.year }}</h2>
                <h3>
                  {{ transaction.result?.assignedTo }}
                </h3>
              </div>
              <div class="transaction-details">
                <ion-note color="medium">
                  <hugeicons-icon [icon]="MailboxIcon" size="16" [strokeWidth]="1.5"></hugeicons-icon>
                  {{ getMailboxName(transaction.mailbox) }}
                </ion-note>
              </div>
            </ion-label>
            <ion-button *ngIf="transaction.result?.status !== 'completed'"
                        slot="end"
                        fill="clear"
                        color="primary"
                        [disabled]="updatingIndex === i"
                        (click)="updateTransactionStatus(transaction, i); $event.stopPropagation();">
              <hugeicons-icon [icon]="RefreshIcon" size="18" [strokeWidth]="1.5"></hugeicons-icon>
            </ion-button>
          </ion-item>
          <ion-item-options side="end">
            <ion-item-option color="danger" (click)="removeTransaction(i)">
              <hugeicons-icon [icon]="TrashIcon" size="16" [strokeWidth]="1.5"/>

            </ion-item-option>
          </ion-item-options>
        </ion-item-sliding>
      </ion-list>
    </div>
  </div>

  <!-- New Search Tab -->
  <div *ngIf="!showNoResults && activeSegment === 'search'" class="search-view">

    <div class="section-header">
      <div class="section-title-container">
        <h2 class="section-title">{{ 'INJAZ.ABOUT_INJAZ' | translate }}</h2>
        <div class="section-underline"></div>
      </div>
    </div>
    <div>
      <p class="description-text">{{ 'INJAZ.SYSTEM_DESCRIPTION' | translate }}</p>
    </div>

    <div class="injaz-card">
      <div>
        {{ 'INJAZ.SEARCH_TITLE' | translate }}
        <p class="section-content">{{ 'INJAZ.SEARCH_DESCRIPTION' | translate }}</p>
      </div>
      <form [formGroup]="transactionForm" (ngSubmit)="submitSearchForm()">
          <ion-item lines="none">

          <ion-input
            type="text"
            formControlName="transactionNumber"
            placeholder="{{ 'INJAZ.TRANSACTION_NUMBER' | translate }}"
            [clearInput]="true"
            inputmode="numeric"
            pattern="[0-9]*"
            fill="solid"
            errorText="{{ 'INJAZ.TRANSACTION_NUMBER_REQUIRED' | translate }}"
          ></ion-input>
          </ion-item>

        <ion-item>
          <ion-select
            formControlName="year"
            interface="action-sheet"
            placeholder="{{ 'INJAZ.SELECT_YEAR' | translate }}"
            [cancelText]="'COMMON.CANCEL' | translate"
            label-placement="floating"
            fill="outline"
            labelPlacement="stacked"
            [errorText]="'INJAZ.YEAR_REQUIRED' | translate"
          >
            <ion-select-option *ngFor="let year of hijriYears" [value]="year">{{ year }}</ion-select-option>
          </ion-select>
        </ion-item>

        <ion-item>
          <ion-select
            formControlName="mailbox"
            interface="action-sheet"
            placeholder="{{ 'INJAZ.SELECT_MAILBOX' | translate }}"
            [cancelText]="'COMMON.CANCEL' | translate"
            label-placement="floating"
            fill="outline"
            labelPlacement="stacked"
            [errorText]="'INJAZ.MAILBOX_REQUIRED' | translate"

          >
            <ion-select-option *ngFor="let box of mailboxes" [value]="box.id">{{ box.name }}</ion-select-option>
          </ion-select>
        </ion-item>


          <ion-button
            type="submit"
            color="primary"
            class="action-button"
            [disabled]="transactionForm.invalid || isLoading"
          >
            <hugeicons-icon [icon]="SearchIcon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
            <span *ngIf="!isLoading" class="ion-padding-start">{{ 'INJAZ.SEARCH' | translate }}</span>
            <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          </ion-button>
      </form>
    </div>
  </div>
</ion-content>
