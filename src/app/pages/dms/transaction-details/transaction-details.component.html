<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/services/dms"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'INJAZ.TRANSACTION_DETAILS' | translate }}</ion-title>
    <ion-buttons slot="end" *ngIf="transaction?.result && transaction?.result?.status !== 'completed'">
      <ion-button (click)="refreshTransactionStatus()" [disabled]="isRefreshing">
        <ion-icon *ngIf="isRefreshing" name="sync" class="rotating-icon"></ion-icon>
        <hugeicons-icon *ngIf="!isRefreshing" [icon]="RefreshIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="circular"></ion-spinner>
    <p>{{ 'INJAZ.LOADING' | translate }}</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <ion-card class="error-card">
      <ion-card-content>
        <div class="ion-text-center">
          <hugeicons-icon [icon]="AlertCircleIcon" size="48" [strokeWidth]="1.5" class="error-icon"></hugeicons-icon>
          <h3>{{ 'INJAZ.ERROR_TITLE' | translate }}</h3>
          <p>{{ error }}</p>
          <ion-button expand="block" color="primary" (click)="goBack()">
            <ion-icon name="arrow-back" slot="start"></ion-icon>
            {{ 'INJAZ.BACK_TO_LIST' | translate }}
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Transaction Details -->
  <div *ngIf="transaction?.result && !isLoading && !error" class="transaction-details">
    <div class="section-header">
      <div class="section-title-container">
        <h2 class="section-title">{{ 'INJAZ.TRANSACTION_DETAILS' | translate }}</h2>
        <div class="section-underline"></div>
      </div>
    </div>
        <ion-list lines="full">
          <ion-item *ngIf="transaction?.result?.status">
            <ion-label position="stacked">{{ 'INJAZ.STATUS' | translate }}</ion-label>
            <ion-text>   {{ getStatusText(transaction?.result?.status) | translate }}</ion-text>
          </ion-item>
          <ion-item *ngIf="transaction?.result?.date">
            <ion-label position="stacked">{{ 'INJAZ.TRANSACTION_DATE' | translate }}</ion-label>
            <ion-text>{{ transaction?.result?.date || '' }}</ion-text>
          </ion-item>
          <ion-item *ngIf="transaction?.result?.DocNo">
            <ion-label position="stacked">{{ 'INJAZ.DOCNO' | translate }}</ion-label>
            <ion-text>{{ transaction?.result?.DocNo || '' }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">{{ 'INJAZ.ASSIGNED_TO' | translate }}</ion-label>
            <ion-text>{{ transaction?.result?.assignedTo || '' }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">{{ 'INJAZ.SOURCE' | translate }}</ion-label>
            <ion-text>{{ transaction?.result?.source || '' }}</ion-text>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">{{ 'INJAZ.MAILBOX' | translate }}</ion-label>
            <ion-text>{{ getMailboxName(transaction?.mailbox || '') }}</ion-text>
          </ion-item>
        </ion-list>


    <div class="section-header">
      <div class="section-title-container">

        <h2 class="section-title">
          <hugeicons-icon [icon]="TimelineIcon" size="22" [strokeWidth]="1.5"></hugeicons-icon>
          {{ 'INJAZ.TRANSACTION_HISTORY' | translate }}</h2>
        <div class="section-underline"></div>
      </div>
    </div>
        <div class="timeline" *ngIf="transaction?.result?.timeline?.length">
          <div class="timeline-item" *ngFor="let event of transaction?.result?.timeline || []; let last = last" [ngClass]="{'last-item': last}">
            <div class="timeline-marker" [ngClass]="{'active': event.active}"></div>
            <div class="timeline-content">
              <div class="timeline-date">{{ event.date }} <span *ngIf="event.time" class="timeline-time">{{ event.time }}</span></div>
              <div class="timeline-title">{{ event.title }}</div>
              <div class="timeline-description" *ngIf="event.description">{{ event.description }}</div>
            </div>
          </div>
        </div>
  </div>
</ion-content>
