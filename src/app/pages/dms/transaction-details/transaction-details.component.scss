// Loading and Error States
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  text-align: center;
  padding: 20px;
}

.loading-container {
  ion-spinner {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 1rem;
  }
}

.error-card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

  ion-card-content {
    padding: 24px;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 16px 0 8px;
    color: var(--ion-color-dark);
  }

  p {
    font-size: 0.95rem;
    color: var(--ion-color-medium);
    margin-bottom: 24px;
    line-height: 1.5;
  }

  ion-button {
    margin-top: 8px;
  }
}

.error-icon {
  color: var(--ion-color-danger-tint);
}

// Transaction Details
.transaction-details {
  padding-bottom: 24px;
}

.status-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &.status-completed {
    --background: rgba(var(--ion-color-success-rgb), 0.1);
    ion-card-title, ion-card-subtitle {
      color: var(--ion-color-success);
    }
  }

  &.status-in-progress {
    --background: rgba(var(--ion-color-warning-rgb), 0.1);
    ion-card-title, ion-card-subtitle {
      color: var(--ion-color-warning);
    }
  }

  &.status-pending {
    --background: rgba(var(--ion-color-primary-rgb), 0.1);
    ion-card-title, ion-card-subtitle {
      color: var(--ion-color-primary);
    }
  }

  ion-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
  }
}

.details-grid {
  padding: 0;

  ion-card {
    margin: 0;
    height: 100%;
  }
}

.info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  ion-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
  }

  ion-card-subtitle {
    font-size: 0.8rem;
  }
}

.details-card, .timeline-card {
  margin-top: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  ion-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
  }

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
  }

  ion-label {
    margin-bottom: 4px;
    color: var(--ion-color-medium);
  }

  ion-text {
    font-size: 1rem;
    color: var(--ion-color-dark);
  }
}

// Timeline
.timeline {
  position: relative;
  margin: 16px 0;
  padding-left: 28px;
}

.timeline-item {
  position: relative;
  padding-bottom: 24px;

  &.last-item {
    padding-bottom: 0;
  }

  &:not(.last-item)::before {
    content: '';
    position: absolute;
    top: 12px;
    left: -20px;
    height: calc(100% - 12px);
    width: 2px;
    background-color: var(--ion-color-light-shade);
  }
}

.timeline-marker {
  position: absolute;
  top: 6px;
  left: -28px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--ion-color-light);
  border: 2px solid var(--ion-color-medium);
  z-index: 1;

  &.active {
    background-color: var(--ion-color-primary);
    border-color: var(--ion-color-primary-shade);
  }
}

.timeline-content {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  padding: 12px 16px;
}

.timeline-date {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;

  .timeline-time {
    display: inline-block;
    margin-right: 5px;
    font-weight: 500;
    color: var(--ion-color-primary);
  }
}

.timeline-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--ion-color-dark);
  margin-bottom: 4px;
}

.timeline-description {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  line-height: 1.4;
}

// Animation
.rotating-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// ==================
// Section Header
// ==================
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 4px 4px;
  position: relative;
}

.section-title-container {
  position: relative;
  padding-bottom: 8px;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1.2;
  display: inline-block;
  position: relative;
}

.section-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(90deg,
    var(--ion-color-primary) 0%,
    var(--ion-color-primary-shade) 70%,
    rgba(var(--ion-color-primary-rgb), 0.5) 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}
