import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ToastController } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { HugeiconsIconComponent } from '@hugeicons/angular';
import { Transaction, TransactionResult } from '../models/transaction.model';
import { InjazService } from '../services/injaz.service';
import {
  AlertCircleStrokeRounded,
  BuildingIconStrokeRounded,
  CalendarIconStrokeRounded,
  CheckmarkCircleIconStrokeRounded,
  Clock05StrokeRounded,
  RefreshIcon,
  TimeIconStrokeRounded
} from '@hugeicons-pro/core-stroke-rounded';

@Component({
  selector: 'app-transaction-details',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    TranslateModule,
    HugeiconsIconComponent
  ],
  templateUrl: './transaction-details.component.html',
  styleUrls: ['./transaction-details.component.scss'],
})
export class TransactionDetailsComponent implements OnInit {
  transaction: Transaction | null = null;
  isLoading = true;
  error: string | null = null;
  isRefreshing = false;

  // Icons
  CheckCircleIcon = CheckmarkCircleIconStrokeRounded;
  AlertCircleIcon = AlertCircleStrokeRounded;
  CalendarIcon = CalendarIconStrokeRounded;
  BuildingIcon = BuildingIconStrokeRounded;
  ClockIcon = Clock05StrokeRounded;
  TimelineIcon = TimeIconStrokeRounded;
  RefreshIcon = RefreshIcon;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private transactionService: InjazService,
    private toastController: ToastController,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadTransactionDetails();
  }

  /**
   * Load transaction details from route parameters
   */
  loadTransactionDetails(): void {
    this.isLoading = true;
    this.error = null;

    // Get route parameters
    const docNumber = this.route.snapshot.paramMap.get('docNumber');
    const year = this.route.snapshot.queryParamMap.get('year');
    const mailbox = this.route.snapshot.queryParamMap.get('mailbox');

    if (!docNumber) {
      this.error = this.translateService.instant('INJAZ.INVALID_TRANSACTION');
      this.isLoading = false;
      return;
    }

    // First try to get from local storage
    this.transactionService.getTransactionById(docNumber, year || undefined, mailbox || undefined)
      .subscribe({
        next: (transaction) => {
          if (transaction) {
            this.transaction = transaction;
            this.isLoading = false;
          } else if (year && mailbox) {
            // If not found in storage but we have all parameters, fetch from API
            this.fetchFromApi(docNumber, year, mailbox);
          } else {
            this.error = this.translateService.instant('INJAZ.TRANSACTION_NOT_FOUND');
            this.isLoading = false;
          }
        },
        error: (err) => {
          this.handleError(err);
        }
      });
  }

  /**
   * Fetch transaction details from API
   */
  fetchFromApi(docNumber: string, year: string, mailbox: string): void {
    this.isLoading = true;
    this.error = null;

    this.transactionService.getTransactionDetails(docNumber, year, mailbox)
      .subscribe({
        next: (result) => {
          // Create transaction object
          const transaction: Transaction = {
            transactionNumber: docNumber,
            year: year,
            mailbox: mailbox,
            searchDate: new Date(),
            result: result
          };

          // Save to local storage
          this.transactionService.saveTransaction(transaction);

          // Update component state
          this.transaction = transaction;
          this.isLoading = false;
        },
        error: (err) => {
          this.handleError(err);
        }
      });
  }

  /**
   * Refresh transaction status
   */
  refreshTransactionStatus(): void {
    if (!this.transaction) return;

    this.isRefreshing = true;

    const { transactionNumber, year, mailbox } = this.transaction;

    this.transactionService.getTransactionDetails(transactionNumber, year, mailbox)
      .subscribe({
        next: (result) => {
          if (this.transaction) {
            // Update the transaction with new result
            this.transaction.result = result;
            this.transaction.searchDate = new Date();

            // Save to local storage
            this.transactionService.saveTransaction(this.transaction);

            // Show success message
            this.showSuccessToast(this.translateService.instant('INJAZ.STATUS_UPDATED'));
          }
          this.isRefreshing = false;
        },
        error: (err) => {
          this.handleError(err);
          this.isRefreshing = false;
        }
      });
  }

  /**
   * Navigate back to the DMS page
   */
  goBack(): void {
    this.router.navigate(['/services/dms']);
  }

  /**
   * Handle errors
   */
  private handleError(error: any): void {
    console.error('Error:', error);
    this.error = error.message || this.translateService.instant('INJAZ.UNKNOWN_ERROR');
    this.isLoading = false;
    this.showErrorToast(this.error || this.translateService.instant('INJAZ.UNKNOWN_ERROR'));
  }

  /**
   * Show error toast
   */
  private async showErrorToast(message: string): Promise<void> {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      position: 'bottom',
      color: 'danger',
      buttons: [{
        text: 'OK',
        role: 'cancel'
      }]
    });
    await toast.present();
  }

  /**
   * Show success toast
   */
  private async showSuccessToast(message: string): Promise<void> {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom',
      color: 'success',
      buttons: [{
        text: 'OK',
        role: 'cancel'
      }]
    });
    await toast.present();
  }

  /**
   * Get mailbox name by ID
   */
  getMailboxName(mailboxId: string): string {
    return this.transactionService.getMailboxName(mailboxId);
  }

  // Get translated status text
  getStatusText(status?: string): string {
    if (!status) return 'INJAZ.STATUS_UNKNOWN';

    switch (status) {
      case 'completed':
        return 'INJAZ.STATUS_COMPLETED';
      case 'in-progress':
        return 'INJAZ.STATUS_IN_PROGRESS';
      case 'pending':
        return 'INJAZ.STATUS_PENDING';
      default:
        return 'INJAZ.STATUS_UNKNOWN';
    }
  }
}
