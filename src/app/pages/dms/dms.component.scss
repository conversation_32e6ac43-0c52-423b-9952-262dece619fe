// ==================
// Section Header
// ==================
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 4px 4px;
  position: relative;
}

.section-title-container {
  position: relative;
  padding-bottom: 8px;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1.2;
  display: inline-block;
  position: relative;
}

.section-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(90deg,
    var(--ion-color-primary) 0%,
    var(--ion-color-primary-shade) 70%,
    rgba(var(--ion-color-primary-rgb), 0.5) 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}



// ==================
// Advisor Card
// ==================
.injaz-card {
  background-color: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }
}

.list-header-actions{
  display: flex;
  justify-content: end;
}
