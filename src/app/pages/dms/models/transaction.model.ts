export interface Transaction {
  transactionNumber: string;
  year: string;
  mailbox: string;
  searchDate: Date;
  result?: TransactionResult;
}

export interface TransactionResult {
  status: 'completed' | 'in-progress' | 'pending';
  assignedTo: string;
  date: string;
  source: string;
  DocNo?: string;
  completionDate?: string;
  timeline?: TimelineEvent[];
}

export interface TimelineEvent {
  date: string;
  time?: string;
  title: string;
  description?: string;
  active: boolean;
}

export interface Mailbox {
  id: string;
  name: string;
}
