<ion-header>
  <ion-toolbar>
    <ion-title>{{ 'ACADEMIC.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <app-student-card *ngIf="user()" [student]="{name: user()!.name,
    universityId: user()!.student_id!,
    college: user()!.faculty!.name,
    major: user()!.major!.name,
    expiry: '2025-05-16'}"></app-student-card>
  <div class="cards-grid">
    <app-home-action-card *ngFor="let card of actionCards" [icon]="card.icon" [label]="card.label | translate"
      [bgColor]="card.bgColor" [iconBg]="card.iconBg" (click)="onCardClick(card)"></app-home-action-card>
  </div>
</ion-content>