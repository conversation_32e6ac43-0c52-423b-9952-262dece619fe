<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'ABSENCES.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="refresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">{{ 'ABSENCES.TITLE' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading State -->
  <div *ngIf="loading()" class="ion-text-center ion-padding">
    <ion-spinner name="lines"></ion-spinner>
    <p>{{ 'ABSENCES.LOADING' | translate }}</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading()">
    <!-- Section Header -->
    <ion-item-group *ngIf="absences().length > 0; else noCourses">
      <ion-item-divider>
        <ion-label>
          <h2>{{ 'ABSENCES.COURSES_LIST' | translate }}</h2>
        </ion-label>
      </ion-item-divider>

      <!-- Courses List -->
      <ion-list>
        <ion-item
          *ngFor="let course of absences()"
          button
          detail="false"
          (click)="showAbsenceDetails(course)">

          <div slot="start" [ngClass]="'status-icon-' + getIconColor(course)">
            <hugeicons-icon
              [icon]="getStatusIcon(course)"
              size="24"
              [strokeWidth]="1.5"/>
          </div>

          <ion-label>
            <h3>{{ getDisplayCourseName(course) }}</h3>
            <p>{{ course.cource_code }}</p>
          </ion-label>

          <ion-badge
            slot="end"
            [color]="getBadgeColor(course)">
            {{ calculateNetAbsencePercent(course) | number:'1.0-1' }}%
          </ion-badge>
        </ion-item>
      </ion-list>
    </ion-item-group>

    <!-- Empty State -->
    <ng-template #noCourses>
      <div class="ion-text-center ion-padding">
        <hugeicons-icon [icon]="FileNotFoundStrokeRounded" size="48" [strokeWidth]="1.5"></hugeicons-icon>
        <h3>{{ 'ABSENCES.NO_ABSENCES_TITLE' | translate }}</h3>
        <p color="medium">{{ 'ABSENCES.NO_ABSENCES_MESSAGE' | translate }}</p>
      </div>
    </ng-template>
  </div>

  <!-- Course Details Modal -->
  <ion-modal
    [isOpen]="isBottomSheetOpen"
    (didDismiss)="dismissBottomSheet()"
    [breakpoints]="[0, 0.95]"
    [initialBreakpoint]="0.95"
    [backdropBreakpoint]="0">

    <ng-template>
      <ion-header>
        <ion-toolbar [color]="getModalHeaderColor()">
          <ion-title>{{ selectedCourseName }}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="dismissBottomSheet()" fill="clear">
              <hugeicons-icon [icon]="CancelIcon" size="24" [strokeWidth]="1.5" slot="icon-only"/>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content>
        <div *ngIf="selectedCourseAbsences.length > 0; else noAbsences">
          <!-- Summary Cards -->
          <ion-grid>
            <ion-row>
              <ion-col size="4">
                <ion-card class="summary-card">
                  <ion-card-content class="ion-text-center">
                      <hugeicons-icon [icon]="CalendarIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                    <h3>{{ selectedCourseAbsences.length }}</h3>
                    <p>{{ 'ABSENCES.TOTAL' | translate }}</p>
                  </ion-card-content>
                </ion-card>
              </ion-col>

              <ion-col size="4">
                <ion-card class="summary-card">
                  <ion-card-content class="ion-text-center">
                      <hugeicons-icon [icon]="CheckmarkCircleIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                    <h3>{{ getExcusedCount() }}</h3>
                    <p>{{ 'ABSENCES.EXCUSED' | translate }}</p>
                  </ion-card-content>
                </ion-card>
              </ion-col>

              <ion-col size="4">
                <ion-card class="summary-card">
                  <ion-card-content class="ion-text-center">
                      <hugeicons-icon [icon]="CancelIcon" size="24" [strokeWidth]="1.5"></hugeicons-icon>
                    <h3>{{ selectedCourseAbsences.length - getExcusedCount() }}</h3>
                    <p>{{ 'ABSENCES.UNEXCUSED_SHORT' | translate }}</p>
                  </ion-card-content>
                </ion-card>
              </ion-col>
            </ion-row>
          </ion-grid>

          <!-- Absence Details List -->
          <ion-item-group>
            <ion-item-divider>
              <ion-label>
                <h2>
                  <hugeicons-icon [icon]="ListViewIcon" size="20" [strokeWidth]="1.5"></hugeicons-icon>
                  {{ 'ABSENCES.DETAILS_LIST' | translate }}
                </h2>
              </ion-label>
            </ion-item-divider>

            <ion-list>
              <ion-item *ngFor="let detail of selectedCourseAbsences">
                  <hugeicons-icon
                    slot="start"
                    [icon]="CalendarIcon"
                    size="24"
                    [strokeWidth]="1.5">
                  </hugeicons-icon>

                <ion-label>
                  <h3>
                    {{ getLocaleFormattedDate(detail.absence_date) }}
                  </h3>
                  <p>
                    {{ 'DAYS.' + detail.absence_day | translate }} •
                    {{ detail.start_time | formatSecondsToTime }} - {{ detail.end_time | formatSecondsToTime }}
                  </p>
                </ion-label>

                <div slot="end" class="ion-text-right">
                  <ion-badge
                    [color]="detail.absence_excused === '1' ? 'success' : 'danger'"
                    class="ion-margin-vertical">
                    {{ detail.absence_excused === '1' ? ('ABSENCES.EXCUSED' | translate) : ('ABSENCES.UNEXCUSED_SHORT' | translate) }}
                  </ion-badge>

                  <br *ngIf="detail.absence_late === '1'">

                  <ion-badge
                    *ngIf="detail.absence_late === '1'"
                    color="warning">
                    {{ 'ABSENCES.LATE' | translate }}
                  </ion-badge>
                </div>
              </ion-item>
            </ion-list>
          </ion-item-group>
        </div>

        <!-- No Absences in Modal -->
        <ng-template #noAbsences>
          <div class="ion-text-center ion-padding">
            <hugeicons-icon [icon]="CheckmarkCircleIcon" size="48" [strokeWidth]="1.5"></hugeicons-icon>
            <h3>{{ 'ABSENCES.NO_DETAILS_TITLE' | translate | titlecase }}</h3>
            <p color="medium">{{ 'ABSENCES.NO_DETAILS_FOR_COURSE' | translate | titlecase }}</p>
          </div>
        </ng-template>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
