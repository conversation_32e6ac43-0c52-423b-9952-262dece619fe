import {
  Component,
  OnInit,
  ChangeDetectorRef,
  signal,
  effect,
} from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AbsenceService } from '../../services/absence/absence.service';
import { Absences, Absences as CourseAbsences } from '../../models/app.models';
import { FormatSecondsToTimePipe } from '../../pipes/format-seconds-to-time.pipe';
import { addIcons } from 'ionicons';
import {
  documentTextOutline,
  personOutline,
  timeOutline,
  chevronDownCircleOutline,
  closeCircleOutline,
} from 'ionicons/icons';
import { HugeiconsIconComponent } from "@hugeicons/angular";
import {
  Alert02StrokeRounded,
  CalendarIcon,
  CancelIcon,
  CheckmarkCircleIcon,
  ClockIcon,
  FileNotFoundStrokeRounded,
  ListViewIcon, Time04Icon, Time04StrokeRounded,
  TimeIcon,
  Triangle01StrokeRounded,
} from "@hugeicons-pro/core-stroke-rounded";
import {CalendarIconSolidRounded, Time04SolidRounded} from "@hugeicons-pro/core-solid-rounded";

@Component({
  selector: 'app-absences',
  templateUrl: './absences.component.html',
  styleUrls: ['./absences.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    TranslateModule,
    FormatSecondsToTimePipe,
    HugeiconsIconComponent,
  ],
  providers: [DatePipe],
})
export class AbsencesComponent implements OnInit {
  // Bottom sheet properties
  isBottomSheetOpen = false;
  selectedCourseAbsences: CourseAbsences['absences'] = [];
  selectedCourseName: string = '';

  // Make HugeIcons accessible in template
  protected readonly CalendarIcon = CalendarIcon;
  protected readonly CancelIcon = CancelIcon;
  protected readonly CheckmarkCircleIcon = CheckmarkCircleIcon;
  protected readonly ClockIcon = ClockIcon;
  protected readonly TimeIcon = TimeIcon;
  protected readonly ListViewIcon = ListViewIcon;
  protected readonly FileNotFoundStrokeRounded = FileNotFoundStrokeRounded;

  constructor(
    private absenceService: AbsenceService,
    private modalController: ModalController,
    private cdr: ChangeDetectorRef,
    private translateService: TranslateService,
    private datePipe: DatePipe
  ) {
    addIcons({
      personOutline,
      timeOutline,
      documentTextOutline,
      chevronDownCircleOutline,
      closeCircleOutline,
    });
  }

  absences = signal<Absences[]>([]);
  loading = signal<boolean>(true);

  // Overall stats
  overallTotalAbsences = signal<number>(0);
  overallExcusedAbsences = signal<number>(0);
  overallUnexcusedAbsences = signal<number>(0);

  // Change them to effects
  totalAbsences = effect(() => {
    this.overallTotalAbsences.set(
      this.absences().reduce((acc, course) => acc + course.absences.length, 0)
    );
    this.overallExcusedAbsences.set(
      this.absences().reduce(
        (acc, course) =>
          acc +
          course.absences.filter((absence) => absence.absence_excused === '1')
            .length,
        0
      )
    );
    this.overallUnexcusedAbsences.set(
      this.absences().reduce(
        (acc, course) =>
          acc +
          course.absences.filter((absence) => absence.absence_excused === '0')
            .length,
        0
      )
    );
  });

  ngOnInit() {
    this.absences = this.absenceService.absences;
    this.loading = this.absenceService.loading;
  }

  calculateNetAbsencePercent(course: CourseAbsences): number {
    const allPercent = parseFloat(course.absence_all_percent);
    const excusedPercent = parseFloat(course.absence_excused_percent);
    if (isNaN(allPercent) || isNaN(excusedPercent)) {
      return 0;
    }
    return allPercent - excusedPercent;
  }

  getDisplayCourseName(course: CourseAbsences): string {
    if (this.translateService.currentLang === 'en' && course.cource_name_en) {
      return course.cource_name_en;
    }
    return course.cource_name;
  }

  getLocaleFormattedDate(dateString: string): string {
    const currentLang = this.translateService.currentLang;
    const format = 'dd-MM-yyyy';

    try {
      return (
        this.datePipe.transform(dateString, format, undefined, currentLang) ||
        dateString
      );
    } catch (error) {
      console.error('Error formatting date:', error);
      return this.datePipe.transform(dateString, format) || dateString;
    }
  }

  showAbsenceDetails(course: CourseAbsences) {
    this.selectedCourseName = this.getDisplayCourseName(course);
    this.selectedCourseAbsences = course.absences;
    this.isBottomSheetOpen = true;
    this.cdr.detectChanges();
  }

  dismissBottomSheet() {
    this.isBottomSheetOpen = false;
  }

  refresh(event: any) {
    this.absenceService.refreshAbsences();
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  // Helper methods for HugeIcons and styling
  getStatusIcon(course: Absences): any {
    const percentage = this.calculateNetAbsencePercent(course);
    if (percentage > 5) return Alert02StrokeRounded;
    return CheckmarkCircleIcon;
  }

  getIconColor(course: Absences): string {
    const percentage = this.calculateNetAbsencePercent(course);
    if (percentage > 15) return 'danger';
    if (percentage > 5) return 'warning';
    return 'success';
  }

  getBadgeColor(course: Absences): string {
    const percentage = this.calculateNetAbsencePercent(course);
    if (percentage > 15) return 'danger';
    if (percentage > 5) return 'warning';
    return 'success';
  }

  getModalHeaderColor(): string {
    const selectedCourse = this.absences().find(course =>
      this.getDisplayCourseName(course) === this.selectedCourseName
    );

    if (!selectedCourse) return 'primary';

    const percentage = this.calculateNetAbsencePercent(selectedCourse);
    if (percentage > 15) return 'danger';
    if (percentage > 5) return 'warning';
    return 'primary';
  }

  getStatusClass(): string {
    if (!this.selectedCourseAbsences.length) return '';

    const percent = this.calculateSelectedCourseAbsencePercent();
    if (percent > 15) return 'danger';
    if (percent > 5) return 'warning';
    return '';
  }

  calculateSelectedCourseAbsencePercent(): number {
    const selectedCourse = this.absences().find(course =>
      this.getDisplayCourseName(course) === this.selectedCourseName
    );

    return selectedCourse ? this.calculateNetAbsencePercent(selectedCourse) : 0;
  }

  getExcusedCount(): number {
    return this.selectedCourseAbsences.filter(a => a.absence_excused === '1').length;
  }

  protected readonly Time04StrokeRounded = Time04StrokeRounded;
  protected readonly Time04Icon = Time04Icon;
  protected readonly Time04SolidRounded = Time04SolidRounded;
  protected readonly CalendarIconSolidRounded = CalendarIconSolidRounded;
}
