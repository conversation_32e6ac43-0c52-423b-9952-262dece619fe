.status-icon-success {
  color: var(--ion-color-success);
}

.status-icon-warning {
  color: var(--ion-color-warning);
}

.status-icon-danger {
  color: var(--ion-color-danger);
}

.status-icon-primary {
  color: var(--ion-color-primary);
}

.summary-card {
  margin: 8px 0;
  box-shadow: unset;
  border-radius: 12px;
  border: 1px solid #e9ecef;


  ion-card-content {
    padding: 16px 8px;
    font-weight: 600;

    h3 {
      margin: 8px 0 4px 0;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
      color: var(--ion-text-color);
    }
  }
}
