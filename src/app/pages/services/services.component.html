<ion-header>
  <ion-toolbar>
    <ion-title>الخدمات</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col size="12">
        <section class="actions-section">
          <h2 class="section-title">{{ 'HOME.QUICK_ACTIONS' | translate }}</h2>
          <div class="cards-grid">
            <app-home-action-card *ngFor="let card of actionCards" [icon]="card.icon" [label]="card.label"
              [bgColor]="card.bgColor" [iconBg]="card.iconBg" (cardClick)="onCardClick(card)">
            </app-home-action-card>
          </div>
        </section>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>