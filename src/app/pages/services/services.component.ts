import { Component, OnInit, signal } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { HomeActionCardComponent } from '../../components/home-action-card/home-action-card.component';
import { Router } from '@angular/router';
import { ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ApiService } from 'src/app/services/api/api.service';

@Component({
  selector: 'app-services',
  templateUrl: './services.component.html',
  styleUrls: ['./services.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    CommonModule,
    HomeActionCardComponent,
    TranslateModule,
  ],
})
export class ServicesComponent implements OnInit {
  constructor(
    private router: Router,
    private toastController: ToastController,
    private translate: TranslateService
  ) {}

  ngOnInit() {}

  actionCards = [
    {
      icon: 'user-multiple-02',
      label: 'خدمة موعد',
      bgColor: '#f4fcf8',
      iconBg: '#3ba86d',
      route: 'appointment',
      isComingSoon: true,
    },
    {
      icon: 'folder-01',
      label: 'إنجاز',
      bgColor: '#f0f7ff',
      iconBg: '#3ba86d',
      route: 'services/dms',
    },
  ];

  async onCardClick(card: any) {
    try {
      // Navigate to the route if it exists
      if (card.route && !card.isComingSoon) {
        this.router.navigate([card.route]);
      } else {
        // Show a toast if no route is defined
        const toast = await this.toastController.create({
          message: `${this.translate.instant(
            card.label
          )} ${this.translate.instant('common.coming_soon')}`,
          duration: 2000,
          position: 'top',
          color: 'primary',
          cssClass: 'toast-message',
        });
        await toast.present();
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }
}
