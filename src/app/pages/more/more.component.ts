import { Component, inject } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LanguageService } from '../../services/language/language.service';
import { AuthService } from '../../services/auth/auth.service';
import { TranslateModule } from '@ngx-translate/core';
import { Platform } from '@ionic/angular';
import { InAppBrowser } from '@capgo/inappbrowser';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-more',
  templateUrl: './more.component.html',
  styleUrls: ['./more.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule, TranslateModule],
})
export class MoreComponent {
  constructor(private platform: Platform, private authService: AuthService) {}

  readonly isAuthenticated = this.authService.isAuthenticated;
  private apiUrl = environment.apiUrl;

  async logout(): Promise<void> {
    try {
      await this.authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
      // For demo purposes, just navigate to home
      window.location.href = '/';
    }
  }

  async login(): Promise<void> {
    console.log(this.platform.is('capacitor'));
    if (!this.platform.is('capacitor')) {
      // its a browser, get token from an input
      const token = prompt('Enter your token');
      if (token) {
        this.authService.login(token);
      }
    } else {
      InAppBrowser.openWebView({
        url: `${this.apiUrl}/mobile/login`,
        title: 'تسجيل الدخول',
      });
      InAppBrowser.addListener('urlChangeEvent', (event) => {
        if (event.url.includes(`${this.apiUrl}/gettoken`)) {
          const url = new URL(event.url);
          const token = url.searchParams.get('token');
          if (token) {
            this.authService.login(token);
          }
          InAppBrowser.close();
        }
      });
    }
  }
}
