<ion-header>
  <ion-toolbar>
    <ion-title>{{ 'TABS.MORE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item button detail="true" href="/profile">
      <ion-label>{{ 'PROFILE.TITLE' | translate }}</ion-label>
    </ion-item>

    <ion-item button detail="true" routerLink="/favorites">
      <ion-label>{{ 'MORE.FAVORITES' | translate }}</ion-label>
    </ion-item>

    <ion-item button detail="true" routerLink="/settings">
      <ion-label>{{ 'SETTINGS.TITLE' | translate }}</ion-label>
    </ion-item>

    <ion-item button detail="true" (click)="logout()" *ngIf="isAuthenticated()">
      <ion-label color="danger">{{ 'MORE.LOGOUT' | translate }}</ion-label>
    </ion-item>
    <ion-item button detail="true" (click)="login()" *ngIf="!isAuthenticated()">
      <ion-label color="primary">{{ 'MORE.LOGIN' | translate }}</ion-label>
    </ion-item>
  </ion-list>
</ion-content>
