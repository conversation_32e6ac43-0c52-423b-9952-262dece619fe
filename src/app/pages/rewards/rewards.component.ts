import {Component, computed, effect} from '@angular/core';
import {RewardsService} from "../../services/rewards/rewards.service";
import {
  IonBackButton, IonButton,
  IonButtons,
  IonContent,
  IonHeader, IonItem, IonLabel, IonList, IonNote, IonRefresher, IonRefresherContent, IonRow,
  IonSpinner, IonText,
  IonTitle,
  IonToolbar
} from "@ionic/angular/standalone";
import {TranslatePipe} from "@ngx-translate/core";
import {HugeiconsIconComponent} from "@hugeicons/angular";
import {
  Alert01Icon, Calendar01StrokeRounded,
  CheckmarkCircleIconStrokeRounded, Clock03Icon,
  GiftIcon,
} from "@hugeicons-pro/core-stroke-rounded";
import {NgIf} from "@angular/common";
import {chevronDownCircleOutline} from "ionicons/icons";
import {addIcons} from "ionicons";

@Component({
  selector: 'app-rewards',
  templateUrl: './rewards.component.html',
  styleUrls: ['./rewards.component.scss'],
  standalone: true,
  imports: [
    IonHeader,
    IonToolbar,
    IonBackButton,
    IonButtons,
    TranslatePipe,
    IonTitle,
    IonContent,
    IonSpinner,
    IonText,
    IonButton,
    IonList,
    IonRefresher,
    IonRefresherContent,
    IonItem,
    HugeiconsIconComponent,
    IonNote,
    IonLabel,
    NgIf,
  ]
})
export class RewardsComponent {
  private refreshEvent: any = null;

  constructor(public rewardsService: RewardsService) {
    //add chevron-down-circle-outline icon:
    addIcons({
      chevronDownCircleOutline,
    });

    // Effect to complete refresh when loading state changes
    effect(() => {
      const isLoading = this.rewardsService.isLoading();

      // If we have a pending refresh event and loading just finished
      if (this.refreshEvent && !isLoading) {
        this.refreshEvent.target.complete();
        this.refreshEvent = null;
      }
    });
  }

  // Reactive pull-to-refresh handler
  handleRefresh(event: any) {
    this.refreshEvent = event;
    this.rewardsService.refresh();

    // Fallback timeout in case the effect doesn't trigger
    setTimeout(() => {
      if (this.refreshEvent) {
        this.refreshEvent.target.complete();
        this.refreshEvent = null;
      }
    }, 5000); // 5 second fallback
  }


  protected readonly CheckmarkCircleIconStrokeRounded = CheckmarkCircleIconStrokeRounded;
  protected readonly GiftIcon = GiftIcon;
  protected readonly Alert01Icon = Alert01Icon;
  protected readonly Clock03Icon = Clock03Icon;
  protected readonly Calendar01StrokeRounded = Calendar01StrokeRounded;
}
