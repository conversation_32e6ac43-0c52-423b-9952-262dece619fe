// Loading, Error, and Empty States
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 2rem;
  text-align: center;

  ion-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  ion-spinner {
    margin-bottom: 1rem;
  }

  h3 {
    margin: 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  p {
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.8;
  }

  ion-button {
    margin-top: 0.5rem;
  }
}

// Rewards List Items
ion-list {
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --inner-padding-end: 0px;

    ion-label {
      h2 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
      }

      p {
        font-size: 0.85rem;
        margin: 0;
        font-weight: 500;
      }
    }

    .date-info {
      text-align: right;
      font-size: 0.85rem;
      line-height: 1.3;

      div:first-child {
        font-weight: 500;
      }

      div:last-child {
        opacity: 0.7;
        font-size: 0.8rem;
      }
    }
  }
}
