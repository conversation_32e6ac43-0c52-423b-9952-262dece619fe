<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="{{ 'BACK_BUTTON_TEXT' | translate }}" defaultHref="/academic"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ 'REWARDS.TITLE' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <!-- Pull to Refresh -->
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="{{ 'COMMON.PULL_TO_REFRESH' | translate }}"
      refreshingSpinner="lines"
      refreshingText="{{ 'COMMON.REFRESHING' | translate }}">
    </ion-refresher-content>
  </ion-refresher>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">{{ 'REWARDS.TITLE' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading State -->
  @if (rewardsService.isLoading()) {
    <div class="loading-container">
      <ion-spinner name="lines" color="medium"></ion-spinner>
      <ion-text color="medium">
        <p>{{ 'REWARDS.LOADING' | translate }}</p>
      </ion-text>
    </div>
  } @else if (rewardsService.error()) {
    <div class="error-container">
      <hugeicons-icon [icon]="Alert01Icon" color="var(--ion-color-danger)" size="24" [strokeWidth]="1.5"/>

      <ion-text color="danger">
        <h3>{{ 'REWARDS.ERROR_TITLE' | translate }}</h3>
        <p>{{ 'REWARDS.ERROR_MESSAGE' | translate }}</p>
      </ion-text>
      <ion-button fill="clear" color="primary" (click)="rewardsService.refresh()">
        {{ 'REWARDS.RETRY' | translate }}
      </ion-button>
    </div>
  } @else if (rewardsService.rewards().length === 0) {
    <div class="empty-container">
      <hugeicons-icon [icon]="GiftIcon" size="24" [strokeWidth]="1.5"/>

      <ion-text color="medium">
        <h3>{{ 'REWARDS.EMPTY_TITLE' | translate }}</h3>
        <p>{{ 'REWARDS.EMPTY_MESSAGE' | translate }}</p>
      </ion-text>
      <ion-button fill="clear" color="primary" (click)="rewardsService.refresh()">
        {{ 'REWARDS.REFRESH' | translate }}
      </ion-button>
    </div>
  }@else {
    <ion-list>
      @for (reward of rewardsService.rewards(); track $index) {
        <ion-item>
          <hugeicons-icon slot="start" *ngIf="reward.isEligible" color="var(--ion-color-warning)" [icon]="Calendar01StrokeRounded" size="32" [strokeWidth]="1.5"/>
          <hugeicons-icon slot="start" *ngIf="!reward.isEligible" color="var(--ion-color-success)" [icon]="CheckmarkCircleIconStrokeRounded" size="32" [strokeWidth]="1.5"/>


          <ion-label>
            <h2>
              {{ reward.reward_amount }}
              <span class="saudi_riyal_font">&#xE900;</span>
            </h2>
            <p>{{ reward.isEligible ? ('REWARDS.ELIGIBLE' | translate) : ('REWARDS.DISBURSED' | translate) }}</p>
          </ion-label>

          <ion-note slot="end" color="medium">
            <div class="date-info">
              <div>{{ "MONTHS." + reward.month | translate }}</div>
              <div>{{ reward.year }}</div>
            </div>
          </ion-note>
        </ion-item>
      }
    </ion-list>
  }
</ion-content>
