<div class="action-card" [ngStyle]="{'background': 'var(--ion-background-color-shade)'}" (click)="onCardClick()">
  <div class="card-content">
    <div class="icon-wrapper">
      <div class="icon-circle">
        <hugeicons-icon [icon]="iconObj" [size]="24" color="var(--ion-color-icon-color-contrast)" [strokeWidth]="1.5"></hugeicons-icon>
      </div>
    </div>
    <h4 class="label">{{ label }}</h4>
  </div>
</div>
