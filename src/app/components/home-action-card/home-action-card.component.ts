import { Ng<PERSON>ty<PERSON> } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { HugeiconsIconComponent } from '@hugeicons/angular';
import {
  Alert02Icon,
  BookOpen01Icon,
  Calendar01Icon, FileIcon, Folder01Icon,
  Money04Icon,
  UserIcon,
  UserMultiple02Icon,
} from '@hugeicons-pro/core-stroke-rounded';

const ICONS: Record<string, any> = {
  'alert-circle': Alert02Icon,
  'money-04': Money04Icon,
  'user-multiple-02': UserMultiple02Icon,
  'users-circle': UserIcon,
  'book-open-01': BookOpen01Icon,
  'gift-01': Money04Icon,
  'calendar-outline': Calendar01Icon,
  'file-text': FileIcon,
  'folder-01': Folder01Icon,
};

@Component({
  selector: 'app-home-action-card',
  templateUrl: './home-action-card.component.html',
  styleUrls: ['./home-action-card.component.scss'],
  standalone: true,
  imports: [NgStyle, HugeiconsIconComponent],
})
export class HomeActionCardComponent {
  @Input() icon: string = '';
  @Input() label: string = '';
  @Input() bgColor: string = '#fff';
  @Input() iconBg: string = '#eee';
  @Output() cardClick = new EventEmitter<void>();

  get iconObj() {
    return ICONS[this.icon] || null;
  }

  onCardClick() {
    this.cardClick.emit();
  }
}
