.action-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  height: 120px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(var(--ion-color-light-shade-rgb), 0.6);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

.card-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 16px;
  height: 100%;
}

.icon-wrapper {
  margin-bottom: 12px;
}

.icon-circle {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.label {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--ion-color-step-750);
  text-align: center;
  padding-inline-start: 4px;
  margin: 0;
}

// dark mode:
@media (prefers-color-scheme: dark) {
  .action-card {
    &:hover {
      box-shadow: 0 8px 16px rgba(var(--ion-color-light-shade-rgb), 0.15);
    }
  }
}
