/* Student Card Styles - Mobile First */
.student-card {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);

  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(var(--ion-color-primary-rgb), 0.3);
  color: white;
  padding: 18px;
  position: relative;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* Modern frosted effect */
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  z-index: 1;
}


/* Shimmer effect overlay */
.student-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.08) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  z-index: -1;
  animation: shimmer 2.5s infinite;
  transform: skewX(-20deg);
}

/* Shimmer animation */
@keyframes shimmer {
  0% {
    transform: translateX(-150%) skewX(-20deg);
  }
  100% {
    transform: translateX(150%) skewX(-20deg);
  }
}

/* Modern pattern overlay */
.student-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 10%),
  radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 15%);
  z-index: -1;
}

.student-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(var(--ion-color-primary-rgb), 0.5);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  .university-logo {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(5px);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }

    img {
      width: 80%;
      height: auto;
    }
  }

  .card-title {
    h4 {
      font-size: 0.95rem;
      font-weight: 700;
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 1px;
      background: linear-gradient(90deg, #ffffff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.card-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  /* Ensure proper spacing */
  min-height: 1-0px;
}

.student-info {
  margin-bottom: 5px;
  flex: 1;
}

.student-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 12px;
}

.detail-item {
  .label {
    display: block;
    font-size: 0.7rem;
    opacity: 0.8;
    margin-bottom: 3px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }

  .value {
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-bottom: 2px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: border-color 0.3s ease;

    &:hover {
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

/* Add student name to card */
.student-name {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 6px;
  letter-spacing: 0.6px;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Barcode/QR section - Fixed for mobile */
.qr-code {
  margin-top: 15px;
  width: 100%;
  display: flex;
  align-items: start;
  justify-content: start; /* Center on mobile */
  border-radius: 10px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }

  img {
    height: 32px;
    width: auto;
    max-width: 90%; /* Ensure it doesn't overflow */
    object-fit: contain;
  }
}

/* Responsive Styles - Mobile First */

/* Medium screens (tablets) */
@media (min-width: 480px) {
  .student-card {
    padding: 20px;
    border-radius: 24px;
  }

  .card-header {
    margin-bottom: 15px;

    .university-logo {
      width: 45px;
      height: 45px;
      border-radius: 14px;
    }

    .card-title h4 {
      font-size: 1.05rem;
    }
  }

  .student-name {
    font-size: 1.3rem;
    margin-bottom: 8px;
  }

  .student-details {
    gap: 14px;
  }

  .detail-item {
    .label {
      font-size: 0.75rem;
      margin-bottom: 3px;
    }

    .value {
      font-size: 0.95rem;
    }
  }

  .qr-code {
    padding: 10px;

    img {
      height: 40px;
    }
  }
}

/* Large screens (tablets and small desktops) */
@media (min-width: 768px) {
  .student-card {
    aspect-ratio: 20/10;
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
  }

  .card-body {
    flex-direction: row; /* Switch to horizontal layout on larger screens */
    justify-content: space-between;
    align-items: flex-end;
    min-height: auto;
  }

  .student-info {
    flex: 1;
    margin-bottom: 0;
  }

  .qr-code {
    width: auto;
    margin-top: 0;
    margin-left: 20px;
    border-radius: 12px;
    justify-content: flex-end;
    align-self: flex-end;

    img {
      height: 48px;
      max-width: none;
    }
  }
}

/* Extra large screens (desktops) */
@media (min-width: 992px) {
  .student-card {
    aspect-ratio: 20/10;
    max-width: 800px;
    min-height: 250px;
    padding: 26px;
    border-radius: 28px;
  }

  .student-name {
    font-size: 1.4rem;
    margin-bottom: 10px;
  }

  .detail-item {
    .label {
      font-size: 0.8rem;
    }

    .value {
      font-size: 1.05rem;
    }
  }

  .qr-code {
    padding: 12px;

    img {
      height: 54px;
    }
  }
}

// .student-card in dark mode
@media (prefers-color-scheme: dark) {
  .student-card {
    background: linear-gradient(135deg, var(--ion-color-light-shade) 0%, var(--ion-color-light-shade) 100%);
    box-shadow: 0 10px 30px rgba(var(--ion-color-light-shade-rgb), 0.3);

  }
  .student-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(var(--ion-color-light-shade-rgb), 0.4);
  }
}
