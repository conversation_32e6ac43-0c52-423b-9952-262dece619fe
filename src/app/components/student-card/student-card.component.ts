import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { FirstNamePipe } from '../../pipes/first-name.pipe';

@Component({
  selector: 'app-student-card',
  templateUrl: './student-card.component.html',
  styleUrls: ['./student-card.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, TranslateModule]
})
export class StudentCardComponent {
  @Input() student: {
    name: string;
    universityId: string;
    college: string;
    major: string;
    expiry: string;
  } = {
    name: '',
    universityId: '',
    college: '',
    major: '',
    expiry: ''
  };
}
