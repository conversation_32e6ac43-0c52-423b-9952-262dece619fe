import { Component, inject } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { LanguageService, Language } from '../../services/language/language.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-language-selector',
  templateUrl: './language-selector.component.html',
  styleUrls: ['./language-selector.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, TranslateModule]
})
export class LanguageSelectorComponent {
  private languageService = inject(LanguageService);

  readonly currentLanguage = this.languageService.currentLanguage;
  readonly supportedLanguages = this.languageService.getSupportedLanguages();

  onLanguageChange(event: CustomEvent): void {
    const languageCode = event.detail.value as 'ar' | 'en';
    this.languageService.switchLanguage(languageCode);
  }

  trackByCode(index: number, language: Language): string {
    return language.code;
  }
}