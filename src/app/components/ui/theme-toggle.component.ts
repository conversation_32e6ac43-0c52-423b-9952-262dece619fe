import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { ThemeService, ThemeMode } from '../../services/theme/theme.service';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-theme-toggle',
  template: `
    <ion-item>
      <ion-label>{{ 'SETTINGS.THEME' | translate }}</ion-label>
      <ion-select
        [value]="currentTheme"
        (ionChange)="themeService.setTheme($event.detail.value)"
        interface="action-sheet"
        [interfaceOptions]="{ header: ('SETTINGS.THEME' | translate) }"
      >
        <ion-select-option value="light">{{ 'SETTINGS.THEME_LIGHT' | translate }}</ion-select-option>
        <ion-select-option value="dark">{{ 'SETTINGS.THEME_DARK' | translate }}</ion-select-option>
        <ion-select-option value="system">{{ 'SETTINGS.THEME_SYSTEM' | translate }}</ion-select-option>
      </ion-select>
    </ion-item>
  `,
  standalone: true,
  imports: [IonicModule, CommonModule, TranslateModule]
})
export class ThemeToggleComponent implements OnInit, OnDestroy {
  protected themeService = inject(ThemeService);
  currentTheme: ThemeMode = 'system';
  private themeSubscription: Subscription = new Subscription();

  ngOnInit() {
    // Initialize with the current theme from ThemeService
    this.themeSubscription = this.themeService.currentTheme.subscribe(theme => {
      this.currentTheme = theme;
    });
  }

  ngOnDestroy() {
    // Clean up subscription when component is destroyed
    if (this.themeSubscription) {
      this.themeSubscription.unsubscribe();
    }
  }
}
