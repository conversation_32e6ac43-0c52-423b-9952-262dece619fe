import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-icon',
  standalone: true,
  imports: [CommonModule],
  template: `
    <svg
      [class]="'icon ' + (class || '')"
      [style.width.px]="size"
      [style.height.px]="size"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      [innerHTML]="getIconPath()"
    ></svg>
  `,
  styles: [`
    .icon {
      display: inline-block;
      line-height: 0;
      flex-shrink: 0;
    }
  `]
})
export class IconComponent {
  @Input() name!: string;
  @Input() size: number = 24;
  @Input() class?: string;

  private readonly icons = {
    home: '<path d="M22 10.5V19C22 20.6569 20.6569 22 19 22H5C3.34315 22 2 20.6569 2 19V10.5M12 17V14M2 10.5L10.2929 3.46447C11.4645 2.45015 13.2371 2.45015 14.4087 3.46447L22 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>',
    academic: '<path d="M12 2L3 7L12 12L21 7L12 2Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M19 9V14M5 9V14M3 7V16M12 12V21M12 12L21 7V16L12 21L3 16V7L12 12Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>',
    services: '<path d="M3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5Z" stroke="currentColor" stroke-width="1.5"/><path d="M14 6.5C14 4.567 15.567 3 17.5 3C19.433 3 21 4.567 21 6.5C21 8.433 19.433 10 17.5 10C15.567 10 14 8.433 14 6.5Z" stroke="currentColor" stroke-width="1.5"/><path d="M3 17.5C3 15.567 4.567 14 6.5 14C8.433 14 10 15.567 10 17.5C10 19.433 8.433 21 6.5 21C4.567 21 3 19.433 3 17.5Z" stroke="currentColor" stroke-width="1.5"/><path d="M14 17.5C14 15.567 15.567 14 17.5 14C19.433 14 21 15.567 21 17.5C21 19.433 19.433 21 17.5 21C15.567 21 14 19.433 14 17.5Z" stroke="currentColor" stroke-width="1.5"/>',
    announcements: '<path d="M12 2H8.2C6.06863 2 4.5 3.56863 4.5 5.7V18.3C4.5 20.4314 6.06863 22 8.2 22H15.8C17.9314 22 19.5 20.4314 19.5 18.3V9.5M12 2L19.5 9.5M12 2V8.3C12 8.96274 12.5373 9.5 13.2 9.5H19.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 13H16M8 17H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>',
    more: '<path d="M4 12C4 10.8954 4.89543 10 6 10C7.10457 10 8 10.8954 8 12C8 13.1046 7.10457 14 6 14C4.89543 14 4 13.1046 4 12Z" stroke="currentColor" stroke-width="1.5"/><path d="M10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12Z" stroke="currentColor" stroke-width="1.5"/><path d="M16 12C16 10.8954 16.8954 10 18 10C19.1046 10 20 10.8954 20 12C20 13.1046 19.1046 14 18 14C16.8954 14 16 13.1046 16 12Z" stroke="currentColor" stroke-width="1.5"/>'
  };

  getIconPath(): string {
    return this.icons[this.name as keyof typeof this.icons] || '';
  }
} 