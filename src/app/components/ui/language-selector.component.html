<ion-item>
  <ion-label>{{ 'SETTINGS.LANGUAGE' | translate }}</ion-label>
  <ion-select
    [value]="currentLanguage().code"
    (ionChange)="onLanguageChange($event)"
    interface="action-sheet"
    [interfaceOptions]="{ header: ('SETTINGS.LANGUAGE' | translate) }"
  >
    <ion-select-option *ngFor="let language of supportedLanguages; trackBy: trackByCode" [value]="language.code">
      {{ language.nativeName }}
    </ion-select-option>
  </ion-select>
</ion-item>