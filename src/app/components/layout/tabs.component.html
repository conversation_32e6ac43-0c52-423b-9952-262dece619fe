<ion-tabs (ionTabsDidChange)="tabChanged()" style="">
  <ion-tab-bar slot="bottom" class="ion-justify-content-between" style="height: 60px;">
    <ion-tab-button tab="home">
      <hugeicons-icon [icon]="homeIcon" [size]="24" [color]="isSelected('home') ? 'medium' : 'light'"
        [strokeWidth]="1.5"></hugeicons-icon>
      <ion-label>{{ 'TABS.HOME' | translate }}</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="academic" *ngIf="isAuthenticated()">
      <hugeicons-icon [icon]="academicIcon" [size]="24"
        [color]="isSelected('academic') ? 'medium' : 'light'" [strokeWidth]="1.5"></hugeicons-icon>
      <ion-label style="line-height: 1.2">{{ 'TABS.ACADEMIC' | translate }}</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="services">
      <hugeicons-icon [icon]="servicesIcon" [size]="24"
        [color]="isSelected('services') ? 'medium' : 'light'" [strokeWidth]="1.5"></hugeicons-icon>
      <ion-label>{{ 'TABS.SERVICES' | translate }}</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="announcements">
      <hugeicons-icon [icon]="announcementsIcon" [size]="24"
        [color]="isSelected('announcements') ? 'medium' : 'light'" [strokeWidth]="1.5"></hugeicons-icon>
      <ion-label>{{ 'TABS.ANNOUNCEMENTS' | translate }}</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="more">
      <hugeicons-icon [icon]="moreIcon" [size]="24" [color]="isSelected('more') ? 'medium' : 'light'"
        [strokeWidth]="1.5"></hugeicons-icon>
      <ion-label>{{ 'TABS.MORE' | translate }}</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>
