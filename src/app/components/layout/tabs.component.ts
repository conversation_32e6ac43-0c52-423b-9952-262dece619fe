import { Component, inject, ViewChild } from '@angular/core';
import { IonicModule, IonTabs } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { LanguageService } from '../../services/language/language.service';
import { HugeiconsIconComponent } from '@hugeicons/angular';
import { TranslateModule } from '@ngx-translate/core';
import {
  HomeIcon,
  NewsIcon,
  Layers01Icon,
  Comment01Icon,
  Menu01Icon,
} from '@hugeicons-pro/core-stroke-rounded';
import { AuthService } from '../../services/auth/auth.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-tabs',
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.scss',
  standalone: true,
  imports: [
    IonicModule,
    RouterModule,
    HugeiconsIconComponent,
    TranslateModule,
    CommonModule,
  ],
})
export class TabsComponent {
  @ViewChild(IonTabs) tabs!: IonTabs;

  private languageService = inject(LanguageService);
  private authService = inject(AuthService);

  readonly currentLanguage = this.languageService.currentLanguage;
  readonly isAuthenticated = this.authService.isAuthenticated;

  selectedTab: string = 'home';

  // Icons
  readonly homeIcon = HomeIcon;
  readonly academicIcon = NewsIcon;
  readonly servicesIcon = Layers01Icon;
  readonly announcementsIcon = Comment01Icon;
  readonly moreIcon = Menu01Icon;

  // Method to check if a tab is selected
  isSelected(tab: string): boolean {
    return this.selectedTab === tab;
  }

  // Method to handle tab changes
  tabChanged() {
    this.selectedTab = this.tabs.getSelected() || 'home';
  }
}
