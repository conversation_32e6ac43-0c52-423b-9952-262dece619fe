import { Routes } from '@angular/router';
import { TabsComponent } from './tabs.component';
import { authGuard } from '../../guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    component: TabsComponent,
    children: [
      {
        path: 'home',
        loadComponent: () =>
          import('../../pages/home/<USER>').then(
            (m) => m.HomeComponent
          ),
      },
      {
        path: 'academic',
        canActivate: [authGuard],
        children: [
          {
            path: '',
            loadComponent: () =>
              import('../../pages/academic/academic.component').then(
                (m) => m.AcademicComponent
              ),
          },
          {
            path: 'advisor',
            loadComponent: () =>
              import(
                '../../pages/academic-advisor/academic-advisor.component'
              ).then((m) => m.AcademicAdvisorComponent),
          },
          {
            path: 'study-plan',
            loadComponent: () =>
              import('../../pages/study-plan/study-plan.component').then(
                (m) => m.StudyPlanComponent
              ),
          },
          {
            path: 'absences',
            loadComponent: () =>
              import('../../pages/absences/absences.component').then(
                (m) => m.AbsencesComponent
              ),
          },
          {
            path: 'rewards',
            loadComponent: () =>
              import('../../pages/rewards/rewards.component').then(
                (m) => m.RewardsComponent
              ),
          },
          {
            path: 'schedule',
            loadComponent: () =>
              import('../../pages/student-schedule/student-schedule.page').then(
                (m) => m.StudentSchedulePage
              ),
          },
          {
            path: 'academic-transactions',
            loadComponent: () =>
              import(
                '../../pages/academic-transactions/academic-transactions.page'
              ).then((m) => m.AcademicTransactionsPage),
          },
        ],
      },
      {
        path: 'services',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('../../pages/services/services.component').then(
                (m) => m.ServicesComponent
              ),
          },
          {
            path: 'dms',
            children: [
              {
                path: '',
                loadComponent: () =>
                  import('../../pages/dms/dms.component').then(
                    (m) => m.DmsComponent
                  ),
              },
              {
                path: ':docNumber',
                loadComponent: () =>
                  import(
                    '../../pages/dms/transaction-details/transaction-details.component'
                  ).then((m) => m.TransactionDetailsComponent),
              },
            ],
          },
        ],
      },
      {
        path: 'announcements',
        loadComponent: () =>
          import('../../pages/announcements/announcements.component').then(
            (m) => m.AnnouncementsComponent
          ),
      },
      {
        path: 'more',
        loadComponent: () =>
          import('../../pages/more/more.component').then(
            (m) => m.MoreComponent
          ),
      },
      {
        path: 'settings',
        loadComponent: () =>
          import('../../pages/settings/settings.component').then(
            (m) => m.SettingsComponent
          ),
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('../../pages/profile/profile.component').then(
            (m) => m.ProfileComponent
          ),
      },
      {
        path: 'favorites',
        loadComponent: () =>
          import('../../pages/favorites/favorites.component').then(
            (m) => m.FavoritesComponent
          ),
      },
      {
        path: '',
        redirectTo: '/home',
        pathMatch: 'full',
      },
    ],
  },
];
