/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
//@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import "@emran-alhaddad/saudi-riyal-font/index.css";

// Import Splide CSS
@import '@splidejs/splide/css';

@import '@ionic/angular/css/palettes/dark.class.css';

ion-label.sc-ion-label-ios-h.sc-ion-label-ios-s.ios.label-rtl {
  line-height: 1.4 !important;
}

.saudi_riyal_font {
  font-family: 'saudi_riyal', var(--ion-font-family), system-ui;
  line-height: 2 !important;
}

hugeicons-icon svg{
  vertical-align: middle;
}

// Global modal styles
.search-form-modal {
  --backdrop-opacity: 0.4;
  --border-radius: 28px 28px 0 0;
  --box-shadow: 0 -10px 50px rgba(0, 0, 0, 0.25);

  &::part(content) {
    border-radius: 28px 28px 0 0;
    box-shadow: 0 -10px 50px rgba(0, 0, 0, 0.25);
  }
}

* {
  line-height: 1.5; /* Adjust the value as needed */
}
ion-label{
  font-family: var(--ion-font-family), system-ui;
}
