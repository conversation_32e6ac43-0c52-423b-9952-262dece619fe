// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/
:root {
  /* Set IBM Plex Sans Arabic as the default font */
  --ion-font-family: 'IBM Plex Sans Arabic', sans-serif;
  --ion-text-color: #000000;

  --ion-color-primary: #1C8354;
  --ion-color-primary-rgb: 28, 131, 84;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #19734a;
  --ion-color-primary-tint: #338f65;

  --ion-color-secondary: #F5BD02;
  --ion-color-secondary-rgb: 245, 189, 2;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0, 0, 0;
  --ion-color-secondary-shade: #d8a602;
  --ion-color-secondary-tint: #f6c41b;

  --ion-color-tertiary: #6D428F;
  --ion-color-tertiary-rgb: 109, 66, 143;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #603a7e;
  --ion-color-tertiary-tint: #7c559a;

  --ion-color-success: #2dd55b;
  --ion-color-success-rgb: 45, 213, 91;
  --ion-color-success-contrast: #000000;
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #28bb50;
  --ion-color-success-tint: #42d96b;

  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #c5000f;
  --ion-color-danger-rgb: 197, 0, 15;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #ad000d;
  --ion-color-danger-tint: #cb1a27;

  --ion-color-light: #f6f8fc;
  --ion-color-light-rgb: 246, 248, 252;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d8dade;
  --ion-color-light-shade-rgb: 216, 218, 222;
  --ion-color-light-tint: #f7f9fc;

  --ion-color-medium: #5f5f5f;
  --ion-color-medium-rgb: 95, 95, 95;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #545454;
  --ion-color-medium-tint: #6f6f6f;

  --ion-color-dark: #2f2f2f;
  --ion-color-dark-rgb: 47, 47, 47;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #292929;
  --ion-color-dark-tint: #444444;


  --ion-color-icon-color: #fff;
  --ion-color-icon-color-rgb: 28, 131, 84;
  --ion-color-icon-color-contrast: #1C8354;
  --ion-color-icon-color-contrast-rgb: 255, 255, 255;
  --ion-color-icon-color-shade: #19734a;
  --ion-color-icon-color-tint: #338f65;





  @media (prefers-color-scheme: dark) {

    ///** primary **/
    --ion-color-primary: #1C8354;
    --ion-color-primary-rgb: 28, 106, 77;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 255, 255, 255;
    --ion-color-primary-shade: #195d44;
    --ion-color-primary-tint: #33795f;

    /*
     * icon-color
     * -------------------------------------------
     */
    --ion-color-icon-color: #404143;
    --ion-color-icon-color-rgb: 29, 29, 29;
    --ion-color-icon-color-contrast: #6c7b74;
    --ion-color-icon-color-contrast-rgb: 255, 255, 255;
    --ion-color-icon-color-shade: #1a1a1a;
    --ion-color-icon-color-tint: #343434;

    /*
     * iOS Dark Theme
     * -------------------------------------------
     */

    --ion-background-color: #121212;
    --ion-background-color-shade: 
    --ion-color-base: #121212;

    --ion-background-color-rgb: 30, 36, 44;

    --ion-text-color: #d0d0d0;
    --ion-text-color-rgb: 208, 208, 208;
    --ion-color-step-50: #0d0d0d;
    --ion-color-step-100: #1a1a1a;
    --ion-color-step-150: #262626;
    --ion-color-step-200: #333333;
    --ion-color-step-250: #404040;
    --ion-color-step-300: #4d4d4d;
    --ion-color-step-350: #595959;
    --ion-color-step-400: #666666;
    --ion-color-step-450: #737373;
    --ion-color-step-500: #808080;
    --ion-color-step-550: #8c8c8c;
    --ion-color-step-600: #999999;
    --ion-color-step-650: #a6a6a6;
    --ion-color-step-700: #b3b3b3;
    --ion-color-step-750: #bfbfbf;
    --ion-color-step-800: #cccccc;
    --ion-color-step-850: #d9d9d9;
    --ion-color-step-900: #e6e6e6;
    --ion-color-step-950: #f2f2f2;


    /*
     * Dark Theme
     * -------------------------------------------
     */
    --ion-item-background: #1e1e1e;
    --ion-toolbar-background: #1f1f1f;
    --ion-tab-bar-background: #1f1f1f;
    --ion-card-background: #1e1e1e;

    --ion-color-light: #1e1e1e;
    --ion-color-light-rgb: 30, 30, 30;
    --ion-color-light-contrast: #ffffff;
    --ion-color-light-contrast-rgb: 255, 255, 255;
    --ion-color-light-shade: #3a3939;
    --ion-color-light-shade-rgb: 58, 57, 57;
    --ion-color-light-tint: #262626;



  }

  ion-modal {
    --ion-background-color: var(--ion-color-step-100);
    --ion-toolbar-background: var(--ion-color-step-150);
    --ion-toolbar-border-color: var(--ion-color-step-250);
  }

}


.ion-color-icon-color {
  --ion-color-base: var(--ion-color-icon-color);
  --ion-color-base-rgb: var(--ion-color-icon-color-rgb);
  --ion-color-contrast: var(--ion-color-icon-color-contrast);
  --ion-color-contrast-rgb: var(--ion-color-icon-color-contrast-rgb);
  --ion-color-shade: var(--ion-color-icon-color-shade);
  --ion-color-tint: var(--ion-color-icon-color-tint);
}
