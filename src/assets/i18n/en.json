{"ABSENCES.ALL": "All", "ABSENCES.COURSES_LIST": "Courses List", "ABSENCES.DATE": "Date", "ABSENCES.DAY": "Day", "ABSENCES.DETAILS_LIST": "Absences", "ABSENCES.DETAILS_TITLE": "Absence Details", "ABSENCES.ERROR_TITLE": "Error Loading Absences", "ABSENCES.EXCUSED": "Excused", "ABSENCES.LATE": "Late", "ABSENCES.LOADING": "Loading Absences...", "ABSENCES.NET_PERCENTAGE_SHORT": "Absence Rate", "ABSENCES.NO_ABSENCES": "No Absences", "ABSENCES.NO_ABSENCES_MESSAGE": "No absences recorded", "ABSENCES.NO_ABSENCES_TITLE": "No Absences", "ABSENCES.NO_DETAILS_FOR_COURSE": "No absence details recorded for this course.", "ABSENCES.NO_DETAILS_TITLE": "Details", "ABSENCES.STATUS": "Status", "ABSENCES.TITLE": "Absences", "ABSENCES.TOTAL": "Total", "ABSENCES.TRY_AGAIN": "Try Again", "ABSENCES.UNEXCUSED": "Unexcused", "ABSENCES.UNEXCUSED_SHORT": "Unexcused", "ACADEMIC.ADDITIONAL_INFO": "Additional Information", "ACADEMIC.CALCULATED_POINTS": "Calculated Points (Hours × Quality Points)", "ACADEMIC.COURSES": "My Courses", "ACADEMIC.COURSE_CODE": "Course Code", "ACADEMIC.COURSE_DESCRIPTION": "Course Description", "ACADEMIC.COURSE_DETAILS": "Course Details", "ACADEMIC.COURSE_INFO": "Course Information", "ACADEMIC.COURSE_NAME": "Course Name", "ACADEMIC.GRADES": "Grades", "ACADEMIC.HOURS": "Credit Hours", "ACADEMIC.LETTER_GRADE": "Grade", "ACADEMIC.LETTER_GRADE_DESC": "Final course grade", "ACADEMIC.NO_COURSE_DATA": "No course data available", "ACADEMIC.QUALITY_POINTS": "Quality Points", "ACADEMIC.SCHEDULE": "Class Schedule", "ACADEMIC.SEMESTER": "<PERSON><PERSON><PERSON>", "ACADEMIC.SEMESTER_DESC": "Course study semester", "ACADEMIC.STUDY_PLAN": "Study Plan", "ACADEMIC.TITLE": "Academic", "ACADEMIC.TOTAL_POINTS": "Total Points", "ACADEMIC.TRANSACTIONS": "Academic Record", "ACADEMIC.VIEW_SYLLABUS": "View Syllabus", "ADVISOR.ACADEMIC_ADVISOR": "Academic Advisor", "ADVISOR.DESCRIPTION": "Academic advisors provide educational guidance to students. The primary responsibility of an academic advisor is to evaluate the student's academic plan to ensure it meets university requirements and is suitable for the student's specific needs.", "ADVISOR.EMAIL": "Email", "ADVISOR.FAQ": "Frequently Asked Questions", "ADVISOR.FAQ_ANSWER_1": "You can send an email to your advisor using the contact information above or schedule an appointment during office hours.", "ADVISOR.FAQ_ANSWER_2": "Your academic advisor can help you with course selection, degree planning, academic progress tracking, graduation requirements, and navigating university policies.", "ADVISOR.FAQ_ANSWER_3": "It is recommended to meet with your academic advisor at least once per semester for course planning and reviewing your progress toward your degree.", "ADVISOR.FAQ_ANSWER_4": "Before meeting your advisor, prepare questions about your academic progress, review your study plan, and think about your academic and career goals.", "ADVISOR.FAQ_QUESTION_1": "How can I contact my academic advisor?", "ADVISOR.FAQ_QUESTION_2": "How can my academic advisor help me?", "ADVISOR.FAQ_QUESTION_3": "How often should I meet with my academic advisor?", "ADVISOR.FAQ_QUESTION_4": "What should I prepare before meeting with my academic advisor?", "ADVISOR.LOADING": "Loading academic advisor data...", "ADVISOR.NAME": "Your Academic Advisor", "ADVISOR.NO_ADVISOR_MESSAGE": "You don't currently have an assigned academic advisor. Please contact your academic department for assistance.", "ADVISOR.NO_ADVISOR_TITLE": "No Academic Advisor Assigned", "ADVISOR.OFFICE_HOURS": "Office Hours", "ADVISOR.OFFICE_LOCATION": "Office Location", "ADVISOR.SCHEDULE_MEETING": "Schedule Meeting", "ADVISOR.SEND_EMAIL": "Send Email", "ADVISOR.TITLE": "Academic Advisor", "ADVISOR.YOUR_ADVISOR": "Your Academic Advisor", "ANNOUNCEMENTS.ALL": "All Announcements", "ANNOUNCEMENTS.IMPORTANT": "Important", "ANNOUNCEMENTS.NO_ANNOUNCEMENTS": "No Announcements", "ANNOUNCEMENTS.NO_ANNOUNCEMENTS_DESCRIPTION": "No announcements available", "ANNOUNCEMENTS.TITLE": "Announcements", "APP.NAME": "University App", "APP.WELCOME": "Welcome to University App", "BACK_BUTTON_TEXT": "Back", "COMMON.CANCEL": "Cancel", "COMMON.CLOSE": "Close", "COMMON.PULL_TO_REFRESH": "Pull to refresh", "COMMON.REFRESHING": "Refreshing...", "DAYS.1": "Sunday", "DAYS.2": "Monday", "DAYS.3": "Tuesday", "DAYS.4": "Wednesday", "DAYS.5": "Thursday", "DAYS.6": "Friday", "DAYS.7": "Saturday", "DMS.DESCRIPTION": "Welcome to the Document Management System. Here you can manage your documents.", "DMS.DOCUMENT_LIST": "Document List", "DMS.TITLE": "Document Management System", "FAVORITES.EMPTY": "No favorites yet", "FAVORITES.EMPTY_MESSAGE": "Items you save will appear here", "FAVORITES.SAVED": "Saved Items", "HOME.COLLEGE": "College", "HOME.EXPIRY": "Expiry Date", "HOME.GREETING": "Welcome back,", "HOME.ID": "University ID", "HOME.MAJOR": "Major", "HOME.QUICK_ACTIONS": "Quick Actions", "HOME.STUDENT_ID": "Student ID Card", "HOME.TITLE": "Home", "INJAZ.ABOUT_INJAZ": "About Injaz System", "INJAZ.ASSIGNED_DEPARTMENTS": "Assigned Departments", "INJAZ.ASSIGNED_TO": "Assigned To", "INJAZ.BACK_TO_LIST": "Back to List", "INJAZ.CLEAR_HISTORY": "Clear History", "INJAZ.COMPLETION_DATE": "Transaction Completion Date", "INJAZ.DOCNO": "Document Number", "INJAZ.EMPTY_MESSAGE": "You haven't performed any previous searches. You can start by searching for a transaction.", "INJAZ.EMPTY_TITLE": "No Previous Transactions", "INJAZ.ERROR_TITLE": "An Error Occurred", "INJAZ.HISTORY_CLEARED": "Search history cleared", "INJAZ.INVALID_RESPONSE": "Invalid response from server. Please try again.", "INJAZ.INVALID_TRANSACTION": "Invalid transaction", "INJAZ.LOADING": "Loading...", "INJAZ.MAILBOX": "Mailbox", "INJAZ.MAILBOX_REQUIRED": "Transaction mailbox is required", "INJAZ.NETWORK_ERROR": "Network connection error. Please check your internet connection and try again.", "INJAZ.NEW_SEARCH": "New Search", "INJAZ.NOT_FOUND": "Transaction not found. Please check the entered data.", "INJAZ.NO_RESULTS_MESSAGE": "No transaction found with the entered data. Please check the data and try again.", "INJAZ.NO_RESULTS_TITLE": "No Results", "INJAZ.RECENT_SEARCHES": "Recent Searches", "INJAZ.RECENT_TRANSACTIONS": "Recent Transactions", "INJAZ.RESULT_TITLE": "Query Result", "INJAZ.SEARCH": "Query", "INJAZ.SEARCH_DESCRIPTION": "Please enter transaction details to query its status", "INJAZ.SEARCH_TITLE": "Transaction Query", "INJAZ.SELECT_MAILBOX": "Transaction Mailbox", "INJAZ.SELECT_YEAR": "Hijri Year", "INJAZ.SERVER_ERROR": "Server error occurred. Please try again later.", "INJAZ.SOURCE": "Source Department", "INJAZ.START_SEARCH": "Start Search", "INJAZ.STATUS": "Status", "INJAZ.STATUS_COMPLETED": "Completed", "INJAZ.STATUS_IN_PROGRESS": "In Progress", "INJAZ.STATUS_PENDING": "Pending", "INJAZ.STATUS_UNKNOWN": "Unknown", "INJAZ.STATUS_UPDATED": "Transaction status updated successfully", "INJAZ.SYSTEM_DESCRIPTION": "Injaz system is an electronic transaction management system that allows you to track and query the status of your transactions. You can search for a transaction using the transaction number, Hijri year, and mailbox.", "INJAZ.TITLE": "Injaz", "INJAZ.TRANSACTION_DATE": "Transaction Date", "INJAZ.TRANSACTION_DETAILS": "Transaction Details", "INJAZ.TRANSACTION_HISTORY": "Transaction History", "INJAZ.TRANSACTION_NOT_FOUND": "Transaction not found", "INJAZ.TRANSACTION_NUMBER": "Transaction Number", "INJAZ.TRANSACTION_NUMBER_REQUIRED": "Transaction number is required", "INJAZ.TRANSACTION_REMOVED": "Transaction removed", "INJAZ.TRY_AGAIN": "Try Again", "INJAZ.UNKNOWN_ERROR": "An unknown error occurred. Please try again.", "INJAZ.YEAR_REQUIRED": "Hijri year is required", "LOADING": "Loading data...", "MONTHS.أبريل": "April", "MONTHS.أغسطس": "August", "MONTHS.أكتوبر": "October", "MONTHS.ديسمبر": "December", "MONTHS.سبتمبر": "September", "MONTHS.فبراير": "February", "MONTHS.مارس": "March", "MONTHS.مايو": "May", "MONTHS.نوفمبر": "November", "MONTHS.يناير": "January", "MONTHS.يوليو": "July", "MONTHS.يونيو": "June", "MORE.FAVORITES": "Favorites", "MORE.LOGIN": "<PERSON><PERSON>", "MORE.LOGOUT": "Logout", "PROFILE.ACADEMIC_INFO": "Academic Information", "PROFILE.ACADEMIC_STATUS": "Academic Status", "PROFILE.COMPLETED_CREDIT_HOURS": "Completed out of total required hours", "PROFILE.CUMULATIVE_GPA": "Cumulative GPA", "PROFILE.CURRENT_SEMESTER_GPA": "Current semester grade point average", "PROFILE.DATE_OF_BIRTH": "Date of Birth", "PROFILE.EARNED_HOURS": "Credit Hours Progress", "PROFILE.EDIT": "Edit Profile", "PROFILE.EMAIL": "Email Address", "PROFILE.EXPECTED_GRADUATION": "Expected Graduation", "PROFILE.FACULTY": "Faculty", "PROFILE.FULL_NAME": "Full Name", "PROFILE.FULL_NAME_EN": "Full Name (English)", "PROFILE.GENDER": "Gender", "PROFILE.LOGOUT": "Logout", "PROFILE.MAJOR": "Major", "PROFILE.NATIONAL_ID": "National ID", "PROFILE.PERSONAL_INFO": "Personal Information", "PROFILE.PHONE_NUMBER": "Phone Number", "PROFILE.SEMESTER_GPA": "Semester GPA", "PROFILE.STUDENT_ID": "Student ID", "PROFILE.TITLE": "Profile", "PROFILE.USER_NAME": "Username", "REWARDS.DISBURSED": "Disbursed", "REWARDS.ELIGIBLE": "Eligible", "REWARDS.ELIGIBLE_COUNT": "Eligible Rewards", "REWARDS.EMPTY_MESSAGE": "You don't have any rewards at the moment. Check back later!", "REWARDS.EMPTY_TITLE": "No Rewards Available", "REWARDS.ERROR_MESSAGE": "Failed to load rewards. Please check your connection and try again.", "REWARDS.ERROR_TITLE": "Error Loading Rewards", "REWARDS.LOADING": "Loading rewards...", "REWARDS.REFRESH": "Refresh", "REWARDS.RETRY": "Retry", "REWARDS.TITLE": "Rewards", "REWARDS.TOTAL_ELIGIBLE": "Total Eligible", "SCHEDULE.TITLE": "Class Schedule", "SERVICES.SEARCH": "Search Services", "SERVICES.TITLE": "Services", "SETTINGS.ABOUT": "About App", "SETTINGS.LANGUAGE": "Language", "SETTINGS.PREFERENCES": "Preferences", "SETTINGS.PRIVACY": "Privacy Policy", "SETTINGS.THEME": "Theme", "SETTINGS.THEME_DARK": "Dark", "SETTINGS.THEME_LIGHT": "Light", "SETTINGS.THEME_SYSTEM": "System", "SETTINGS.TITLE": "Settings", "STUDY_PLAN.CODE": "Code", "STUDY_PLAN.DEFAULT_DESCRIPTION": "Description", "STUDY_PLAN.DESCRIPTION": "Course Description", "STUDY_PLAN.HOURS": "Hours", "STUDY_PLAN.LEARNING_OUTCOMES": "Learning Outcomes", "STUDY_PLAN.LETTER_GRADE": "Grade", "STUDY_PLAN.LOADING": "Loading data...", "STUDY_PLAN.NO_COURSE_DATA": "No course data available", "STUDY_PLAN.NO_DATA": "No data available", "STUDY_PLAN.NO_PREREQUISITES": "No prerequisites", "STUDY_PLAN.PREREQUISITES": "Prerequisites", "STUDY_PLAN.SCHEDULE": "Section", "STUDY_PLAN.STATUS": "Status", "STUDY_PLAN.STATUS_PASSED": "Passed", "STUDY_PLAN.STATUS_REMAINING": "Remaining", "STUDY_PLAN.STATUS_STUDENT_SCHEDULE": "In Class Schedule", "STUDY_PLAN.SYLLABUS": "Syllabus", "STUDY_PLAN.TITLE": "Study Plan", "TABS.ACADEMIC": "Academic", "TABS.ANNOUNCEMENTS": "Announcements", "TABS.HOME": "Home", "TABS.MORE": "More", "TABS.SERVICES": "Services", "HOME.ANNOUNCEMENTS_TITLE": "General and Important Announcements", "HOME.NEWS_TITLE": "Latest News", "HOME.ACHIEVEMENTS_TITLE": "Achievements", "HOME.CALENDAR_TITLE": "Academic Calendar", "HOME.UNIVERSITY_INFO_TITLE": "University Information", "HOME.LOGIN_CTA_TITLE": "University Members", "HOME.LOGIN_CTA_SUBTITLE": "Login to access personalized features", "HOME.LOGIN_BUTTON": "<PERSON><PERSON>", "HOME.READ_MORE": "Read More", "HOME.VIEW_ALL": "View All", "HOME.IMPORTANT": "Important", "HOME.RECENT": "Recent", "HOME.UPCOMING": "Upcoming", "HOME.VISION": "Vision", "HOME.MISSION": "Mission", "HOME.ABOUT_UNIVERSITY": "About University", "HOME.COLLEGES": "Colleges", "HOME.STUDENTS_COUNT": "Students Count", "HOME.PROGRAMS_COUNT": "Programs Count", "HOME.ESTABLISHED": "Established", "HOME.DEAN": "<PERSON>", "HOME.READ_TIME": "min read", "HOME.EVENT_TYPE.EXAM": "Exam", "HOME.EVENT_TYPE.REGISTRATION": "Registration", "HOME.EVENT_TYPE.HOLIDAY": "Holiday", "HOME.EVENT_TYPE.EVENT": "Event", "HOME.EVENT_TYPE.DEADLINE": "Deadline", "HOME.ACHIEVEMENT_TYPE.ACADEMIC": "Academic", "HOME.ACHIEVEMENT_TYPE.RESEARCH": "Research", "HOME.ACHIEVEMENT_TYPE.SPORTS": "Sports", "HOME.ACHIEVEMENT_TYPE.CULTURAL": "Cultural", "HOME.ACHIEVEMENT_TYPE.COMMUNITY": "Community", "HOME.LOADING": "Loading...", "HOME.ERROR": "Error loading data", "HOME.RETRY": "Retry", "HOME.NO_DATA": "No data available", "academic.courseInfo": "Course Information", "common.cancel": "Cancel", "common.coming_soon": "Coming Soon", "common.ok": "OK", "common.select": "Select an option", "injaz.docno": "Transaction Number", "common.Male": "Male", "common.Female": "Female", "common.N/A": "N/A"}