Here's a detailed feature description for implementing the Absences page in an Ionic/Angular mobile application:

# Absences Page Feature Description

## Overview
The Absences page provides students with a comprehensive view of their attendance record, organized by courses, with detailed information about each absence and proactive warnings about attendance thresholds.

## UI Components

### Header Section
- Page title "Absences"
- Total absences counter badge
- Filter button (optional) to filter by date range/semester

### Warnings Section (Priority Display)
- <PERSON>ert card at the top showing critical warnings
- Visual indicator (color-coded):
  - Red: Courses where absences > 15% of total classes
  - Yellow: Courses where absences > 10% of total classes
  - Green: Courses with safe absence levels
- Progress bar for each critical course showing:
  - Current absence percentage
  - Maximum allowed threshold (20%)
  - Remaining absences before reaching limit

### Course-Grouped Absences List
- Expandable/collapsible course sections
- Each course section header displays:
  - Course name
  - Course code
  - Total absences count
  - Percentage of absences
  - Small circular progress indicator

### Individual Absence Items
- Date of absence
- Class period/time
- Status indicator (justified/unjustified)
- Tap interaction to view details

### Absence Details Modal (Ion Sheet)
- Full absence details including:
  - Date and time
  - Course information
  - Professor name
  - Class topic/content (if available)
  - Absence status (justified/unjustified)
  - Justification documentation (if provided)
  - Impact on overall attendance
  - Close button

## Functionality

### Data Organization
- Group absences by course
- Sort courses alphabetically
- Sort absences within each course by date (newest first)

### Calculations
- Compute absence percentage per course
- Track remaining allowed absences
- Calculate warning thresholds:
  - Critical: > 15% absences
  - Warning: > 10% absences
  - Safe: < 10% absences

### Warning System
- Real-time calculation of absence percentages
- Proactive notifications when approaching limits
- Clear visualization of attendance status

### Interactions
- Tap to expand/collapse course sections
- Tap absence for detailed view
- Pull-to-refresh for data updates
- Smooth animations for transitions

## Technical Specifications

### Data Structure
```typescript
interface Absence {
  id: string;
  courseId: string;
  date: Date;
  period: string;
  status: 'justified' | 'unjustified';
  justification?: string;
  topic?: string;
  professorName: string;
}

interface Course {
  id: string;
  name: string;
  code: string;
  totalClasses: number;
  maxAbsences: number; // 20% of totalClasses
  absences: Absence[];
}
```

### API Integration
- GET /api/student/absences (fetch all absences)
- GET /api/courses (fetch course details)
- GET /api/absence/{id} (fetch specific absence details)

### Components Required
- AbsencesPage (main container)
- WarningSection
- CourseAbsenceList
- AbsenceItem
- AbsenceDetailModal
- ProgressIndicator
- AlertBadge

## Design Guidelines

### Color Scheme
- Error/Critical: #dc2626 (red-600)
- Warning: #fbbf24 (amber-400)
- Safe: #22c55e (green-500)
- Background: #f8fafc (slate-50)
- Text: #1e293b (slate-800)

### Typography
- Course titles: 16px, Semi-bold
- Dates: 14px, Regular
- Warning text: 14px, Medium
- Details text: 14px, Regular

### Spacing
- Standard padding: 16px
- Section margins: 12px
- Item padding: 12px

## Error Handling
- Network error state with retry option
- Empty state design when no absences
- Loading states with skeletons
- Error messages for failed operations

## Animations
- Smooth expand/collapse for course sections
- Modal slide-up animation
- Progress bar animations
- Warning alert fade-in

## Performance Considerations
- Lazy loading of absence details
- Pagination for large absence lists
- Caching of frequently accessed data
- Optimistic UI updates

## Accessibility
- Clear contrast ratios
- Semantic HTML structure
- Screen reader support
- Touch targets >= 44px

This feature description provides a comprehensive guide for implementing the Absences page, ensuring both functionality and user experience are optimized for mobile use within an Ionic/Angular application.
