# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:2.3.0, @ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@angular-devkit/architect@npm:0.1902.7, @angular-devkit/architect@npm:>= 0.1900.0 < 0.2000.0":
  version: 0.1902.7
  resolution: "@angular-devkit/architect@npm:0.1902.7"
  dependencies:
    "@angular-devkit/core": "npm:19.2.7"
    rxjs: "npm:7.8.1"
  checksum: 10c0/04bbd1057de8b22798f7c43c0cd7051eef2f9102655417d2aba57888ddd9f4c0a8327df2480cd2146e18d99861d9f92d467038ae799ed900923a7a8623312767
  languageName: node
  linkType: hard

"@angular-devkit/build-angular@npm:^19.0.0":
  version: 19.2.7
  resolution: "@angular-devkit/build-angular@npm:19.2.7"
  dependencies:
    "@ampproject/remapping": "npm:2.3.0"
    "@angular-devkit/architect": "npm:0.1902.7"
    "@angular-devkit/build-webpack": "npm:0.1902.7"
    "@angular-devkit/core": "npm:19.2.7"
    "@angular/build": "npm:19.2.7"
    "@babel/core": "npm:7.26.10"
    "@babel/generator": "npm:7.26.10"
    "@babel/helper-annotate-as-pure": "npm:7.25.9"
    "@babel/helper-split-export-declaration": "npm:7.24.7"
    "@babel/plugin-transform-async-generator-functions": "npm:7.26.8"
    "@babel/plugin-transform-async-to-generator": "npm:7.25.9"
    "@babel/plugin-transform-runtime": "npm:7.26.10"
    "@babel/preset-env": "npm:7.26.9"
    "@babel/runtime": "npm:7.26.10"
    "@discoveryjs/json-ext": "npm:0.6.3"
    "@ngtools/webpack": "npm:19.2.7"
    "@vitejs/plugin-basic-ssl": "npm:1.2.0"
    ansi-colors: "npm:4.1.3"
    autoprefixer: "npm:10.4.20"
    babel-loader: "npm:9.2.1"
    browserslist: "npm:^4.21.5"
    copy-webpack-plugin: "npm:12.0.2"
    css-loader: "npm:7.1.2"
    esbuild: "npm:0.25.1"
    esbuild-wasm: "npm:0.25.1"
    fast-glob: "npm:3.3.3"
    http-proxy-middleware: "npm:3.0.3"
    istanbul-lib-instrument: "npm:6.0.3"
    jsonc-parser: "npm:3.3.1"
    karma-source-map-support: "npm:1.4.0"
    less: "npm:4.2.2"
    less-loader: "npm:12.2.0"
    license-webpack-plugin: "npm:4.0.2"
    loader-utils: "npm:3.3.1"
    mini-css-extract-plugin: "npm:2.9.2"
    open: "npm:10.1.0"
    ora: "npm:5.4.1"
    picomatch: "npm:4.0.2"
    piscina: "npm:4.8.0"
    postcss: "npm:8.5.2"
    postcss-loader: "npm:8.1.1"
    resolve-url-loader: "npm:5.0.0"
    rxjs: "npm:7.8.1"
    sass: "npm:1.85.0"
    sass-loader: "npm:16.0.5"
    semver: "npm:7.7.1"
    source-map-loader: "npm:5.0.0"
    source-map-support: "npm:0.5.21"
    terser: "npm:5.39.0"
    tree-kill: "npm:1.2.2"
    tslib: "npm:2.8.1"
    webpack: "npm:5.98.0"
    webpack-dev-middleware: "npm:7.4.2"
    webpack-dev-server: "npm:5.2.0"
    webpack-merge: "npm:6.0.1"
    webpack-subresource-integrity: "npm:5.1.0"
  peerDependencies:
    "@angular/compiler-cli": ^19.0.0 || ^19.2.0-next.0
    "@angular/localize": ^19.0.0 || ^19.2.0-next.0
    "@angular/platform-server": ^19.0.0 || ^19.2.0-next.0
    "@angular/service-worker": ^19.0.0 || ^19.2.0-next.0
    "@angular/ssr": ^19.2.7
    "@web/test-runner": ^0.20.0
    browser-sync: ^3.0.2
    jest: ^29.5.0
    jest-environment-jsdom: ^29.5.0
    karma: ^6.3.0
    ng-packagr: ^19.0.0 || ^19.2.0-next.0
    protractor: ^7.0.0
    tailwindcss: ^2.0.0 || ^3.0.0 || ^4.0.0
    typescript: ">=5.5 <5.9"
  dependenciesMeta:
    esbuild:
      optional: true
  peerDependenciesMeta:
    "@angular/localize":
      optional: true
    "@angular/platform-server":
      optional: true
    "@angular/service-worker":
      optional: true
    "@angular/ssr":
      optional: true
    "@web/test-runner":
      optional: true
    browser-sync:
      optional: true
    jest:
      optional: true
    jest-environment-jsdom:
      optional: true
    karma:
      optional: true
    ng-packagr:
      optional: true
    protractor:
      optional: true
    tailwindcss:
      optional: true
  checksum: 10c0/fbf12818754a4e3dbe7e9947d1953f89d60775520bfe4d217e98e0c5f7144ea4a05615fc8dfc832596094d2fcf2af1535013d44bf4ff87ccf2f1e8ff66d68aab
  languageName: node
  linkType: hard

"@angular-devkit/build-webpack@npm:0.1902.7":
  version: 0.1902.7
  resolution: "@angular-devkit/build-webpack@npm:0.1902.7"
  dependencies:
    "@angular-devkit/architect": "npm:0.1902.7"
    rxjs: "npm:7.8.1"
  peerDependencies:
    webpack: ^5.30.0
    webpack-dev-server: ^5.0.2
  checksum: 10c0/8e1bfc2fce96215ddb668577d5591cfc5730d1ea21a0d459cfd2a293bf414a528c11e4deb193bc47cee2d7e02dfe467c2a32df3337daa5d5f17544bb5f097638
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:19.2.7, @angular-devkit/core@npm:>= 19.0.0 < 20.0.0, @angular-devkit/core@npm:^19.0.0":
  version: 19.2.7
  resolution: "@angular-devkit/core@npm:19.2.7"
  dependencies:
    ajv: "npm:8.17.1"
    ajv-formats: "npm:3.0.1"
    jsonc-parser: "npm:3.3.1"
    picomatch: "npm:4.0.2"
    rxjs: "npm:7.8.1"
    source-map: "npm:0.7.4"
  peerDependencies:
    chokidar: ^4.0.0
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 10c0/a2acb312a60dce6009da270ba28f104782f8ab635b8361373915fc2662f646e7366c458ebddc8c3312b280aabb1df0170c562334d44d8c5da19448ec5f3b8200
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:19.2.7, @angular-devkit/schematics@npm:>= 19.0.0 < 20.0.0, @angular-devkit/schematics@npm:^19.0.0":
  version: 19.2.7
  resolution: "@angular-devkit/schematics@npm:19.2.7"
  dependencies:
    "@angular-devkit/core": "npm:19.2.7"
    jsonc-parser: "npm:3.3.1"
    magic-string: "npm:0.30.17"
    ora: "npm:5.4.1"
    rxjs: "npm:7.8.1"
  checksum: 10c0/88ba702d1e9d65f684d134db6186c2533c41b4867b9d9bc94ab6ddc36899150fd680c8ae4de7be6f8cc77a57938b37d75734efa36e6a6b4ed5defe6cc131c9ae
  languageName: node
  linkType: hard

"@angular-eslint/builder@npm:^19.0.0":
  version: 19.3.0
  resolution: "@angular-eslint/builder@npm:19.3.0"
  dependencies:
    "@angular-devkit/architect": "npm:>= 0.1900.0 < 0.2000.0"
    "@angular-devkit/core": "npm:>= 19.0.0 < 20.0.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: "*"
  checksum: 10c0/dd634e3cbf47ce1229ca36313d996f70b44676526a214bbac2fe900173e07901ac0d0926276e386a68a70fc783ab349fb62c086dcd266cc2dae91a0172f5920d
  languageName: node
  linkType: hard

"@angular-eslint/bundled-angular-compiler@npm:19.3.0":
  version: 19.3.0
  resolution: "@angular-eslint/bundled-angular-compiler@npm:19.3.0"
  checksum: 10c0/4e6144d309fbf83223c772d5b88c519bf4cf99f0508ff1071964a8ce3f0f1cc78da9e4cd668e9cbe73e9f1a30fc787a5341d91a537dd9f8107a8a68851feaf4c
  languageName: node
  linkType: hard

"@angular-eslint/eslint-plugin-template@npm:19.3.0, @angular-eslint/eslint-plugin-template@npm:^19.0.0":
  version: 19.3.0
  resolution: "@angular-eslint/eslint-plugin-template@npm:19.3.0"
  dependencies:
    "@angular-eslint/bundled-angular-compiler": "npm:19.3.0"
    "@angular-eslint/utils": "npm:19.3.0"
    aria-query: "npm:5.3.2"
    axobject-query: "npm:4.1.0"
  peerDependencies:
    "@typescript-eslint/types": ^7.11.0 || ^8.0.0
    "@typescript-eslint/utils": ^7.11.0 || ^8.0.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: "*"
  checksum: 10c0/c42c2e160d84bb813a86d7371917968ce2af183b52a043535a024828429f44233afbb6d0d38382adb36f18d88c8b8680b376d520c8c7500a78e6bc3972a3b2d7
  languageName: node
  linkType: hard

"@angular-eslint/eslint-plugin@npm:19.3.0, @angular-eslint/eslint-plugin@npm:^19.0.0":
  version: 19.3.0
  resolution: "@angular-eslint/eslint-plugin@npm:19.3.0"
  dependencies:
    "@angular-eslint/bundled-angular-compiler": "npm:19.3.0"
    "@angular-eslint/utils": "npm:19.3.0"
  peerDependencies:
    "@typescript-eslint/utils": ^7.11.0 || ^8.0.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: "*"
  checksum: 10c0/d2bee1c094d02b0dc1e2caf0c13e540b5cb426817803bbd74ef8602deaceac931401314b942eaacfdced78db9915d0f477ba4b20ec6d91d914e1b615a6ad4c3a
  languageName: node
  linkType: hard

"@angular-eslint/schematics@npm:^19.0.0":
  version: 19.3.0
  resolution: "@angular-eslint/schematics@npm:19.3.0"
  dependencies:
    "@angular-devkit/core": "npm:>= 19.0.0 < 20.0.0"
    "@angular-devkit/schematics": "npm:>= 19.0.0 < 20.0.0"
    "@angular-eslint/eslint-plugin": "npm:19.3.0"
    "@angular-eslint/eslint-plugin-template": "npm:19.3.0"
    ignore: "npm:7.0.3"
    semver: "npm:7.7.1"
    strip-json-comments: "npm:3.1.1"
  checksum: 10c0/4752d2c66deb3c90de593194ad719f7990a51914eac19a11262f11b903e69cc2ed11535a570726a52e4e8014fd2505e3dfc6f4c432a5360e56d574d05ee4b120
  languageName: node
  linkType: hard

"@angular-eslint/template-parser@npm:^19.0.0":
  version: 19.3.0
  resolution: "@angular-eslint/template-parser@npm:19.3.0"
  dependencies:
    "@angular-eslint/bundled-angular-compiler": "npm:19.3.0"
    eslint-scope: "npm:^8.0.2"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: "*"
  checksum: 10c0/c025d96c1934b4153393d952783e53d53e3ec09729cd50bfe9c1289b4283efb7e1ea628f32f91626923610dd27cb90aa48059bbb7c06a001827abec3e4c99b89
  languageName: node
  linkType: hard

"@angular-eslint/utils@npm:19.3.0":
  version: 19.3.0
  resolution: "@angular-eslint/utils@npm:19.3.0"
  dependencies:
    "@angular-eslint/bundled-angular-compiler": "npm:19.3.0"
  peerDependencies:
    "@typescript-eslint/utils": ^7.11.0 || ^8.0.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: "*"
  checksum: 10c0/997fa1f8f6cf54aad0f552cba5f01ec59a9b11461dab1155475af84d69320d86c5e8a871e07855e51fc550f72845ba4cdc63429c78cc013e9f8db847814c031d
  languageName: node
  linkType: hard

"@angular/animations@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/animations@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": 19.2.6
    "@angular/core": 19.2.6
  checksum: 10c0/802d39c5421c2ac8d48acfa874b4c7ee61f8d813e1d684f41e6706fe27156d53e2eec8f1c00394cf96cc4413d0f3926e0bdbc6d8f686b9054869d1d2fc79ca9f
  languageName: node
  linkType: hard

"@angular/build@npm:19.2.7":
  version: 19.2.7
  resolution: "@angular/build@npm:19.2.7"
  dependencies:
    "@ampproject/remapping": "npm:2.3.0"
    "@angular-devkit/architect": "npm:0.1902.7"
    "@babel/core": "npm:7.26.10"
    "@babel/helper-annotate-as-pure": "npm:7.25.9"
    "@babel/helper-split-export-declaration": "npm:7.24.7"
    "@babel/plugin-syntax-import-attributes": "npm:7.26.0"
    "@inquirer/confirm": "npm:5.1.6"
    "@vitejs/plugin-basic-ssl": "npm:1.2.0"
    beasties: "npm:0.3.2"
    browserslist: "npm:^4.23.0"
    esbuild: "npm:0.25.1"
    fast-glob: "npm:3.3.3"
    https-proxy-agent: "npm:7.0.6"
    istanbul-lib-instrument: "npm:6.0.3"
    listr2: "npm:8.2.5"
    lmdb: "npm:3.2.6"
    magic-string: "npm:0.30.17"
    mrmime: "npm:2.0.1"
    parse5-html-rewriting-stream: "npm:7.0.0"
    picomatch: "npm:4.0.2"
    piscina: "npm:4.8.0"
    rollup: "npm:4.34.8"
    sass: "npm:1.85.0"
    semver: "npm:7.7.1"
    source-map-support: "npm:0.5.21"
    vite: "npm:6.2.5"
    watchpack: "npm:2.4.2"
  peerDependencies:
    "@angular/compiler": ^19.0.0 || ^19.2.0-next.0
    "@angular/compiler-cli": ^19.0.0 || ^19.2.0-next.0
    "@angular/localize": ^19.0.0 || ^19.2.0-next.0
    "@angular/platform-server": ^19.0.0 || ^19.2.0-next.0
    "@angular/service-worker": ^19.0.0 || ^19.2.0-next.0
    "@angular/ssr": ^19.2.7
    karma: ^6.4.0
    less: ^4.2.0
    ng-packagr: ^19.0.0 || ^19.2.0-next.0
    postcss: ^8.4.0
    tailwindcss: ^2.0.0 || ^3.0.0 || ^4.0.0
    typescript: ">=5.5 <5.9"
  dependenciesMeta:
    lmdb:
      optional: true
  peerDependenciesMeta:
    "@angular/localize":
      optional: true
    "@angular/platform-server":
      optional: true
    "@angular/service-worker":
      optional: true
    "@angular/ssr":
      optional: true
    karma:
      optional: true
    less:
      optional: true
    ng-packagr:
      optional: true
    postcss:
      optional: true
    tailwindcss:
      optional: true
  checksum: 10c0/20bff63bcf4bc335705448499bf6e0284e41555e51a227ccb4844bc970d6b294c16bcabd56c0e53428da9e11504610cfb88c5c4ecbed72f762031c0b8fac72b4
  languageName: node
  linkType: hard

"@angular/cli@npm:^19.0.0":
  version: 19.2.7
  resolution: "@angular/cli@npm:19.2.7"
  dependencies:
    "@angular-devkit/architect": "npm:0.1902.7"
    "@angular-devkit/core": "npm:19.2.7"
    "@angular-devkit/schematics": "npm:19.2.7"
    "@inquirer/prompts": "npm:7.3.2"
    "@listr2/prompt-adapter-inquirer": "npm:2.0.18"
    "@schematics/angular": "npm:19.2.7"
    "@yarnpkg/lockfile": "npm:1.1.0"
    ini: "npm:5.0.0"
    jsonc-parser: "npm:3.3.1"
    listr2: "npm:8.2.5"
    npm-package-arg: "npm:12.0.2"
    npm-pick-manifest: "npm:10.0.0"
    pacote: "npm:20.0.0"
    resolve: "npm:1.22.10"
    semver: "npm:7.7.1"
    symbol-observable: "npm:4.0.0"
    yargs: "npm:17.7.2"
  bin:
    ng: bin/ng.js
  checksum: 10c0/615a03d7b27b3eff026ee3efecd7fd688035016755414347f1baeb7a6943365d7ffa828d8e33fe0586793be90507ea9eb42ec66ad5fca55347e020799c54cc36
  languageName: node
  linkType: hard

"@angular/common@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/common@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/core": 19.2.6
    rxjs: ^6.5.3 || ^7.4.0
  checksum: 10c0/df3bc4bc386691159862a91b4904d6eef2185d1837425bee3fe32ca789138a43a281824e511c94942ed19757592525f5ce5aee42117004baa5d51fab9748dc84
  languageName: node
  linkType: hard

"@angular/compiler-cli@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/compiler-cli@npm:19.2.6"
  dependencies:
    "@babel/core": "npm:7.26.9"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
    chokidar: "npm:^4.0.0"
    convert-source-map: "npm:^1.5.1"
    reflect-metadata: "npm:^0.2.0"
    semver: "npm:^7.0.0"
    tslib: "npm:^2.3.0"
    yargs: "npm:^17.2.1"
  peerDependencies:
    "@angular/compiler": 19.2.6
    typescript: ">=5.5 <5.9"
  bin:
    ng-xi18n: bundles/src/bin/ng_xi18n.js
    ngc: bundles/src/bin/ngc.js
    ngcc: bundles/ngcc/index.js
  checksum: 10c0/07bafcdff23124f000eb516abd04edd79d39f8601d24696cdc6953fab7bd7ab5d443ab5c8afd61e5d6d57f8871aa1df8bddc322d096f53f3757dfed54c61675c
  languageName: node
  linkType: hard

"@angular/compiler@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/compiler@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  checksum: 10c0/09a9f3f57edd5ed9cc5130088c063a0f02c7539c9363974b3ab9bc66afc3e0d82313322398c1166d3da2696e0aaa25ef4f483e1be30288b41da72731b6ca54d9
  languageName: node
  linkType: hard

"@angular/core@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/core@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    rxjs: ^6.5.3 || ^7.4.0
    zone.js: ~0.15.0
  checksum: 10c0/fecb8ae068644352574f45b5c6f5e11d9da5f74d50853b1ebe30cf4c0f6b7bbfdb874fbf02832301e10f28ae9f07ea2402596b46c1e159bebd56c742484fb536
  languageName: node
  linkType: hard

"@angular/forms@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/forms@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": 19.2.6
    "@angular/core": 19.2.6
    "@angular/platform-browser": 19.2.6
    rxjs: ^6.5.3 || ^7.4.0
  checksum: 10c0/6eefc49a4f364f9f961392c244d594a146cbd7f1748387f3b5210ad87c73a92161c7215a752a57e913a07baf2431963de3fa49dce40a5bdaa02aed8bd3bf51d1
  languageName: node
  linkType: hard

"@angular/language-service@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/language-service@npm:19.2.6"
  checksum: 10c0/aeb295242bc0ba958c3f5d5d884e7f0b4dd772c2ab5d31364bb7859cfd8b8b45f5991ea8183e655e81f168c419904248585adc730ec66d09295b8bda77aa14a0
  languageName: node
  linkType: hard

"@angular/platform-browser-dynamic@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/platform-browser-dynamic@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": 19.2.6
    "@angular/compiler": 19.2.6
    "@angular/core": 19.2.6
    "@angular/platform-browser": 19.2.6
  checksum: 10c0/e976dcbad7e3e6a0b1af4affaf477fb875f591fc3b344fae1abba46985bceeef509e8d37aa21592d1187f5662d1d271ba4d9cde7a14d844492ab4abfd4326fd4
  languageName: node
  linkType: hard

"@angular/platform-browser@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/platform-browser@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/animations": 19.2.6
    "@angular/common": 19.2.6
    "@angular/core": 19.2.6
  peerDependenciesMeta:
    "@angular/animations":
      optional: true
  checksum: 10c0/705767384be4bc0d8f59c791c25ae6a2b97e7cba9c598f03cd533a340cbbe9f0aa23367872690ff1f4eec28698a873854f5683760052fbc4bf2cb230803ebe1f
  languageName: node
  linkType: hard

"@angular/router@npm:^19.0.0":
  version: 19.2.6
  resolution: "@angular/router@npm:19.2.6"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": 19.2.6
    "@angular/core": 19.2.6
    "@angular/platform-browser": 19.2.6
    rxjs: ^6.5.3 || ^7.4.0
  checksum: 10c0/53c179e06ed4f5f167e262377bb2e60368ac717994205838aa2a834dd09ffff37036dbe2e1cd1070ae1bb9cb777ccab148e0ddc29f0d3704a20133fdf28c5fe3
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/7d79621a6849183c415486af99b1a20b84737e8c11cd55b6544f688c51ce1fd710e6d869c3dd21232023da272a79b91efb3e83b5bc2dc65c1187c5fcd1b72ea8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/compat-data@npm:7.26.8"
  checksum: 10c0/66408a0388c3457fff1c2f6c3a061278dd7b3d2f0455ea29bb7b187fa52c60ae8b4054b3c0a184e21e45f0eaac63cf390737bc7504d1f4a088a6e7f652c068ca
  languageName: node
  linkType: hard

"@babel/core@npm:7.26.10, @babel/core@npm:^7.12.3, @babel/core@npm:^7.23.9":
  version: 7.26.10
  resolution: "@babel/core@npm:7.26.10"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.10"
    "@babel/helper-compilation-targets": "npm:^7.26.5"
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helpers": "npm:^7.26.10"
    "@babel/parser": "npm:^7.26.10"
    "@babel/template": "npm:^7.26.9"
    "@babel/traverse": "npm:^7.26.10"
    "@babel/types": "npm:^7.26.10"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/e046e0e988ab53841b512ee9d263ca409f6c46e2a999fe53024688b92db394346fa3aeae5ea0866331f62133982eee05a675d22922a4603c3f603aa09a581d62
  languageName: node
  linkType: hard

"@babel/core@npm:7.26.9":
  version: 7.26.9
  resolution: "@babel/core@npm:7.26.9"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.9"
    "@babel/helper-compilation-targets": "npm:^7.26.5"
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helpers": "npm:^7.26.9"
    "@babel/parser": "npm:^7.26.9"
    "@babel/template": "npm:^7.26.9"
    "@babel/traverse": "npm:^7.26.9"
    "@babel/types": "npm:^7.26.9"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/ed7212ff42a9453765787019b7d191b167afcacd4bd8fec10b055344ef53fa0cc648c9a80159ae4ecf870016a6318731e087042dcb68d1a2a9d34eb290dc014b
  languageName: node
  linkType: hard

"@babel/generator@npm:7.26.10, @babel/generator@npm:^7.26.10, @babel/generator@npm:^7.26.9":
  version: 7.26.10
  resolution: "@babel/generator@npm:7.26.10"
  dependencies:
    "@babel/parser": "npm:^7.26.10"
    "@babel/types": "npm:^7.26.10"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/88b3b3ea80592fc89349c4e1a145e1386e4042866d2507298adf452bf972f68d13bf699a845e6ab8c028bd52c2247013eb1221b86e1db5c9779faacba9c4b10e
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/generator@npm:7.27.0"
  dependencies:
    "@babel/parser": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/7cb10693d2b365c278f109a745dc08856cae139d262748b77b70ce1d97da84627f79648cab6940d847392c0e5d180441669ed958b3aee98d9c7d274b37c553bd
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:7.25.9, @babel/helper-annotate-as-pure@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-annotate-as-pure@npm:7.25.9"
  dependencies:
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/095b6ba50489d797733abebc4596a81918316a99e3632755c9f02508882912b00c2ae5e468532a25a5c2108d109ddbe9b7da78333ee7cc13817fc50c00cf06fe
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.25.9, @babel/helper-compilation-targets@npm:^7.26.5":
  version: 7.27.0
  resolution: "@babel/helper-compilation-targets@npm:7.27.0"
  dependencies:
    "@babel/compat-data": "npm:^7.26.8"
    "@babel/helper-validator-option": "npm:^7.25.9"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/375c9f80e6540118f41bd53dd54d670b8bf91235d631bdead44c8b313b26e9cd89aed5c6df770ad13a87a464497b5346bb72b9462ba690473da422f5402618b6
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.25.9":
  version: 7.27.0
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-member-expression-to-functions": "npm:^7.25.9"
    "@babel/helper-optimise-call-expression": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.27.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/c4945903136d934050e070f69a4d72ec425f1f70634e0ddf14ad36695f935125a6df559f8d5b94cc1ed49abd4ce9c5be8ef3ba033fa8d09c5dd78d1a9b97d8cc
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.25.9":
  version: 7.27.0
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/62513522a43521d8a29285a47127694ec28d66d793cd156cf875cdee6a9b3a9a1626c43c1eb75ce18fa2bf5dc3140f0a8081a34feb24272ecf66084f3cc3b00a
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-member-expression-to-functions@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/e08c7616f111e1fb56f398365e78858e26e466d4ac46dff25921adc5ccae9b232f66e952a2f4162bbe336627ba336c7fd9eca4835b6548935973d3380d77eaff
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/078d3c2b45d1f97ffe6bb47f61961be4785d2342a4156d8b42c92ee4e1b7b9e365655dd6cb25329e8fe1a675c91eeac7e3d04f0c518b67e417e29d6e27b6aa70
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.25.9, @babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ee111b68a5933481d76633dad9cdab30c41df4479f0e5e1cc4756dc9447c1afd2c9473b5ba006362e35b17f4ebddd5fca090233bef8dfc84dca9d9127e56ec3a
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-optimise-call-expression@npm:7.25.9"
  dependencies:
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/90203e6607edeadd2a154940803fd616c0ed92c1013d6774c4b8eb491f1a5a3448b68faae6268141caa5c456e55e3ee49a4ed2bd7ddaf2365daea321c435914c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.25.9, @babel/helper-plugin-utils@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-plugin-utils@npm:7.26.5"
  checksum: 10c0/cdaba71d4b891aa6a8dfbe5bac2f94effb13e5fa4c2c487667fdbaa04eae059b78b28d85a885071f45f7205aeb56d16759e1bed9c118b94b16e4720ef1ab0f65
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-remap-async-to-generator@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-wrap-function": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/6798b562f2788210980f29c5ee96056d90dc73458c88af5bd32f9c82e28e01975588aa2a57bb866c35556bd9b76bac937e824ee63ba472b6430224b91b4879e9
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.25.9, @babel/helper-replace-supers@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-replace-supers@npm:7.26.5"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.25.9"
    "@babel/helper-optimise-call-expression": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/b19b1245caf835207aaaaac3a494f03a16069ae55e76a2e1350b5acd560e6a820026997a8160e8ebab82ae873e8208759aa008eb8422a67a775df41f0a4633d4
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/09ace0c6156961624ac9524329ce7f45350bab94bbe24335cbe0da7dfaa1448e658771831983cb83fe91cf6635b15d0a3cab57c03b92657480bfb49fb56dd184
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-split-export-declaration@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 10c0/0254577d7086bf09b01bbde98f731d4fcf4b7c3fa9634fdb87929801307c1f6202a1352e3faa5492450fa8da4420542d44de604daf540704ff349594a78184f6
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 10c0/7244b45d8e65f6b4338a6a68a8556f2cb161b782343e97281a5f2b9b93e420cad0d9f5773a59d79f61d0c448913d06f6a2358a87f2e203cf112e3c5b53522ee6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 10c0/4fc6f830177b7b7e887ad3277ddb3b91d81e6c4a24151540d9d1023e8dc6b1c0505f0f0628ae653601eb4388a8db45c1c14b2c07a9173837aef7e4116456259d
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 10c0/27fb195d14c7dcb07f14e58fe77c44eea19a6a40a74472ec05c441478fa0bb49fa1c32b2d64be7a38870ee48ef6601bdebe98d512f0253aea0b39756c4014f3e
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-wrap-function@npm:7.25.9"
  dependencies:
    "@babel/template": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/b6627d83291e7b80df020f8ee2890c52b8d49272962cac0114ef90f189889c90f1027985873d1b5261a4e986e109b2754292dc112392f0b1fcbfc91cc08bd003
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.10, @babel/helpers@npm:^7.26.9":
  version: 7.27.0
  resolution: "@babel/helpers@npm:7.27.0"
  dependencies:
    "@babel/template": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
  checksum: 10c0/a3c64fd2d8b164c041808826cc00769d814074ea447daaacaf2e3714b66d3f4237ef6e420f61d08f463d6608f3468c2ac5124ab7c68f704e20384def5ade95f4
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.14.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.26.10, @babel/parser@npm:^7.26.9, @babel/parser@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/parser@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.27.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/ba2ed3f41735826546a3ef2a7634a8d10351df221891906e59b29b0a0cd748f9b0e7a6f07576858a9de8e77785aad925c8389ddef146de04ea2842047c9d2859
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/7aab47fcbb8c1ddc195a3cd66609edcad54c5022f018db7de40185f0182950389690e953e952f117a1737b72f665ff02ad30de6c02b49b97f1d8f4ccdffedc34
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/3a652b3574ca62775c5f101f8457950edc540c3581226579125da535d67765f41ad7f0e6327f8efeb2540a5dad5bb0c60a89fb934af3f67472e73fb63612d004
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/18fc9004104a150f9f5da9f3307f361bc3104d16778bb593b7523d5110f04a8df19a2587e6bdd5e726fb1d397191add45223f4f731bb556c33f14f2779d596e8
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
    "@babel/plugin-transform-optional-chaining": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 10c0/3f6c8781a2f7aa1791a31d2242399ca884df2ab944f90c020b6f112fb19f05fa6dad5be143d274dad1377e40415b63d24d5489faf5060b9c4a99e55d8f0c317c
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/02b365f0cc4df8b8b811c68697c93476da387841e5f153fe42766f34241b685503ea51110d5ed6df7132759820b93e48d9fa3743cffc091eed97c19f7e5fe272
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e605e0070da087f6c35579499e65801179a521b6842c15181a1e305c04fded2393f11c1efd09b087be7f8b083d1b75e8f3efcbc1292b4f60d3369e14812cff63
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/525b174e60b210d96c1744c1575fc2ddedcc43a479cba64a5344cf77bd0541754fc58120b5a11ff832ba098437bb05aa80900d1f49bb3d888c5e349a4a3a356e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:7.26.0, @babel/plugin-syntax-import-attributes@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e594c185b12bfe0bbe7ca78dfeebe870e6d569a12128cac86f3164a075fe0ff70e25ddbd97fd0782906b91f65560c9dc6957716b7b4a68aba2516c9b7455e352
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/9144e5b02a211a4fb9a0ce91063f94fbe1004e80bde3485a0910c9f14897cf83fabd8c21267907cff25db8e224858178df0517f14333cfcf3380ad9a4139cb50
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/851fef9f58be60a80f46cc0ce1e46a6f7346a6f9d50fa9e0fa79d46ec205320069d0cc157db213e2bea88ef5b7d9bd7618bb83f0b1996a836e2426c3a3a1f622
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:7.26.8, @babel/plugin-transform-async-generator-functions@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.26.8"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    "@babel/helper-remap-async-to-generator": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.26.8"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f6fefce963fe2e6268dde1958975d7adbce65fba94ca6f4bc554c90da03104ad1dd2e66d03bc0462da46868498428646e30b03a218ef0e5a84bfc87a7e375cec
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:7.25.9, @babel/plugin-transform-async-to-generator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.25.9"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-remap-async-to-generator": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c443d9e462ddef733ae56360064f32fc800105803d892e4ff32d7d6a6922b3765fa97b9ddc9f7f1d3f9d8c2d95721d85bef9dbf507804214c6cf6466b105c168
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.26.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2f3060800ead46b09971dd7bf830d66383b7bc61ced9945633b4ef9bf87787956ea83fcf49b387cecb377812588c6b81681714c760f9cf89ecba45edcbab1192
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.25.9":
  version: 7.27.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/15a604fac04151a795ff3213c73ece06bda7cd5f7c8cb7a3b29563ab243f0b3f7cba9e6facfc9d70e3e63b21af32f9d26bd10ccc58e1c425c7801186014b5ce4
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-class-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f0603b6bd34d8ba62c03fc0572cb8bbc75874d097ac20cc7c5379e001081210a84dba1749e7123fca43b978382f605bb9973c99caf2c5b4c492d5c0a4a441150
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-transform-class-static-block@npm:7.26.0"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 10c0/cdcf5545ae6514ed75fbd73cccfa209c6a5dfdf0c2bb7bb62c0fb4ec334a32281bcf1bc16ace494d9dbe93feb8bdc0bd3cf9d9ccb6316e634a67056fa13b741b
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-classes@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/02742ea7cd25be286c982e672619effca528d7a931626a6f3d6cea11852951b7ee973276127eaf6418ac0e18c4d749a16b520709c707e86a67012bd23ff2927d
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-computed-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/template": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/948c0ae3ce0ba2375241d122a9bc7cda4a7ac8110bd8a62cd804bc46a5fdb7a7a42c7799c4cd972e14e0a579d2bd0999b92e53177b73f240bb0d4b09972c758b
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-destructuring@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7beec5fda665d108f69d5023aa7c298a1e566b973dd41290faa18aeea70f6f571295c1ece0a058f3ceb6c6c96de76de7cd34f5a227fbf09a1b8d8a735d28ca49
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7c3471ae5cf7521fd8da5b03e137e8d3733fc5ee4524ce01fb0c812f0bb77cb2c9657bc8a6253186be3a15bb4caa8974993c7ddc067f554ecc6a026f0a3b5e12
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d0c74894b9bf6ff2a04189afffb9cd43d87ebd7b7943e51a827c92d2aaa40fa89ac81565a2fd6fbeabf9e38413a9264c45862eee2b017f1d49046cc3c8ff06b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/a8039a6d2b90e011c7b30975edee47b5b1097cf3c2f95ec1f5ddd029898d783a995f55f7d6eb8d6bb8873c060fb64f9f1ccba938dfe22d118d09cf68e0cd3bf6
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e643a8209072b668350f5788f23c64e9124f81f958b595c80fecca6561086d8ef346c04391b9e5e4cad8b8cbe22c258f0cd5f4ea89b97e74438e7d1abfd98cf
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.26.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cac922e851c6a0831fdd2e3663564966916015aeff7f4485825fc33879cbc3a313ceb859814c9200248e2875d65bb13802a723e5d7d7b40a2e90da82a5a1e15c
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f291ea2ec5f36de9028a00cbd5b32f08af281b8183bf047200ff001f4cb260be56f156b2449f42149448a4a033bd6e86a3a7f06d0c2825532eb0ae6b03058dfb
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.26.9":
  version: 7.26.9
  resolution: "@babel/plugin-transform-for-of@npm:7.26.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e28a521521cf9f84ddd69ca8da7c89fb9f7aa38e4dea35742fe973e4e1d7c23f9cee1a4861a2fdd9e9f18ff945886a44d7335cea1c603b96bfcb1c7c8791ef09
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-function-name@npm:7.25.9"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8e67fbd1dd367927b8b6afdf0a6e7cb3a3fd70766c52f700ca77428b6d536f6c9d7ec643e7762d64b23093233765c66bffa40e31aabe6492682879bcb45423e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-json-strings@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00bc2d4751dfc9d44ab725be16ee534de13cfd7e77dfb386e5dac9e48101ce8fcbc5971df919dc25b3f8a0fa85d6dc5f2a0c3cf7ec9d61c163d9823c091844f0
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00b14e9c14cf1e871c1f3781bf6334cac339c360404afd6aba63d2f6aca9270854d59a2b40abff1c4c90d4ffdca614440842d3043316c2f0ceb155fdf7726b3b
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6e2051e10b2d6452980fc4bdef9da17c0d6ca48f81b8529e8804b031950e4fff7c74a7eb3de4a2b6ad22ffb631d0b67005425d232cce6e2b29ce861c78ed04f5
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/91d17b451bcc5ea9f1c6f8264144057ade3338d4b92c0b248366e4db3a7790a28fd59cc56ac433a9627a9087a17a5684e53f4995dd6ae92831cb72f1bd540b54
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-amd@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/849957d9484d0a2d93331226ed6cf840cee7d57454549534c447c93f8b839ef8553eae9877f8f550e3c39f14d60992f91244b2e8e7502a46064b56c5d68ba855
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.26.3"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/82e59708f19f36da29531a64a7a94eabbf6ff46a615e0f5d9b49f3f59e8ef10e2bac607d749091508d3fa655146c9e5647c3ffeca781060cdabedb4c7a33c6f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8299e3437542129c2684b86f98408c690df27db4122a79edded4782cf04e755d6ecb05b1e812c81a34224a81e664303392d5f3c36f3d2d51fdc99bb91c881e9a
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-umd@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/fa11a621f023e2ac437b71d5582f819e667c94306f022583d77da9a8f772c4128861a32bbb63bef5cba581a70cd7dbe87a37238edaafcfacf889470c395e7076
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/32b14fda5c885d1706863f8af2ee6c703d39264355b57482d3a24fce7f6afbd4c7a0896e501c0806ed2b0759beb621bf7f3f7de1fbbc82026039a98d961e78ef
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-new-target@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7b5f1b7998f1cf183a7fa646346e2f3742e5805b609f28ad5fee22d666a15010f3e398b7e1ab78cddb7901841a3d3f47135929af23d54e8bf4ce69b72051f71e
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.26.6":
  version: 7.26.6
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.26.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/574d6db7cbc5c092db5d1dece8ce26195e642b9c40dbfeaf3082058a78ad7959c1c333471cdd45f38b784ec488850548075d527b178c5010ee9bff7aa527cc7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ad63ad341977844b6f9535fcca15ca0d6d6ad112ed9cc509d4f6b75e9bf4b1b1a96a0bcb1986421a601505d34025373608b5f76d420d924b4e21f86b1a1f2749
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.25.9"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/plugin-transform-parameters": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/02077d8abd83bf6a48ff0b59e98d7561407cf75b591cffd3fdc5dc5e9a13dec1c847a7a690983762a3afecddb244831e897e0515c293e7c653b262c30cd614af
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-object-super@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0348d00e76f1f15ada44481a76e8c923d24cba91f6e49ee9b30d6861eb75344e7f84d62a18df8a6f9e9a7eacf992f388174b7f9cc4ce48287bcefca268c07600
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/722fd5ee12ab905309d4e84421584fce4b6d9e6b639b06afb20b23fa809e6ab251e908a8d5e8b14d066a28186b8ef8f58d69fd6eca9ce1b9ef7af08333378f6c
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/041ad2beae5affb8e68a0bcb6882a2dadb758db3c629a0e012f57488ab43a822ac1ea17a29db8ef36560a28262a5dfa4dbbbf06ed6e431db55abe024b7cd3961
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-parameters@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/aecb446754b9e09d6b6fa95fd09e7cf682f8aaeed1d972874ba24c0a30a7e803ad5f014bb1fffc7bfeed22f93c0d200947407894ea59bf7687816f2f464f8df3
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-private-methods@npm:7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/64bd71de93d39daefa3e6c878d6f2fd238ed7d4ecfb13b0e771ddbbc131487def3ceb405b62b534a5cbb5043046b504e1b189b0a45229cc75af979a9fbcaa7bd
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-create-class-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d4965de19d9f204e692cc74dbc39f0bb469e5f29df96dd4457ea23c5e5596fba9d5af76eaa96f9d48a9fc20ec5f12a94c679285e36b8373406868ea228109e27
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-property-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1639e35b2438ccf3107af760d34e6a8e4f9acdd3ae6186ae771a6e3029bd59dfe778e502d67090f1185ecda5c16addfed77561e39c518a3f51ff10d41790e106
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.25.9":
  version: 7.27.0
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    regenerator-transform: "npm:^0.15.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/aa1c6a1592338df96034e0c3933d9c84d0ae25e9768413fda90d4896470192a11e2ab146dbcb92005c5059bbea67aea3d11936de8e4be382613efceafc9c92b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.26.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4abc1db6c964efafc7a927cda814c7275275afa4b530483e0936fd614de23cb5802f7ca43edaa402008a723d4e7eac282b6f5283aa2eeb3b27da6d6c1dd7f8ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-reserved-words@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8b028b80d1983e3e02f74e21924323cc66ba930e5c5758909a122aa7d80e341b8b0f42e1698e42b50d47a6ba911332f584200b28e1a4e2104b7514d9dc011e96
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:7.26.10":
  version: 7.26.10
  resolution: "@babel/plugin-transform-runtime@npm:7.26.10"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4b70a63b904a3f7faa6ca95f9034d2f29330764820b06cf1814dda4ab0482b233a28241e98d8497bc1690dd31972e72861d8534ae0e37f26e04637e7d615e43d
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/05a20d45f0fb62567644c507ccd4e379c1a74dacf887d2b2cac70247415e3f6d7d3bf4850c8b336053144715fedb6200fc38f7130c4b76c94eec9b9c0c2a8e9b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-spread@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/996c8fed238efc30e0664f9f58bd7ec8c148f4659f84425f68923a094fe891245711d26eb10d1f815f50c124434e076e860dbe9662240844d1b77cd09907dcdf
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e9612b0615dab4c4fba1c560769616a9bd7b9226c73191ef84b6c3ee185c8b719b4f887cdd8336a0a13400ce606ab4a0d33bc8fa6b4fcdb53e2896d07f2568f6
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/plugin-transform-template-literals@npm:7.26.8"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/205a938ded9554857a604416d369023a961334b6c20943bd861b45f0e5dbbeca1cf6fda1c2049126e38a0d18865993433fdc78eae3028e94836b3b643c08ba0d
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.26.7":
  version: 7.27.0
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.26.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00adbd4e044166ac291978bd64173b4a0d36cbcfae3495a196816dd16ba889cc8b5becee232086241d714cd67a80c15742402504fc36f6db4f746a7dd8d2b1c4
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/615c84d7c53e1575d54ba9257e753e0b98c5de1e3225237d92f55226eaab8eb5bceb74df43f50f4aa162b0bbcc934ed11feafe2b60b8ec4934ce340fad4b8828
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1685836fc38af4344c3d2a9edbd46f7c7b28d369b63967d5b83f2f6849ec45b97223461cea3d14cc3f0be6ebb284938e637a5ca3955c0e79c873d62f593d615c
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/448004f978279e726af26acd54f63f9002c9e2582ecd70d1c5c4436f6de490fcd817afb60016d11c52f5ef17dbaac2590e8cc7bfaf4e91b58c452cf188c7920f
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/56ee04fbe236b77cbcd6035cbf0be7566d1386b8349154ac33244c25f61170c47153a9423cd1d92855f7d6447b53a4a653d9e8fd1eaeeee14feb4b2baf59bd9f
  languageName: node
  linkType: hard

"@babel/preset-env@npm:7.26.9":
  version: 7.26.9
  resolution: "@babel/preset-env@npm:7.26.9"
  dependencies:
    "@babel/compat-data": "npm:^7.26.8"
    "@babel/helper-compilation-targets": "npm:^7.26.5"
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    "@babel/helper-validator-option": "npm:^7.25.9"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "npm:^7.25.9"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": "npm:^7.25.9"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.25.9"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.25.9"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.25.9"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions": "npm:^7.26.0"
    "@babel/plugin-syntax-import-attributes": "npm:^7.26.0"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.25.9"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.26.8"
    "@babel/plugin-transform-async-to-generator": "npm:^7.25.9"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.26.5"
    "@babel/plugin-transform-block-scoping": "npm:^7.25.9"
    "@babel/plugin-transform-class-properties": "npm:^7.25.9"
    "@babel/plugin-transform-class-static-block": "npm:^7.26.0"
    "@babel/plugin-transform-classes": "npm:^7.25.9"
    "@babel/plugin-transform-computed-properties": "npm:^7.25.9"
    "@babel/plugin-transform-destructuring": "npm:^7.25.9"
    "@babel/plugin-transform-dotall-regex": "npm:^7.25.9"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.25.9"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "npm:^7.25.9"
    "@babel/plugin-transform-dynamic-import": "npm:^7.25.9"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.26.3"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.25.9"
    "@babel/plugin-transform-for-of": "npm:^7.26.9"
    "@babel/plugin-transform-function-name": "npm:^7.25.9"
    "@babel/plugin-transform-json-strings": "npm:^7.25.9"
    "@babel/plugin-transform-literals": "npm:^7.25.9"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.25.9"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.25.9"
    "@babel/plugin-transform-modules-amd": "npm:^7.25.9"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.26.3"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.25.9"
    "@babel/plugin-transform-modules-umd": "npm:^7.25.9"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.25.9"
    "@babel/plugin-transform-new-target": "npm:^7.25.9"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.26.6"
    "@babel/plugin-transform-numeric-separator": "npm:^7.25.9"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.25.9"
    "@babel/plugin-transform-object-super": "npm:^7.25.9"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.25.9"
    "@babel/plugin-transform-optional-chaining": "npm:^7.25.9"
    "@babel/plugin-transform-parameters": "npm:^7.25.9"
    "@babel/plugin-transform-private-methods": "npm:^7.25.9"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.25.9"
    "@babel/plugin-transform-property-literals": "npm:^7.25.9"
    "@babel/plugin-transform-regenerator": "npm:^7.25.9"
    "@babel/plugin-transform-regexp-modifiers": "npm:^7.26.0"
    "@babel/plugin-transform-reserved-words": "npm:^7.25.9"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.25.9"
    "@babel/plugin-transform-spread": "npm:^7.25.9"
    "@babel/plugin-transform-sticky-regex": "npm:^7.25.9"
    "@babel/plugin-transform-template-literals": "npm:^7.26.8"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.26.7"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.25.9"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.25.9"
    "@babel/plugin-transform-unicode-regex": "npm:^7.25.9"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.25.9"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    core-js-compat: "npm:^3.40.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6812ca76bd38165a58fe8354bab5e7204e1aa17d8b9270bd8f8babb08cc7fa94cd29525fe41b553f2ba0e84033d566f10da26012b8ee0f81897005c5225d0051
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/9d02f70d7052446c5f3a4fb39e6b632695fb6801e46d31d7f7c5001f7c18d31d1ea8369212331ca7ad4e7877b73231f470b0d559162624128f1b80fe591409e6
  languageName: node
  linkType: hard

"@babel/runtime@npm:7.26.10, @babel/runtime@npm:^7.8.4":
  version: 7.26.10
  resolution: "@babel/runtime@npm:7.26.10"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/6dc6d88c7908f505c4f7770fb4677dfa61f68f659b943c2be1f2a99cb6680343462867abf2d49822adc435932919b36c77ac60125793e719ea8745f2073d3745
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.9, @babel/template@npm:^7.26.9, @babel/template@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/template@npm:7.27.0"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/parser": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
  checksum: 10c0/13af543756127edb5f62bf121f9b093c09a2b6fe108373887ccffc701465cfbcb17e07cf48aa7f440415b263f6ec006e9415c79dfc2e8e6010b069435f81f340
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.9, @babel/traverse@npm:^7.26.10, @babel/traverse@npm:^7.26.5, @babel/traverse@npm:^7.26.8, @babel/traverse@npm:^7.26.9, @babel/traverse@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/traverse@npm:7.27.0"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.27.0"
    "@babel/parser": "npm:^7.27.0"
    "@babel/template": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/c7af29781960dacaae51762e8bc6c4b13d6ab4b17312990fbca9fc38e19c4ad7fecaae24b1cf52fb844e8e6cdc76c70ad597f90e496bcb3cc0a1d66b41a0aa5b
  languageName: node
  linkType: hard

"@babel/types@npm:^7.24.7, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.10, @babel/types@npm:^7.26.9, @babel/types@npm:^7.27.0, @babel/types@npm:^7.4.4":
  version: 7.27.0
  resolution: "@babel/types@npm:7.27.0"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10c0/6f1592eabe243c89a608717b07b72969be9d9d2fce1dee21426238757ea1fa60fdfc09b29de9e48d8104311afc6e6fb1702565a9cc1e09bc1e76f2b2ddb0f6e1
  languageName: node
  linkType: hard

"@capacitor/app@npm:7.0.1":
  version: 7.0.1
  resolution: "@capacitor/app@npm:7.0.1"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/dded2134a429b3c9fb377233ec6c7f73a6cc66941294c0b8d5ad8f53fa0511a168ed3679daaff111644c85d72f552e3006a809c7ab5550b293316a978a31806c
  languageName: node
  linkType: hard

"@capacitor/assets@npm:^3.0.5":
  version: 3.0.5
  resolution: "@capacitor/assets@npm:3.0.5"
  dependencies:
    "@capacitor/cli": "npm:^5.3.0"
    "@ionic/utils-array": "npm:2.1.6"
    "@ionic/utils-fs": "npm:3.1.7"
    "@trapezedev/project": "npm:^7.0.10"
    commander: "npm:8.3.0"
    debug: "npm:4.3.4"
    fs-extra: "npm:10.1.0"
    node-fetch: "npm:2.7.0"
    node-html-parser: "npm:5.4.2"
    sharp: "npm:0.32.6"
    tslib: "npm:2.6.2"
    yargs: "npm:17.7.2"
  bin:
    capacitor-assets: bin/capacitor-assets
  checksum: 10c0/d43838d3c16c5476bf76537859be452f919723f828d748e4cbd6263803102312e0e8fa3651bf795e4a7b744a0d5ec4c26c01a277edb5f1396f584028bca45f56
  languageName: node
  linkType: hard

"@capacitor/browser@npm:^7.0.1":
  version: 7.0.1
  resolution: "@capacitor/browser@npm:7.0.1"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/f292e33f5f1be10b8cd25e5c1290d8addb219f159831219c7c09f38a246e546512d68ae60a1d21bfa9bd9e4aba6c847b6d33c502cb332694f82b4c2402bd9fe6
  languageName: node
  linkType: hard

"@capacitor/cli@npm:7.2.0":
  version: 7.2.0
  resolution: "@capacitor/cli@npm:7.2.0"
  dependencies:
    "@ionic/cli-framework-output": "npm:^2.2.8"
    "@ionic/utils-subprocess": "npm:^3.0.1"
    "@ionic/utils-terminal": "npm:^2.3.5"
    commander: "npm:^12.1.0"
    debug: "npm:^4.4.0"
    env-paths: "npm:^2.2.0"
    fs-extra: "npm:^11.2.0"
    kleur: "npm:^4.1.5"
    native-run: "npm:^2.0.1"
    open: "npm:^8.4.0"
    plist: "npm:^3.1.0"
    prompts: "npm:^2.4.2"
    rimraf: "npm:^6.0.1"
    semver: "npm:^7.6.3"
    tar: "npm:^6.1.11"
    tslib: "npm:^2.8.1"
    xml2js: "npm:^0.6.2"
  bin:
    cap: bin/capacitor
    capacitor: bin/capacitor
  checksum: 10c0/6718daa7c1c8c52b1afce510e567bb4d5eb6f6e1e3da15b80d42cf570ada1c343c74e61dafb4204826f4192dbfca4ad7fca9f44c834681afca0d9c68647a62b0
  languageName: node
  linkType: hard

"@capacitor/cli@npm:^5.3.0":
  version: 5.7.8
  resolution: "@capacitor/cli@npm:5.7.8"
  dependencies:
    "@ionic/cli-framework-output": "npm:^2.2.5"
    "@ionic/utils-fs": "npm:^3.1.6"
    "@ionic/utils-subprocess": "npm:^2.1.11"
    "@ionic/utils-terminal": "npm:^2.3.3"
    commander: "npm:^9.3.0"
    debug: "npm:^4.3.4"
    env-paths: "npm:^2.2.0"
    kleur: "npm:^4.1.4"
    native-run: "npm:^2.0.0"
    open: "npm:^8.4.0"
    plist: "npm:^3.0.5"
    prompts: "npm:^2.4.2"
    rimraf: "npm:^4.4.1"
    semver: "npm:^7.3.7"
    tar: "npm:^6.1.11"
    tslib: "npm:^2.4.0"
    xml2js: "npm:^0.5.0"
  bin:
    cap: bin/capacitor
    capacitor: bin/capacitor
  checksum: 10c0/55c15d469cb567f7c4ae1e4e7d50d4cfed731af3fb1e253c1c6ab65f1378b9a00739981fea3057cc161931e74fcc6075cf8d5efb1782ea1c1644f4cc6a8caa21
  languageName: node
  linkType: hard

"@capacitor/core@npm:7.2.0":
  version: 7.2.0
  resolution: "@capacitor/core@npm:7.2.0"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/b8fc3e20f7a35ad67baa7b82f1d01c527bf3a09a7eac7a85fab1054897c97f69a228db4a30f505acaa16148e1806408f459d15f3dbd3f409a2b9f90f1ef86fd2
  languageName: node
  linkType: hard

"@capacitor/haptics@npm:7.0.1":
  version: 7.0.1
  resolution: "@capacitor/haptics@npm:7.0.1"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/3e8145b80d29acfb26b6b615b13dda22bd9a1afb4c110b148efb27ccd4db26cc8e26246f83b1c11704a3bf159224db5247fdf91dd3c9cf652122fc76fed7aff7
  languageName: node
  linkType: hard

"@capacitor/ios@npm:^7.2.0":
  version: 7.2.0
  resolution: "@capacitor/ios@npm:7.2.0"
  peerDependencies:
    "@capacitor/core": ^7.2.0
  checksum: 10c0/125fe6d2100172b7e2d0a69f386b577253e24a7b03a125d898ea3a561a317b4a0b61034f8b293904e710c7106c2a6680e800d699f29520ccc70940f12e9780d3
  languageName: node
  linkType: hard

"@capacitor/keyboard@npm:7.0.1":
  version: 7.0.1
  resolution: "@capacitor/keyboard@npm:7.0.1"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/0c10d79d46386dc0a672fcc148a7ad411193d94a424494ee775cf642e0f66776d3625bc0ad05e92ecd75c2a2a6317a4ebe4decee55b10d0dfadf974515ca7372
  languageName: node
  linkType: hard

"@capacitor/status-bar@npm:7.0.1":
  version: 7.0.1
  resolution: "@capacitor/status-bar@npm:7.0.1"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/bd1368030c0c051a32549b2dde6839e28328ec070896de202bb16b40af823772869687466569fa7a0186a4af8843597ced7933a5c04a2b88e924c858a4cf8675
  languageName: node
  linkType: hard

"@capgo/inappbrowser@npm:^7.9.3":
  version: 7.9.3
  resolution: "@capgo/inappbrowser@npm:7.9.3"
  peerDependencies:
    "@capacitor/core": ">=7.0.0"
  checksum: 10c0/812b4a55ccd47e04001a5351d9338a9311ea74b5b35edcec5526f62ae18dbe2f10f61127ef7e42999887480732fed3a2a3c45bb3c149e255054e3cd335ff4cf7
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: 10c0/eb42729851adca56d19a08e48d5a1e95efd2a32c55ae0323de8119052be0510d4b7a1611f2abcbf28c044a6c11e6b7d38f99fccdad7429300c37a8ea5fb95b44
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:0.3.9"
  checksum: 10c0/05c5368c13b662ee4c122c7bfbe5dc0b613416672a829f3e78bc49a357a197e0218d6e74e7c66cfcd04e15a179acab080bd3c69658c9fbefd0e1ccd950a07fc6
  languageName: node
  linkType: hard

"@discoveryjs/json-ext@npm:0.6.3":
  version: 0.6.3
  resolution: "@discoveryjs/json-ext@npm:0.6.3"
  checksum: 10c0/778a9f9d5c3696da3c1f9fa4186613db95a1090abbfb6c2601430645c0d0158cd5e4ba4f32c05904e2dd2747d57710f6aab22bd2f8aa3c4e8feab9b247c65d85
  languageName: node
  linkType: hard

"@emran-alhaddad/saudi-riyal-font@npm:^1.0.3":
  version: 1.0.3
  resolution: "@emran-alhaddad/saudi-riyal-font@npm:1.0.3"
  checksum: 10c0/666ee1bef3697663c903b942a05edaf9931fe198796d23626388c01bacdee7a0bdfca7d992cf1ab0085158a07e922d6095caf6ce9a045191a37720a88b22ea0c
  languageName: node
  linkType: hard

"@es-joy/jsdoccomment@npm:~0.46.0":
  version: 0.46.0
  resolution: "@es-joy/jsdoccomment@npm:0.46.0"
  dependencies:
    comment-parser: "npm:1.4.1"
    esquery: "npm:^1.6.0"
    jsdoc-type-pratt-parser: "npm:~4.0.0"
  checksum: 10c0/a7a67936ebf6d9aaf74af018c3ac744769af3552b05ad9b88fca96b2ffdca16e724b0ff497f53634ec4cca81e98d8c471b6b6bde0fa5b725af4222ad9a0707f0
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/aix-ppc64@npm:0.25.1"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-arm64@npm:0.25.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-arm@npm:0.25.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-x64@npm:0.25.1"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/darwin-arm64@npm:0.25.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/darwin-x64@npm:0.25.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/freebsd-arm64@npm:0.25.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/freebsd-x64@npm:0.25.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-arm64@npm:0.25.1"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-arm@npm:0.25.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-ia32@npm:0.25.1"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-loong64@npm:0.25.1"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-mips64el@npm:0.25.1"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-ppc64@npm:0.25.1"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-riscv64@npm:0.25.1"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-s390x@npm:0.25.1"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-x64@npm:0.25.1"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/netbsd-arm64@npm:0.25.1"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/netbsd-x64@npm:0.25.1"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/openbsd-arm64@npm:0.25.1"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/openbsd-x64@npm:0.25.1"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/sunos-x64@npm:0.25.1"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-arm64@npm:0.25.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-ia32@npm:0.25.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-x64@npm:0.25.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.6.0
  resolution: "@eslint-community/eslint-utils@npm:4.6.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/a64131c1b43021e3a84267f6011fd678a936718097c9be169c37a40ada2c7016bec7d6685ecc88112737d57733f36837bb90d9425ad48d2e2aa351d999d32443
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.0
  resolution: "@eslint/config-array@npm:0.20.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/94bc5d0abb96dc5295ff559925242ff75a54eacfb3576677e95917e42f7175e1c4b87bf039aa2a872f949b4852ad9724bf2f7529aaea6b98f28bb3fca7f1d659
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.0":
  version: 0.2.1
  resolution: "@eslint/config-helpers@npm:0.2.1"
  checksum: 10c0/3e829a78b0bb4f7c44384ba1df3986e5de24b7f440ad5c6bb3cfc366ded773a869ca9ee8d212b5a563ae94596c5940dea6fd2ea1ee53a84c6241ac953dcb8bb7
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.12.0":
  version: 0.12.0
  resolution: "@eslint/core@npm:0.12.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/d032af81195bb28dd800c2b9617548c6c2a09b9490da3c5537fd2a1201501666d06492278bb92cfccac1f7ac249e58601dd87f813ec0d6a423ef0880434fa0c3
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.13.0":
  version: 0.13.0
  resolution: "@eslint/core@npm:0.13.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/ba724a7df7ed9dab387481f11d0d0f708180f40be93acce2c21dacca625c5867de3528760c42f1c457ccefe6a669d525ff87b779017eabc0d33479a36300797b
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.24.0":
  version: 9.24.0
  resolution: "@eslint/js@npm:9.24.0"
  checksum: 10c0/efe22e29469e4140ac3e2916be8143b1bcfd1084a6edf692b7a58a3e54949d53c67f7f979bc0a811db134d9cc1e7bff8aa71ef1376b47eecd7e226b71206bb36
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.7":
  version: 0.2.8
  resolution: "@eslint/plugin-kit@npm:0.2.8"
  dependencies:
    "@eslint/core": "npm:^0.13.0"
    levn: "npm:^0.4.1"
  checksum: 10c0/554847c8f2b6bfe0e634f317fc43d0b54771eea0015c4f844f75915fdb9e6170c830c004291bad57db949d61771732e459f36ed059f45cf750af223f77357c5c
  languageName: node
  linkType: hard

"@hugeicons-pro/core-solid-rounded@npm:^1.0.14":
  version: 1.0.14
  resolution: "@hugeicons-pro/core-solid-rounded@npm:1.0.14"
  checksum: 10c0/5de4c7266a9f2645e500befea1f7b1a1fb1bf991cfb46eb951eebd4c064eb0b6bd18f623a602f0239f01abeb00d7afc241048e9f6e1091dd730c7a6bf4897847
  languageName: node
  linkType: hard

"@hugeicons-pro/core-stroke-rounded@npm:^1.0.14":
  version: 1.0.14
  resolution: "@hugeicons-pro/core-stroke-rounded@npm:1.0.14"
  checksum: 10c0/fe315aafada9610f60dc2d5a4c57796d7588f738cc228200f6892283d9db017941e5f004b92581c7e1fd568807950a9894b4d918d332835fe7cf070ae5984e5b
  languageName: node
  linkType: hard

"@hugeicons/angular@npm:^1.0.4":
  version: 1.0.4
  resolution: "@hugeicons/angular@npm:1.0.4"
  dependencies:
    tslib: "npm:^2.6.0"
  peerDependencies:
    "@angular/common": ">=17.0.0 <20.0.0"
    "@angular/core": ">=17.0.0 <20.0.0"
  checksum: 10c0/236cf82aaaa4984b8edf7c1620dbf274073649827319b323bbbb6dca3e5f8c3bf540eaaf8761bd9567c93c8bcfb572c7b089d960f9f1bc0fe2b173e7937c2176
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.2
  resolution: "@humanwhocodes/retry@npm:0.4.2"
  checksum: 10c0/0235525d38f243bee3bf8b25ed395fbf957fb51c08adae52787e1325673071abe856c7e18e530922ed2dd3ce12ed82ba01b8cee0279ac52a3315fcdc3a69ef0c
  languageName: node
  linkType: hard

"@hutson/parse-repository-url@npm:^3.0.0":
  version: 3.0.2
  resolution: "@hutson/parse-repository-url@npm:3.0.2"
  checksum: 10c0/d9197757ecad2df18d29d3e1d1fe0716d458fd88b849c71cbec9e78239f911074c97e8d764dfd8ed890431c1137e52dd7a337207fd65be20ce0784f7860ae4d1
  languageName: node
  linkType: hard

"@inquirer/checkbox@npm:^4.1.2":
  version: 4.1.5
  resolution: "@inquirer/checkbox@npm:4.1.5"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/b984fb3ce8af34c327f3a85adcfe9fbd9eaac0c689bb9af79a5d55d508acb01de329747e8c923c9f4962e4006c353ed2dbe79e3fc9ae0f85f5851427dbed75ed
  languageName: node
  linkType: hard

"@inquirer/confirm@npm:5.1.6, @inquirer/confirm@npm:^5.1.6":
  version: 5.1.6
  resolution: "@inquirer/confirm@npm:5.1.6"
  dependencies:
    "@inquirer/core": "npm:^10.1.7"
    "@inquirer/type": "npm:^3.0.4"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/57b667f8096ec261504b613656e7b7718a238a73e059870a2b8e97c3127bc50085251100ed371250733b7cc5cd68122d8694d6a04a46de95d08bb590a8437b11
  languageName: node
  linkType: hard

"@inquirer/core@npm:^10.1.10, @inquirer/core@npm:^10.1.7":
  version: 10.1.10
  resolution: "@inquirer/core@npm:10.1.10"
  dependencies:
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    cli-width: "npm:^4.1.0"
    mute-stream: "npm:^2.0.0"
    signal-exit: "npm:^4.1.0"
    wrap-ansi: "npm:^6.2.0"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/8d0a3b725e42e40efbdc6ed087283795f1e36e642b119dd7dd3cbf31fce74bdbdb1b987da16159cd2475f45b2ede7e33293ae92bad3ac481832889c230df3fc0
  languageName: node
  linkType: hard

"@inquirer/editor@npm:^4.2.7":
  version: 4.2.10
  resolution: "@inquirer/editor@npm:4.2.10"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    external-editor: "npm:^3.1.0"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/b0213ad3ef45bc30427def4742db22126a1e6a59923033d21cae216276d8cf85d2af8abe432e5567ea24a7f6a31e23e7014e31308405cde684060b974e454a22
  languageName: node
  linkType: hard

"@inquirer/expand@npm:^4.0.9":
  version: 4.0.12
  resolution: "@inquirer/expand@npm:4.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/f7abfc09ef942b63504677be5cf6fc443fb8090b5d43f7d2fe09983215cc01c6d82351cd1b596e90723b382a0931c9344d3280d54acf47d898782f4af2030b2e
  languageName: node
  linkType: hard

"@inquirer/figures@npm:^1.0.11":
  version: 1.0.11
  resolution: "@inquirer/figures@npm:1.0.11"
  checksum: 10c0/6270e24eebbe42bbc4e7f8e761e906be66b4896787f31ab3e7484ad271c8edc90bce4ec20e232a5da447aee4fc73803397b2dda8cf645f4f7eea83e773b44e1e
  languageName: node
  linkType: hard

"@inquirer/input@npm:^4.1.6":
  version: 4.1.9
  resolution: "@inquirer/input@npm:4.1.9"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/db2e661ee482f3f27bf8cb77f054f99aba30291bd24d63b28db62204c4c5efc496199a9ddc03d01e0f6e6455d6967efb3ef92d2cd91e672905948c8c978c67a1
  languageName: node
  linkType: hard

"@inquirer/number@npm:^3.0.9":
  version: 3.0.12
  resolution: "@inquirer/number@npm:3.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/e40726e1c60ba48a374b4867d212bd5e14cb12daae97a6536095906246ba6af91ec7fa68e347ba52607ba5bd84f9e804768d12fbc1250b2cac814187fb5e9628
  languageName: node
  linkType: hard

"@inquirer/password@npm:^4.0.9":
  version: 4.0.12
  resolution: "@inquirer/password@npm:4.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/03257985bbbd813c4f0c412effb691737517e348ca2590558864fe09877080daf90eb9910a60d097048fce9cf0c56a900e8f099854a9ae21512ceaadbd986e01
  languageName: node
  linkType: hard

"@inquirer/prompts@npm:7.3.2":
  version: 7.3.2
  resolution: "@inquirer/prompts@npm:7.3.2"
  dependencies:
    "@inquirer/checkbox": "npm:^4.1.2"
    "@inquirer/confirm": "npm:^5.1.6"
    "@inquirer/editor": "npm:^4.2.7"
    "@inquirer/expand": "npm:^4.0.9"
    "@inquirer/input": "npm:^4.1.6"
    "@inquirer/number": "npm:^3.0.9"
    "@inquirer/password": "npm:^4.0.9"
    "@inquirer/rawlist": "npm:^4.0.9"
    "@inquirer/search": "npm:^3.0.9"
    "@inquirer/select": "npm:^4.0.9"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/a318d7c2a963f753f4868151f2ce5673e214f3a6597430e712bc59ef9605c831b71a6b52a9c5ea2f312b23063d2ee9fd633e127cdc9e4999e95ef15a5e90c7e1
  languageName: node
  linkType: hard

"@inquirer/rawlist@npm:^4.0.9":
  version: 4.0.12
  resolution: "@inquirer/rawlist@npm:4.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/1524d7257728532776334e457248aae4f9daf573428c526765440a33dcd3cc27fff039a2c3b75d3ced3b19328b9dc5f02356bf2ad32b59d34f8320a276ac91a4
  languageName: node
  linkType: hard

"@inquirer/search@npm:^3.0.9":
  version: 3.0.12
  resolution: "@inquirer/search@npm:3.0.12"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/ef764f96b561b48e4d9a99716789d1fc0941d40884d1c9fea715c304360b46ec8c6e3edf603f7425a27d7743915564f405a3ccd1a72f0379a714be22887fe6ff
  languageName: node
  linkType: hard

"@inquirer/select@npm:^4.0.9":
  version: 4.1.1
  resolution: "@inquirer/select@npm:4.1.1"
  dependencies:
    "@inquirer/core": "npm:^10.1.10"
    "@inquirer/figures": "npm:^1.0.11"
    "@inquirer/type": "npm:^3.0.6"
    ansi-escapes: "npm:^4.3.2"
    yoctocolors-cjs: "npm:^2.1.2"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/939a7eec9cd51fcd7ef775c26728f8741f9a4753e2a6f1ead951c8c541ecbe9e0124bd9e51f8b6651232325f6b9f1e54170b7237d16554b1845f1ccce959dcc7
  languageName: node
  linkType: hard

"@inquirer/type@npm:^1.5.5":
  version: 1.5.5
  resolution: "@inquirer/type@npm:1.5.5"
  dependencies:
    mute-stream: "npm:^1.0.0"
  checksum: 10c0/4c41736c09ba9426b5a9e44993bdd54e8f532e791518802e33866f233a2a6126a25c1c82c19d1abbf1df627e57b1b957dd3f8318ea96073d8bfc32193943bcb3
  languageName: node
  linkType: hard

"@inquirer/type@npm:^3.0.4, @inquirer/type@npm:^3.0.6":
  version: 3.0.6
  resolution: "@inquirer/type@npm:3.0.6"
  peerDependencies:
    "@types/node": ">=18"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/92382c1b046559ddb16c53e1353a900a43266566a0d73902e5325433c640b6aaeaf3e34cc5b2a68fd089ff5d8add914d0b9875cdec64f7a09313f9c4420b021d
  languageName: node
  linkType: hard

"@ionic/angular-toolkit@npm:^12.0.0":
  version: 12.2.0
  resolution: "@ionic/angular-toolkit@npm:12.2.0"
  dependencies:
    "@angular-devkit/core": "npm:^19.0.0"
    "@angular-devkit/schematics": "npm:^19.0.0"
    "@schematics/angular": "npm:^19.0.0"
  checksum: 10c0/d6dfdcde9c64ae36fe1d5b32fbf2630197321eb3e493b1d066d89829278c4778c52fc34a3624524893b5ca432de0e56194c41750ab145ffda9cd550ac0937457
  languageName: node
  linkType: hard

"@ionic/angular@npm:^8.0.0":
  version: 8.5.4
  resolution: "@ionic/angular@npm:8.5.4"
  dependencies:
    "@ionic/core": "npm:8.5.4"
    ionicons: "npm:^7.0.0"
    jsonc-parser: "npm:^3.0.0"
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/core": ">=16.0.0"
    "@angular/forms": ">=16.0.0"
    "@angular/router": ">=16.0.0"
    rxjs: ">=7.5.0"
    zone.js: ">=0.13.0"
  checksum: 10c0/df47c1abc62c222e783857e40c2dd44a9f87761e7203647868a27629feacf7b6575a8ee70ff0d57027cdcb73c64d8e2ee6eedc06343dacb21353ed948dd9e82d
  languageName: node
  linkType: hard

"@ionic/cli-framework-output@npm:^2.2.5, @ionic/cli-framework-output@npm:^2.2.8":
  version: 2.2.8
  resolution: "@ionic/cli-framework-output@npm:2.2.8"
  dependencies:
    "@ionic/utils-terminal": "npm:2.3.5"
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/a7bf9379201225f20dc3fe1ff93d8b811ef6f9437762785ca80d0c26bcb5aa6fe1661e9d8e1a41d6b193036d7c09a97215d6ec36f4692fa6ea860c1aebd5a97f
  languageName: node
  linkType: hard

"@ionic/core@npm:8.5.4":
  version: 8.5.4
  resolution: "@ionic/core@npm:8.5.4"
  dependencies:
    "@stencil/core": "npm:4.20.0"
    ionicons: "npm:^7.2.2"
    tslib: "npm:^2.1.0"
  checksum: 10c0/f6be2c7ef216db084e79013218473e666cc5702dea609dddab6ded03fdd3b53a2d9e50e87a96196608f0ab3aa95219f2c97386aa6afe0a3947647446edb39f2b
  languageName: node
  linkType: hard

"@ionic/storage-angular@npm:^4.0.0":
  version: 4.0.0
  resolution: "@ionic/storage-angular@npm:4.0.0"
  dependencies:
    "@ionic/storage": "npm:^4.0.0"
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/core": "*"
    rxjs: "*"
  checksum: 10c0/53803b1454f653408d52a634e5ccfd8eb827fd38b7b51c693702bf4926c7c301ac55a93dce203685925618c6acd2a107fc1efd47987b248696feb9d8ed3b31fe
  languageName: node
  linkType: hard

"@ionic/storage@npm:^4.0.0":
  version: 4.0.0
  resolution: "@ionic/storage@npm:4.0.0"
  dependencies:
    localforage: "npm:^1.9.0"
  checksum: 10c0/3ba7507e8c3cf0444f4a00b270d4e2c0e32b6570ddc770e44b05ed54bfbce3975e4aae5d7d0f9091b6f21273261e829c3f456a610a4ee361f1f376fd9e7ddbc0
  languageName: node
  linkType: hard

"@ionic/utils-array@npm:2.1.6":
  version: 2.1.6
  resolution: "@ionic/utils-array@npm:2.1.6"
  dependencies:
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/07b9a1c77b34e0c4d0e3bd1f638d7f7ede9481e8903328f4d1ecce20a96e92d71faed7f1c7572bcc7099d14f5c26d815c38b15a7c0679d71ee0ab40c3fde3d20
  languageName: node
  linkType: hard

"@ionic/utils-fs@npm:3.1.7, @ionic/utils-fs@npm:^3.1.5, @ionic/utils-fs@npm:^3.1.6, @ionic/utils-fs@npm:^3.1.7":
  version: 3.1.7
  resolution: "@ionic/utils-fs@npm:3.1.7"
  dependencies:
    "@types/fs-extra": "npm:^8.0.0"
    debug: "npm:^4.0.0"
    fs-extra: "npm:^9.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/e6d71395530ad7d60ee2a698ed5f3c1fc4df85083808a7847a639fc5dfd3cb701563e1652e7cc80b7915b19a62071138a8a40e47537dc750d1a262bdb43daf6e
  languageName: node
  linkType: hard

"@ionic/utils-object@npm:2.1.6":
  version: 2.1.6
  resolution: "@ionic/utils-object@npm:2.1.6"
  dependencies:
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/17b55abb65008d6be72db8cd4d3fffd2fdf103de11b38f48b65c9f6f8fe0501ff19bb7dd972014852b19dce79cf4029d0a905628e041894be10f4d2fd19f601f
  languageName: node
  linkType: hard

"@ionic/utils-process@npm:2.1.11":
  version: 2.1.11
  resolution: "@ionic/utils-process@npm:2.1.11"
  dependencies:
    "@ionic/utils-object": "npm:2.1.6"
    "@ionic/utils-terminal": "npm:2.3.4"
    debug: "npm:^4.0.0"
    signal-exit: "npm:^3.0.3"
    tree-kill: "npm:^1.2.2"
    tslib: "npm:^2.0.1"
  checksum: 10c0/21cb8fc2326ae15e177d5878838e62d33c979521b8c12fa89a2b39ae89a48cd32e951619baf5d5f50ac8efa3253e17265b7eaf00acef24d8672fff906ec4eb1d
  languageName: node
  linkType: hard

"@ionic/utils-process@npm:2.1.12":
  version: 2.1.12
  resolution: "@ionic/utils-process@npm:2.1.12"
  dependencies:
    "@ionic/utils-object": "npm:2.1.6"
    "@ionic/utils-terminal": "npm:2.3.5"
    debug: "npm:^4.0.0"
    signal-exit: "npm:^3.0.3"
    tree-kill: "npm:^1.2.2"
    tslib: "npm:^2.0.1"
  checksum: 10c0/0ee9c9810080fcf77dd7bed457f65dd074009d799b7c4efa147f3c53049993d08c73409653bc402fa9c7bf67b691d3c8cce7cd6836db4fdb75334e179ae325fa
  languageName: node
  linkType: hard

"@ionic/utils-stream@npm:3.1.6":
  version: 3.1.6
  resolution: "@ionic/utils-stream@npm:3.1.6"
  dependencies:
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/76e45603b0221dbda54963f42093f149e2bf141d9428b75446c3b3dcdf1d214d62c10118a684e840973a56eaf82f55590a90d2c73da5323dc5e28da5d6b5aed5
  languageName: node
  linkType: hard

"@ionic/utils-stream@npm:3.1.7":
  version: 3.1.7
  resolution: "@ionic/utils-stream@npm:3.1.7"
  dependencies:
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/738715334237cbafe4c43f3620ffd6ccbef3a6148958d6d4642dba784acb55223c4c059d7593edddcc72fcab7b5fe7c5c51b74cfd54521dd91ff1a9de7d5d121
  languageName: node
  linkType: hard

"@ionic/utils-subprocess@npm:^2.1.11, @ionic/utils-subprocess@npm:^2.1.8":
  version: 2.1.14
  resolution: "@ionic/utils-subprocess@npm:2.1.14"
  dependencies:
    "@ionic/utils-array": "npm:2.1.6"
    "@ionic/utils-fs": "npm:3.1.7"
    "@ionic/utils-process": "npm:2.1.11"
    "@ionic/utils-stream": "npm:3.1.6"
    "@ionic/utils-terminal": "npm:2.3.4"
    cross-spawn: "npm:^7.0.3"
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/4533d220df43d5e55d8e717f42986ebbd667ea7dbe13b4a72a3647ff855ad1bf7b2fd63ccaaf0fdd8a1cb0604a66dff6b94467019f60fe2304ca7c5b2a8b2eb1
  languageName: node
  linkType: hard

"@ionic/utils-subprocess@npm:^3.0.1":
  version: 3.0.1
  resolution: "@ionic/utils-subprocess@npm:3.0.1"
  dependencies:
    "@ionic/utils-array": "npm:2.1.6"
    "@ionic/utils-fs": "npm:3.1.7"
    "@ionic/utils-process": "npm:2.1.12"
    "@ionic/utils-stream": "npm:3.1.7"
    "@ionic/utils-terminal": "npm:2.3.5"
    cross-spawn: "npm:^7.0.3"
    debug: "npm:^4.0.0"
    tslib: "npm:^2.0.1"
  checksum: 10c0/b4f4b02c3ebd7dcc587c5db077ddade5d938f7693fc7aaba7e5356404f272dac8c9e032adfb496115ab3ca5ec7ec37949ce7925394f4a7574db81f30e3b27eed
  languageName: node
  linkType: hard

"@ionic/utils-terminal@npm:2.3.4, @ionic/utils-terminal@npm:^2.3.3":
  version: 2.3.4
  resolution: "@ionic/utils-terminal@npm:2.3.4"
  dependencies:
    "@types/slice-ansi": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    signal-exit: "npm:^3.0.3"
    slice-ansi: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    tslib: "npm:^2.0.1"
    untildify: "npm:^4.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/2d7c80f244f5332fe646c3e053544328df3dbdc0164d2b3f8f4860cdae10eb1bc5b7d3d394d39b209def8fc961f5ba6ac1a38899e905936d25f0bf00ec05eba5
  languageName: node
  linkType: hard

"@ionic/utils-terminal@npm:2.3.5, @ionic/utils-terminal@npm:^2.3.4, @ionic/utils-terminal@npm:^2.3.5":
  version: 2.3.5
  resolution: "@ionic/utils-terminal@npm:2.3.5"
  dependencies:
    "@types/slice-ansi": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    signal-exit: "npm:^3.0.3"
    slice-ansi: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    tslib: "npm:^2.0.1"
    untildify: "npm:^4.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/a3fdd68f6913250f49a302e03f4c93c2abafed3064f982c10b69a2e05439df208d09cc25dfec062cbfd741d22ebf5473f72831aeba96e246f8e3d82d6caaa03a
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.0.3"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
  checksum: 10c0/fa425b606d7c7ee5bfa6a31a7b050dd5814b4082f318e0e4190f991902181b4330f43f4805db1dd4f2433fd0ed9cc7a7b9c2683f1deeab1df1b0a98b1e24055b
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@jsonjoy.com/base64@npm:^1.1.1":
  version: 1.1.2
  resolution: "@jsonjoy.com/base64@npm:1.1.2"
  peerDependencies:
    tslib: 2
  checksum: 10c0/88717945f66dc89bf58ce75624c99fe6a5c9a0c8614e26d03e406447b28abff80c69fb37dabe5aafef1862cf315071ae66e5c85f6018b437d95f8d13d235e6eb
  languageName: node
  linkType: hard

"@jsonjoy.com/json-pack@npm:^1.0.3":
  version: 1.2.0
  resolution: "@jsonjoy.com/json-pack@npm:1.2.0"
  dependencies:
    "@jsonjoy.com/base64": "npm:^1.1.1"
    "@jsonjoy.com/util": "npm:^1.1.2"
    hyperdyperid: "npm:^1.2.0"
    thingies: "npm:^1.20.0"
  peerDependencies:
    tslib: 2
  checksum: 10c0/0744cfe2f54d896003ad240f0f069b41a152feb53b6134c5e65961126b9e5fdfc74a46f63b1dfa280e80a3d176c57e06de072bf03d749ec1982e41677a1ce5d5
  languageName: node
  linkType: hard

"@jsonjoy.com/util@npm:^1.1.2, @jsonjoy.com/util@npm:^1.3.0":
  version: 1.5.0
  resolution: "@jsonjoy.com/util@npm:1.5.0"
  peerDependencies:
    tslib: 2
  checksum: 10c0/0065ae12c4108d8aede01a479c8d2b5a39bce99e9a449d235befc753f57e8385d9c1115720529f26597840b7398d512898155423d9859fd638319fb0c827365d
  languageName: node
  linkType: hard

"@leichtgewicht/ip-codec@npm:^2.0.1":
  version: 2.0.5
  resolution: "@leichtgewicht/ip-codec@npm:2.0.5"
  checksum: 10c0/14a0112bd59615eef9e3446fea018045720cd3da85a98f801a685a818b0d96ef2a1f7227e8d271def546b2e2a0fe91ef915ba9dc912ab7967d2317b1a051d66b
  languageName: node
  linkType: hard

"@listr2/prompt-adapter-inquirer@npm:2.0.18":
  version: 2.0.18
  resolution: "@listr2/prompt-adapter-inquirer@npm:2.0.18"
  dependencies:
    "@inquirer/type": "npm:^1.5.5"
  peerDependencies:
    "@inquirer/prompts": ">= 3 < 8"
  checksum: 10c0/580d2f0ae414cf3090c2fbfe4623649e448d930b3ff24b0211e64e0e037f1a3ffff5307bc36c10cdc0c4a35fc12f04190585e864c4ce05fbf5f062b41ff29e40
  languageName: node
  linkType: hard

"@lmdb/lmdb-darwin-arm64@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-darwin-arm64@npm:3.2.6"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@lmdb/lmdb-darwin-x64@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-darwin-x64@npm:3.2.6"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@lmdb/lmdb-linux-arm64@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-linux-arm64@npm:3.2.6"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@lmdb/lmdb-linux-arm@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-linux-arm@npm:3.2.6"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@lmdb/lmdb-linux-x64@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-linux-x64@npm:3.2.6"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@lmdb/lmdb-win32-x64@npm:3.2.6":
  version: 3.2.6
  resolution: "@lmdb/lmdb-win32-x64@npm:3.2.6"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/nice-android-arm-eabi@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-android-arm-eabi@npm:1.0.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/nice-android-arm64@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-android-arm64@npm:1.0.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/nice-darwin-arm64@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-darwin-arm64@npm:1.0.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/nice-darwin-x64@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-darwin-x64@npm:1.0.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/nice-freebsd-x64@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-freebsd-x64@npm:1.0.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-arm-gnueabihf@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-arm-gnueabihf@npm:1.0.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-arm64-gnu@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-arm64-gnu@npm:1.0.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-arm64-musl@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-arm64-musl@npm:1.0.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-ppc64-gnu@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-ppc64-gnu@npm:1.0.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-riscv64-gnu@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-riscv64-gnu@npm:1.0.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-s390x-gnu@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-s390x-gnu@npm:1.0.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-x64-gnu@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-x64-gnu@npm:1.0.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/nice-linux-x64-musl@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-linux-x64-musl@npm:1.0.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/nice-win32-arm64-msvc@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-win32-arm64-msvc@npm:1.0.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/nice-win32-ia32-msvc@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-win32-ia32-msvc@npm:1.0.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@napi-rs/nice-win32-x64-msvc@npm:1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice-win32-x64-msvc@npm:1.0.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/nice@npm:^1.0.1":
  version: 1.0.1
  resolution: "@napi-rs/nice@npm:1.0.1"
  dependencies:
    "@napi-rs/nice-android-arm-eabi": "npm:1.0.1"
    "@napi-rs/nice-android-arm64": "npm:1.0.1"
    "@napi-rs/nice-darwin-arm64": "npm:1.0.1"
    "@napi-rs/nice-darwin-x64": "npm:1.0.1"
    "@napi-rs/nice-freebsd-x64": "npm:1.0.1"
    "@napi-rs/nice-linux-arm-gnueabihf": "npm:1.0.1"
    "@napi-rs/nice-linux-arm64-gnu": "npm:1.0.1"
    "@napi-rs/nice-linux-arm64-musl": "npm:1.0.1"
    "@napi-rs/nice-linux-ppc64-gnu": "npm:1.0.1"
    "@napi-rs/nice-linux-riscv64-gnu": "npm:1.0.1"
    "@napi-rs/nice-linux-s390x-gnu": "npm:1.0.1"
    "@napi-rs/nice-linux-x64-gnu": "npm:1.0.1"
    "@napi-rs/nice-linux-x64-musl": "npm:1.0.1"
    "@napi-rs/nice-win32-arm64-msvc": "npm:1.0.1"
    "@napi-rs/nice-win32-ia32-msvc": "npm:1.0.1"
    "@napi-rs/nice-win32-x64-msvc": "npm:1.0.1"
  dependenciesMeta:
    "@napi-rs/nice-android-arm-eabi":
      optional: true
    "@napi-rs/nice-android-arm64":
      optional: true
    "@napi-rs/nice-darwin-arm64":
      optional: true
    "@napi-rs/nice-darwin-x64":
      optional: true
    "@napi-rs/nice-freebsd-x64":
      optional: true
    "@napi-rs/nice-linux-arm-gnueabihf":
      optional: true
    "@napi-rs/nice-linux-arm64-gnu":
      optional: true
    "@napi-rs/nice-linux-arm64-musl":
      optional: true
    "@napi-rs/nice-linux-ppc64-gnu":
      optional: true
    "@napi-rs/nice-linux-riscv64-gnu":
      optional: true
    "@napi-rs/nice-linux-s390x-gnu":
      optional: true
    "@napi-rs/nice-linux-x64-gnu":
      optional: true
    "@napi-rs/nice-linux-x64-musl":
      optional: true
    "@napi-rs/nice-win32-arm64-msvc":
      optional: true
    "@napi-rs/nice-win32-ia32-msvc":
      optional: true
    "@napi-rs/nice-win32-x64-msvc":
      optional: true
  checksum: 10c0/9be30f8292e23f45f5b8f6553411f5cbaead998cc3a51859c60f56fc2e679610a3a04ed49b748267552b9abd17fe5e6ae88186e223ab5cb93d5d184d10b6569b
  languageName: node
  linkType: hard

"@ngtools/webpack@npm:19.2.7":
  version: 19.2.7
  resolution: "@ngtools/webpack@npm:19.2.7"
  peerDependencies:
    "@angular/compiler-cli": ^19.0.0 || ^19.2.0-next.0
    typescript: ">=5.5 <5.9"
    webpack: ^5.54.0
  checksum: 10c0/5399bfed24691fa97b72282a0fc0cfb4812c7ea3ece58de80c7ea82accb83ed725b58e93a634a0d79a0449bdd3583f974be2b96eebf02b6211603ec86565df1a
  languageName: node
  linkType: hard

"@ngx-translate/core@npm:^16.0.4":
  version: 16.0.4
  resolution: "@ngx-translate/core@npm:16.0.4"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": ">=16"
    "@angular/core": ">=16"
  checksum: 10c0/b84839745d04f9f8a0964cc1eeadb54ff77508675dd1e42f2b9b6dd6ccca91177c7905ac2909de90807cd3883ae1967c293b3313592cb76ba566a6c233313e3b
  languageName: node
  linkType: hard

"@ngx-translate/http-loader@npm:^16.0.1":
  version: 16.0.1
  resolution: "@ngx-translate/http-loader@npm:16.0.1"
  dependencies:
    tslib: "npm:^2.3.0"
  peerDependencies:
    "@angular/common": ">=16"
    "@angular/core": ">=16"
  checksum: 10c0/8d0543310d24f62efcca73a00de5568e2b48ba405b1536ca74906887c4cce3e4749c1f0793a2ae5655443880af4550f39b008349dd63a184919d0ea8c6d970c1
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.1.5":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: 10c0/06a0b52c81a6fa7f04d67762e08b2c476a00285858150caeaaff4037356dd5e119f45b2a530f638b77a5eeca013168ec1b655db41bae3236cb2e9d511484fc77
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@npmcli/git@npm:^6.0.0":
  version: 6.0.3
  resolution: "@npmcli/git@npm:6.0.3"
  dependencies:
    "@npmcli/promise-spawn": "npm:^8.0.0"
    ini: "npm:^5.0.0"
    lru-cache: "npm:^10.0.1"
    npm-pick-manifest: "npm:^10.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    semver: "npm:^7.3.5"
    which: "npm:^5.0.0"
  checksum: 10c0/a8ff1d5f997f7bfdc149fbe7478017b100efe3d08bd566df6b5ac716fd630d2eff0f7feebc6705831a3a7072a67a955a339a8fea8551ce4faffafa9526306e05
  languageName: node
  linkType: hard

"@npmcli/installed-package-contents@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/installed-package-contents@npm:3.0.0"
  dependencies:
    npm-bundled: "npm:^4.0.0"
    npm-normalize-package-bin: "npm:^4.0.0"
  bin:
    installed-package-contents: bin/index.js
  checksum: 10c0/8bb361251cd13b91ae2d04bfcc59b52ffb8cd475d074259c143b3c29a0c4c0ae90d76cfb2cab00ff61cc76bd0c38591b530ce1bdbbc8a61d60ddc6c9ecbf169b
  languageName: node
  linkType: hard

"@npmcli/node-gyp@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/node-gyp@npm:4.0.0"
  checksum: 10c0/58422c2ce0693f519135dd32b5c5bcbb441823f08f9294d5ec19d9a22925ba1a5ec04a1b96f606f2ab09a5f5db56e704f6e201a485198ce9d11fb6b2705e6e79
  languageName: node
  linkType: hard

"@npmcli/package-json@npm:^6.0.0":
  version: 6.1.1
  resolution: "@npmcli/package-json@npm:6.1.1"
  dependencies:
    "@npmcli/git": "npm:^6.0.0"
    glob: "npm:^10.2.2"
    hosted-git-info: "npm:^8.0.0"
    json-parse-even-better-errors: "npm:^4.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.5.3"
    validate-npm-package-license: "npm:^3.0.4"
  checksum: 10c0/ec24e6dc4ade64de205f7964e325de31ee0c2dd7d70c2321a2b822481bf97505fffbe23ec05672fc61862ae68bbae3e9b44d4640439081dbc8f8b2dcdeda2aa3
  languageName: node
  linkType: hard

"@npmcli/promise-spawn@npm:^8.0.0":
  version: 8.0.2
  resolution: "@npmcli/promise-spawn@npm:8.0.2"
  dependencies:
    which: "npm:^5.0.0"
  checksum: 10c0/fe987dece7b843d9353d4d38982336ab3beabc2dd3c135862a4ba2921aae55b0d334891fe44c6cbbee20626259e54478bf498ad8d380c14c53732b489ae14f40
  languageName: node
  linkType: hard

"@npmcli/redact@npm:^3.0.0":
  version: 3.1.1
  resolution: "@npmcli/redact@npm:3.1.1"
  checksum: 10c0/0653d1acbad34c78a372ac581429c3a257849bc761322cad088a202dc6639f5a61912cae0a678e2f53fdf5a4b6e9353cc2d528155d3cad2c7d5e89486aed081c
  languageName: node
  linkType: hard

"@npmcli/run-script@npm:^9.0.0":
  version: 9.1.0
  resolution: "@npmcli/run-script@npm:9.1.0"
  dependencies:
    "@npmcli/node-gyp": "npm:^4.0.0"
    "@npmcli/package-json": "npm:^6.0.0"
    "@npmcli/promise-spawn": "npm:^8.0.0"
    node-gyp: "npm:^11.0.0"
    proc-log: "npm:^5.0.0"
    which: "npm:^5.0.0"
  checksum: 10c0/4ed8eae5c7722c24814473f819d0bfe950f70e876bf9c52e05a61d3e74f2a044386da95e2e171e5a7a81e4c0b144582535addf2510e5decfd7d4aa7ae9e50931
  languageName: node
  linkType: hard

"@paralleldrive/cuid2@npm:^2.2.2":
  version: 2.2.2
  resolution: "@paralleldrive/cuid2@npm:2.2.2"
  dependencies:
    "@noble/hashes": "npm:^1.1.5"
  checksum: 10c0/af5826df93de437121308f4f4ce0b2eeb89b60bb57a1a6592fb89c0d40d311ad1d9f3f6a4db2cce6f2bcf572de1aa3f85704254e89b18ce61c41ebb06564c4ee
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-android-arm64@npm:2.5.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-x64@npm:2.5.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.1
  resolution: "@parcel/watcher@npm:2.5.1"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-x64": "npm:2.5.1"
    "@parcel/watcher-freebsd-x64": "npm:2.5.1"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.1"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.1"
    "@parcel/watcher-win32-arm64": "npm:2.5.1"
    "@parcel/watcher-win32-ia32": "npm:2.5.1"
    "@parcel/watcher-win32-x64": "npm:2.5.1"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10c0/8f35073d0c0b34a63d4c8d2213482f0ebc6a25de7b2cdd415d19cb929964a793cb285b68d1d50bfb732b070b3c82a2fdb4eb9c250eab709a1cd9d63345455a82
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.2
  resolution: "@pkgr/core@npm:0.1.2"
  checksum: 10c0/fd4acc154c8f1b5c544b6dd152b7ce68f6cbb8b92e9abf2e5d756d6e95052d08d0d693a668dea67af1386d62635b50adfe463cce03c5620402b468498cc7592f
  languageName: node
  linkType: hard

"@prettier/plugin-xml@npm:^2.2.0":
  version: 2.2.0
  resolution: "@prettier/plugin-xml@npm:2.2.0"
  dependencies:
    "@xml-tools/parser": "npm:^1.0.11"
    prettier: "npm:>=2.4.0"
  checksum: 10c0/a76c791aced51a406fad0cf6ab10cb119443d0d426d7a52be6d718513737d576d2a31adf9b7b07fbc6431c37fecce02b166e85fd781ca05ee5555550a8769b19
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.34.8"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-android-arm64@npm:4.34.8"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-darwin-arm64@npm:4.34.8"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-darwin-x64@npm:4.34.8"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.34.8"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-freebsd-x64@npm:4.34.8"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.34.8"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.34.8"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.34.8"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.34.8"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.34.8"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.34.8"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.34.8"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.34.8"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.34.8"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.34.8"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.34.8"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.34.8"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.34.8":
  version: 4.34.8
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.34.8"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 10c0/b5bcfb0d87f7d1c1c7c0f7693f53b07866ed9fec4c34a97a8c948fb9a7c0082e416ce4d3b60beb4f5e167cbe04cdeefbf6771320f3ede059b9ce91188c409a5b
  languageName: node
  linkType: hard

"@schematics/angular@npm:19.2.7, @schematics/angular@npm:^19.0.0":
  version: 19.2.7
  resolution: "@schematics/angular@npm:19.2.7"
  dependencies:
    "@angular-devkit/core": "npm:19.2.7"
    "@angular-devkit/schematics": "npm:19.2.7"
    jsonc-parser: "npm:3.3.1"
  checksum: 10c0/67a520059b82f28db20b13e522318a3363c07281c88f94e9e201b232ab9c58ff316ac572e9efd4462197ca9f477f491f3fed7037b13d77285944a48bee9c2077
  languageName: node
  linkType: hard

"@sigstore/bundle@npm:^3.1.0":
  version: 3.1.0
  resolution: "@sigstore/bundle@npm:3.1.0"
  dependencies:
    "@sigstore/protobuf-specs": "npm:^0.4.0"
  checksum: 10c0/f34afa3efe81b0925cf1568eeea7678876c5889799fcdf9b81d1062067108e74fc3f3480b0d2b7daa7389f944e4a2523b5fc98d65dbbaa34d206d8c2edc4fa5a
  languageName: node
  linkType: hard

"@sigstore/core@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sigstore/core@npm:2.0.0"
  checksum: 10c0/bb7e668aedcda68312d2ff7c986fd0ba29057ca4dfbaef516c997b0799cd8858b2fc8017a7946fd2e43f237920adbcaa7455097a0a02909ed86cad9f98d592d4
  languageName: node
  linkType: hard

"@sigstore/protobuf-specs@npm:^0.4.0":
  version: 0.4.1
  resolution: "@sigstore/protobuf-specs@npm:0.4.1"
  checksum: 10c0/8de75bc84f307b7d7bf4fa265f89a8bfa717d96e091be29ee9be1ffd6036b81355a349955e5036b86570d37f2b30b18a68a43f5e40bd958bd4128aa6bb39a3f3
  languageName: node
  linkType: hard

"@sigstore/sign@npm:^3.1.0":
  version: 3.1.0
  resolution: "@sigstore/sign@npm:3.1.0"
  dependencies:
    "@sigstore/bundle": "npm:^3.1.0"
    "@sigstore/core": "npm:^2.0.0"
    "@sigstore/protobuf-specs": "npm:^0.4.0"
    make-fetch-happen: "npm:^14.0.2"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
  checksum: 10c0/7647f3a1350a09d66e7d77fdf8edf6eeb047f818acc2cd06325fc8ec9f0cd654dd25909876147b7ed052d459dc6a1d64e8cbaa44486300b241c3b139d778f254
  languageName: node
  linkType: hard

"@sigstore/tuf@npm:^3.1.0":
  version: 3.1.0
  resolution: "@sigstore/tuf@npm:3.1.0"
  dependencies:
    "@sigstore/protobuf-specs": "npm:^0.4.0"
    tuf-js: "npm:^3.0.1"
  checksum: 10c0/940237295bec3817ef4dbfd48de8b9a73b4e297966c05e81b6103747904def999f27499adb3de572407f2c72c6f28d2c699a6c8446be808b599c427a9903f081
  languageName: node
  linkType: hard

"@sigstore/verify@npm:^2.1.0":
  version: 2.1.0
  resolution: "@sigstore/verify@npm:2.1.0"
  dependencies:
    "@sigstore/bundle": "npm:^3.1.0"
    "@sigstore/core": "npm:^2.0.0"
    "@sigstore/protobuf-specs": "npm:^0.4.0"
  checksum: 10c0/3e4f575c25d352ce4953e1fedd220c481199e8a704c5906b1cea933945020dc77bdc25090a0957ef15f01d2d475bd4bc3dbc1edf8acbb2f3a3448befdb2eca7e
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^2.1.0":
  version: 2.3.0
  resolution: "@sindresorhus/merge-streams@npm:2.3.0"
  checksum: 10c0/69ee906f3125fb2c6bb6ec5cdd84e8827d93b49b3892bce8b62267116cc7e197b5cccf20c160a1d32c26014ecd14470a72a5e3ee37a58f1d6dadc0db1ccf3894
  languageName: node
  linkType: hard

"@socket.io/component-emitter@npm:~3.1.0":
  version: 3.1.2
  resolution: "@socket.io/component-emitter@npm:3.1.2"
  checksum: 10c0/c4242bad66f67e6f7b712733d25b43cbb9e19a595c8701c3ad99cbeb5901555f78b095e24852f862fffb43e96f1d8552e62def885ca82ae1bb05da3668fd87d7
  languageName: node
  linkType: hard

"@splidejs/splide@npm:^4.1.4":
  version: 4.1.4
  resolution: "@splidejs/splide@npm:4.1.4"
  checksum: 10c0/2dc36e0ce13d049b6ff6e2dcd02cf100ad5df014cd7e751e34f941d54f73a9af948b0912f977aaf896a05ac4f0388fc0a2404cf79d970ac632d207f9dbe03044
  languageName: node
  linkType: hard

"@stencil/core@npm:4.20.0, @stencil/core@npm:^4.0.3":
  version: 4.20.0
  resolution: "@stencil/core@npm:4.20.0"
  bin:
    stencil: bin/stencil
  checksum: 10c0/5757cf6f2cf7028b335e52ff6f1eb4c75920d8e07a7dccc74c57d27ed2bf1a0ba77af832a7d4fc86cfc75bda42f4090c875535a83b743f6c2fb781efa3bac2ea
  languageName: node
  linkType: hard

"@trapezedev/gradle-parse@npm:7.1.3":
  version: 7.1.3
  resolution: "@trapezedev/gradle-parse@npm:7.1.3"
  checksum: 10c0/78c6d3a3666f4bfa3ba1a6e007b274557c2503839114f293ee39a071b03b71a9dc3f622d4f2eb3682a2a6801ebc31553b7a85cbaed3bfa42c59d136023a64c25
  languageName: node
  linkType: hard

"@trapezedev/project@npm:^7.0.10":
  version: 7.1.3
  resolution: "@trapezedev/project@npm:7.1.3"
  dependencies:
    "@ionic/utils-fs": "npm:^3.1.5"
    "@ionic/utils-subprocess": "npm:^2.1.8"
    "@prettier/plugin-xml": "npm:^2.2.0"
    "@trapezedev/gradle-parse": "npm:7.1.3"
    "@xmldom/xmldom": "npm:^0.7.5"
    conventional-changelog: "npm:^3.1.4"
    cross-spawn: "npm:^7.0.3"
    diff: "npm:^5.1.0"
    env-paths: "npm:^3.0.0"
    gradle-to-js: "npm:^2.0.0"
    ini: "npm:^2.0.0"
    kleur: "npm:^4.1.5"
    lodash: "npm:^4.17.21"
    mergexml: "npm:^1.2.3"
    plist: "npm:^3.0.4"
    prettier: "npm:^2.7.1"
    prompts: "npm:^2.4.2"
    replace: "npm:^1.1.0"
    tempy: "npm:^1.0.1"
    tmp: "npm:^0.2.1"
    ts-node: "npm:^10.2.1"
    xcode: "npm:^3.0.1"
    xml-js: "npm:^1.6.11"
    xpath: "npm:^0.0.32"
    yargs: "npm:^17.2.1"
  checksum: 10c0/a0d6cfddf9b728f99b9eb29ca535df96d169e03f171c3fe6769a9ce1c1413ecc620fa9ff27548bacf45c125f95ca70d3a7a081b84207a58e3cb0916f26e4bcb6
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 10c0/28a0710e5d039e0de484bdf85fee883bfd3f6a8980601f4d44066b0a6bcd821d31c4e231d1117731c4e24268bd4cf2a788a6787c12fc7f8d11014c07d582783c
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 10c0/dddca2b553e2bee1308a056705103fc8304e42bb2d2cbd797b84403a223b25c78f2c683ec3e24a095e82cd435387c877239bffcb15a590ba817cd3f6b9a99fd9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 10c0/67c1316d065fdaa32525bc9449ff82c197c4c19092b9663b23213c8cbbf8d88b6ed6a17898e0cbc2711950fbfaf40388938c1c748a2ee89f7234fc9e7fe2bf44
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 10c0/05f8f2734e266fb1839eb1d57290df1664fe2aa3b0fdd685a9035806daa635f7519bf6d5d9b33f6e69dd545b8c46bd6e2b5c79acb2b1f146e885f7f11a42a5bb
  languageName: node
  linkType: hard

"@tufjs/canonical-json@npm:2.0.0":
  version: 2.0.0
  resolution: "@tufjs/canonical-json@npm:2.0.0"
  checksum: 10c0/52c5ffaef1483ed5c3feedfeba26ca9142fa386eea54464e70ff515bd01c5e04eab05d01eff8c2593291dcaf2397ca7d9c512720e11f52072b04c47a5c279415
  languageName: node
  linkType: hard

"@tufjs/models@npm:3.0.1":
  version: 3.0.1
  resolution: "@tufjs/models@npm:3.0.1"
  dependencies:
    "@tufjs/canonical-json": "npm:2.0.0"
    minimatch: "npm:^9.0.5"
  checksum: 10c0/0b2022589139102edf28f7fdcd094407fc98ac25bf530ebcf538dd63152baea9b6144b713c8dfc4f6b7580adeff706ab6ecc5f9716c4b816e58a04419abb1926
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/aebeb200f25e8818d8cf39cd0209026750d77c9b85381cdd8deeb50913e4d18a1ebe4b74ca9b0b4d21952511eeaba5e9fbbf739b52731a2061e206ec60d568df
  languageName: node
  linkType: hard

"@types/bonjour@npm:^3.5.13":
  version: 3.5.13
  resolution: "@types/bonjour@npm:3.5.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/eebedbca185ac3c39dd5992ef18d9e2a9f99e7f3c2f52f5561f90e9ed482c5d224c7962db95362712f580ed5713264e777a98d8f0bd8747f4eadf62937baed16
  languageName: node
  linkType: hard

"@types/connect-history-api-fallback@npm:^1.5.4":
  version: 1.5.4
  resolution: "@types/connect-history-api-fallback@npm:1.5.4"
  dependencies:
    "@types/express-serve-static-core": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/1b4035b627dcd714b05a22557f942e24a57ca48e7377dde0d2f86313fe685bc0a6566512a73257a55b5665b96c3041fb29228ac93331d8133011716215de8244
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/cors@npm:^2.8.12":
  version: 2.8.17
  resolution: "@types/cors@npm:2.8.17"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/457364c28c89f3d9ed34800e1de5c6eaaf344d1bb39af122f013322a50bc606eb2aa6f63de4e41a7a08ba7ef454473926c94a830636723da45bf786df032696d
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.7":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "npm:*"
    "@types/estree": "npm:*"
  checksum: 10c0/a0ecbdf2f03912679440550817ff77ef39a30fa8bfdacaf6372b88b1f931828aec392f52283240f0d648cf3055c5ddc564544a626bcf245f3d09fcb099ebe3cc
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "npm:*"
    "@types/json-schema": "npm:*"
  checksum: 10c0/69ba24fee600d1e4c5abe0df086c1a4d798abf13792d8cfab912d76817fe1a894359a1518557d21237fbaf6eda93c5ab9309143dee4c59ef54336d1b3570420e
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10c0/be815254316882f7c40847336cd484c3bc1c3e34f710d197160d455dc9d6d050ffbf4c3bc76585dba86f737f020ab20bdb137ebe0e9116b0c86c7c0342221b8c
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10c0/cdfd751f6f9065442cd40957c07fd80361c962869aa853c1c2fd03e101af8b9389d8ff4955a43a6fcfa223dd387a089937f95be0f3eec21ca527039fd2d9859a
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:*":
  version: 5.0.6
  resolution: "@types/express-serve-static-core@npm:5.0.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/aced8cc88c1718adbbd1fc488756b0f22d763368d9eff2ae21b350698fab4a77d8d13c3699056dc662a887e43a8b67a3e8f6289ff76102ecc6bad4a7710d31a6
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/4281f4ead71723f376b3ddf64868ae26244d434d9906c101cf8d436d4b5c779d01bd046e4ea0ed1a394d3e402216fabfa22b1fa4dba501061cd7c81c54045983
  languageName: node
  linkType: hard

"@types/express@npm:*, @types/express@npm:^4.17.21":
  version: 4.17.21
  resolution: "@types/express@npm:4.17.21"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10c0/12e562c4571da50c7d239e117e688dc434db1bac8be55613294762f84fd77fbd0658ccd553c7d3ab02408f385bc93980992369dd30e2ecd2c68c358e6af8fabf
  languageName: node
  linkType: hard

"@types/fs-extra@npm:^8.0.0":
  version: 8.1.5
  resolution: "@types/fs-extra@npm:8.1.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/c9f7965bc499a6cc1cadb37a4e9002c0f33810867a0a47a132c4165cbe3b49c6ea52e26c3c38f07720540dd5c470619254c0ef00a2e14a8bf4971ec5d478ba69
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.4
  resolution: "@types/http-errors@npm:2.0.4"
  checksum: 10c0/494670a57ad4062fee6c575047ad5782506dd35a6b9ed3894cea65830a94367bd84ba302eb3dde331871f6d70ca287bfedb1b2cf658e6132cd2cbd427ab56836
  languageName: node
  linkType: hard

"@types/http-proxy@npm:^1.17.15, @types/http-proxy@npm:^1.17.8":
  version: 1.17.16
  resolution: "@types/http-proxy@npm:1.17.16"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/b71bbb7233b17604f1158bbbe33ebf8bb870179d2b6e15dc9483aa2a785ce0d19ffb6c2237225b558addf24211d1853c95e337ee496df058eb175b433418a941
  languageName: node
  linkType: hard

"@types/jasmine@npm:~5.1.0":
  version: 5.1.7
  resolution: "@types/jasmine@npm:5.1.7"
  checksum: 10c0/f8df11fb8c8dce20088bfbaf5f805878b1228b4522f6ef100ea539e2f363537304095200ea8e856a0795e45d18803970d12683bc5afa35ccb475cc8ec5ea6d54
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 10c0/6bf5337bc447b706bb5b4431d37686aa2ea6d07cfd6f79cc31de80170d6ff9b1c7384a9c0ccbc45b3f512bae9e9f75c2e12109806a15331dc94e8a8db6dbb4ac
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10c0/c2ee31cd9b993804df33a694d5aa3fa536511a49f2e06eeab0b484fef59b4483777dbb9e42a4198a0809ffbf698081fdbca1e5c2218b82b91603dfab10a10fbc
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.0":
  version: 1.2.5
  resolution: "@types/minimist@npm:1.2.5"
  checksum: 10c0/3f791258d8e99a1d7d0ca2bda1ca6ea5a94e5e7b8fc6cde84dd79b0552da6fb68ade750f0e17718f6587783c24254bbca0357648dd59dc3812c150305cabdc46
  languageName: node
  linkType: hard

"@types/node-forge@npm:^1.3.0":
  version: 1.3.11
  resolution: "@types/node-forge@npm:1.3.11"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/3d7d23ca0ba38ac0cf74028393bd70f31169ab9aba43f21deb787840170d307d662644bac07287495effe2812ddd7ac8a14dbd43f16c2936bbb06312e96fc3b9
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=10.0.0":
  version: 22.14.1
  resolution: "@types/node@npm:22.14.1"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/d49c4d00403b1c2348cf0701b505fd636d80aabe18102105998dc62fdd36dcaf911e73c7a868c48c21c1022b825c67b475b65b1222d84b704d8244d152bb7f86
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: 10c0/aef7bb9b015883d6f4119c423dd28c4bdc17b0e8a0ccf112c78b4fe0e91fbc4af7c6204b04bba0e199a57d2f3fbbd5b4a14bf8739bf9d2a39b2a0aad545e0f86
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.18
  resolution: "@types/qs@npm:6.9.18"
  checksum: 10c0/790b9091348e06dde2c8e4118b5771ab386a8c22a952139a2eb0675360a2070d0b155663bf6f75b23f258fd0a1f7ffc0ba0f059d99a719332c03c40d9e9cd63b
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10c0/361bb3e964ec5133fa40644a0b942279ed5df1949f21321d77de79f48b728d39253e5ce0408c9c17e4e0fd95ca7899da36841686393b9f7a1e209916e9381a3c
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.2":
  version: 0.12.2
  resolution: "@types/retry@npm:0.12.2"
  checksum: 10c0/07481551a988cc90b423351919928b9ddcd14e3f5591cac3ab950851bb20646e55a10e89141b38bc3093d2056d4df73700b22ff2612976ac86a6367862381884
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.4
  resolution: "@types/send@npm:0.17.4"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10c0/7f17fa696cb83be0a104b04b424fdedc7eaba1c9a34b06027239aba513b398a0e2b7279778af521f516a397ced417c96960e5f50fcfce40c4bc4509fb1a5883c
  languageName: node
  linkType: hard

"@types/serve-index@npm:^1.9.4":
  version: 1.9.4
  resolution: "@types/serve-index@npm:1.9.4"
  dependencies:
    "@types/express": "npm:*"
  checksum: 10c0/94c1b9e8f1ea36a229e098e1643d5665d9371f8c2658521718e259130a237c447059b903bac0dcc96ee2c15fd63f49aa647099b7d0d437a67a6946527a837438
  languageName: node
  linkType: hard

"@types/serve-static@npm:*, @types/serve-static@npm:^1.15.5":
  version: 1.15.7
  resolution: "@types/serve-static@npm:1.15.7"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/26ec864d3a626ea627f8b09c122b623499d2221bbf2f470127f4c9ebfe92bd8a6bb5157001372d4c4bd0dd37a1691620217d9dc4df5aa8f779f3fd996b1c60ae
  languageName: node
  linkType: hard

"@types/slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "@types/slice-ansi@npm:4.0.0"
  checksum: 10c0/5ea517c6739652029ac13810fe8b3b360e7cca4b2fcf99fc879964c2a4f12f8e6b559dacda5b9f016415f0c14017fd9e2b614ecfe0eb056d68e16b3a04cc48bf
  languageName: node
  linkType: hard

"@types/sockjs@npm:^0.3.36":
  version: 0.3.36
  resolution: "@types/sockjs@npm:0.3.36"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/b20b7820ee813f22de4f2ce98bdd12c68c930e016a8912b1ed967595ac0d8a4cbbff44f4d486dd97f77f5927e7b5725bdac7472c9ec5b27f53a5a13179f0612f
  languageName: node
  linkType: hard

"@types/ws@npm:^8.5.10":
  version: 8.18.1
  resolution: "@types/ws@npm:8.18.1"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/61aff1129143fcc4312f083bc9e9e168aa3026b7dd6e70796276dcfb2c8211c4292603f9c4864fae702f2ed86e4abd4d38aa421831c2fd7f856c931a481afbab
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^8.18.0":
  version: 8.30.1
  resolution: "@typescript-eslint/eslint-plugin@npm:8.30.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.30.1"
    "@typescript-eslint/type-utils": "npm:8.30.1"
    "@typescript-eslint/utils": "npm:8.30.1"
    "@typescript-eslint/visitor-keys": "npm:8.30.1"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.3.1"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.0.1"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/e34e067c977a20fe927a30e5ffd5402b03eb12d1c9dc932e7c4a772e78fda9e34708fa2d12ace34bad2c51ecaf5b8cfaa4b372c0c5550fe06587b721f6eae57b
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^8.18.0":
  version: 8.30.1
  resolution: "@typescript-eslint/parser@npm:8.30.1"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.30.1"
    "@typescript-eslint/types": "npm:8.30.1"
    "@typescript-eslint/typescript-estree": "npm:8.30.1"
    "@typescript-eslint/visitor-keys": "npm:8.30.1"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/add025d5cfca5cd4d1f74c9297e71de95c945f4efbe6cbfbc72e2cd794cd2684397c7d832bdb5177a1f54398111243d20bd0d2ffdb32a4d5230f1db7cd6fbfb6
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/scope-manager@npm:8.30.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.30.1"
    "@typescript-eslint/visitor-keys": "npm:8.30.1"
  checksum: 10c0/8560fd02bb2a73b56f79af1dfa311491926f3625a04c0f32777c7c0bdec47b4a677addf2d2e2cc313416bb59b7a6e0bff7837449816a5ec5ff81e923daa76ca7
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/type-utils@npm:8.30.1"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.30.1"
    "@typescript-eslint/utils": "npm:8.30.1"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.0.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/c233d2b0b06bd8eca4ee38aebb7544d4084143590328f38c00302f98a62b06868394d4ab1cd798af68d5a47efd84976cc14d415e9e519396dc89aa8d4d47c9ee
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/types@npm:8.30.1"
  checksum: 10c0/461e800bf911c24d9b61bdbeed897921454acc0c24b4e8a79f943c14234241828c13a31dce31dcce77511185f806a2fb94769075e122e3182ba5a32dd55573eb
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.30.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.30.1"
    "@typescript-eslint/visitor-keys": "npm:8.30.1"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.0.1"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/9eb0b1bc4b5df37c84ac411d77ce0edf934b5fdde021ed45c984aa7894132ff7a276d2b95e2d29ef84c411df8ecdf096eec3e07ec1ee5b1fa8c623d40a82ecf0
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/utils@npm:8.30.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:8.30.1"
    "@typescript-eslint/types": "npm:8.30.1"
    "@typescript-eslint/typescript-estree": "npm:8.30.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/ad54aa386edc2e19957c73ef25eea3e263e7e15e941c72e91ca6c8ea2536979d343a6069de0e40b15f0e732ddaacbfcc3d5f25a1583e11a32120c42c471802ea
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.30.1":
  version: 8.30.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.30.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.30.1"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/bdc182289c68a5c8f891f9aecf6ccb59743c3f2b1bbe57f57f8c7ce1688f4381182e301919895cefc929539eea914eeb847f7d351cdc3f685ed6c5ee67a10c9e
  languageName: node
  linkType: hard

"@vitejs/plugin-basic-ssl@npm:1.2.0":
  version: 1.2.0
  resolution: "@vitejs/plugin-basic-ssl@npm:1.2.0"
  peerDependencies:
    vite: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
  checksum: 10c0/0d360fcca01f91ade6e451edbea09a107ff9e95cd3c3766c7a069d1a168709df92d96c0bd1eccc66e2739a153e07c75a45321ec487450c0da942606200d8441d
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
  checksum: 10c0/67a59be8ed50ddd33fbb2e09daa5193ac215bf7f40a9371be9a0d9797a114d0d1196316d2f3943efdb923a3d809175e1563a3cb80c814fb8edccd1e77494972b
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: 10c0/0e88bdb8b50507d9938be64df0867f00396b55eba9df7d3546eb5dc0ca64d62e06f8d881ec4a6153f2127d0f4c11d102b6e7d17aec2f26bb5ff95a5e60652412
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 10c0/31be497f996ed30aae4c08cac3cce50c8dcd5b29660383c0155fce1753804fc55d47fcba74e10141c7dd2899033164e117b3bcfcda23a6b043e4ded4f1003dfb
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: 10c0/0d54105dc373c0fe6287f1091e41e3a02e36cdc05e8cf8533cdc16c59ff05a646355415893449d3768cda588af451c274f13263300a251dc11a575bc4c9bd210
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": "npm:1.13.2"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/9c46852f31b234a8fb5a5a9d3f027bc542392a0d4de32f1a9c0075d5e8684aa073cb5929b56df565500b3f9cc0a2ab983b650314295b9bf208d1a1651bfc825a
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 10c0/c4355d14f369b30cf3cbdd3acfafc7d0488e086be6d578e3c9780bd1b512932352246be96e034e2a7fcfba4f540ec813352f312bfcbbfe5bcfbf694f82ccc682
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
  checksum: 10c0/1f9b33731c3c6dbac3a9c483269562fa00d1b6a4e7133217f40e83e975e636fd0f8736e53abd9a47b06b66082ecc976c7384391ab0a68e12d509ea4e4b948d64
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": "npm:^1.2.0"
  checksum: 10c0/2e732ca78c6fbae3c9b112f4915d85caecdab285c0b337954b180460290ccd0fb00d2b1dc4bb69df3504abead5191e0d28d0d17dfd6c9d2f30acac8c4961c8a7
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/dad5ef9e383c8ab523ce432dfd80098384bf01c45f70eb179d594f85ce5db2f80fa8c9cba03adafd85684e6d6310f0d3969a882538975989919329ac4c984659
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 10c0/d3fac9130b0e3e5a1a7f2886124a278e9323827c87a2b971e6d0da22a2ba1278ac9f66a4f2e363ecd9fac8da42e6941b22df061a119e5c0335f81006de9ee799
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-section": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-opt": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
    "@webassemblyjs/wast-printer": "npm:1.14.1"
  checksum: 10c0/5ac4781086a2ca4b320bdbfd965a209655fe8a208ca38d89197148f8597e587c9a2c94fb6bd6f1a7dbd4527c49c6844fcdc2af981f8d793a97bf63a016aa86d2
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10c0/d678810d7f3f8fecb2e2bdadfb9afad2ec1d2bc79f59e4711ab49c81cec578371e22732d4966f59067abe5fba8e9c54923b57060a729d28d408e608beef67b10
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
  checksum: 10c0/515bfb15277ee99ba6b11d2232ddbf22aed32aad6d0956fe8a0a0a004a1b5a3a277a71d9a3a38365d0538ac40d1b7b7243b1a244ad6cd6dece1c1bb2eb5de7ee
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10c0/95427b9e5addbd0f647939bd28e3e06b8deefdbdadcf892385b5edc70091bf9b92fa5faac3fce8333554437c5d85835afef8c8a7d9d27ab6ba01ffab954db8c6
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/8d7768608996a052545251e896eac079c98e0401842af8dd4de78fba8d90bd505efb6c537e909cd6dae96e09db3fa2e765a6f26492553a675da56e2db51f9d24
  languageName: node
  linkType: hard

"@xml-tools/parser@npm:^1.0.11":
  version: 1.0.11
  resolution: "@xml-tools/parser@npm:1.0.11"
  dependencies:
    chevrotain: "npm:7.1.1"
  checksum: 10c0/5abc75163d6b2ac8e9006a54576523513535953237463297137c5a3665ce1b9d220b77b6dbb68ab93df3fab40bbc98bbb10e90dd690fd7646fdb021323827971
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.7.0, @xmldom/xmldom@npm:^0.7.5":
  version: 0.7.13
  resolution: "@xmldom/xmldom@npm:0.7.13"
  checksum: 10c0/cb02e4e8d986acf18578a5f25d1bce5e18d08718f40d8a0cdd922a4c112c8e00daf94de4e43f9556ed147c696b135f2ab81fa9a2a8a0416f60af15d156b60e40
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: 10c0/a8565d29d135039bd99ae4b2220d3e167d22cf53f867e491ed479b3f84f895742d0097f935b19aab90265a23d5d46711e4204f14c479ae3637fbf06c4666882f
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 10c0/8582cbc69c79ad2d31568c412129bf23d2b1210a1dfb60c82d5a1df93334da4ee51f3057051658569e2c196d8dc33bc05ae6b974a711d0d16e801e1d0647ccd1
  languageName: node
  linkType: hard

"@yarnpkg/lockfile@npm:1.1.0":
  version: 1.1.0
  resolution: "@yarnpkg/lockfile@npm:1.1.0"
  checksum: 10c0/0bfa50a3d756623d1f3409bc23f225a1d069424dbc77c6fd2f14fb377390cd57ec703dc70286e081c564be9051ead9ba85d81d66a3e68eeb6eb506d4e0c0fbda
  languageName: node
  linkType: hard

"JSONStream@npm:^1.0.4":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: "npm:^1.2.0"
    through: "npm:>=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 10c0/0f54694da32224d57b715385d4a6b668d2117379d1f3223dc758459246cca58fdc4c628b83e8a8883334e454a0a30aa198ede77c788b55537c1844f686a751f2
  languageName: node
  linkType: hard

"MyQU@workspace:.":
  version: 0.0.0-use.local
  resolution: "MyQU@workspace:."
  dependencies:
    "@angular-devkit/build-angular": "npm:^19.0.0"
    "@angular-eslint/builder": "npm:^19.0.0"
    "@angular-eslint/eslint-plugin": "npm:^19.0.0"
    "@angular-eslint/eslint-plugin-template": "npm:^19.0.0"
    "@angular-eslint/schematics": "npm:^19.0.0"
    "@angular-eslint/template-parser": "npm:^19.0.0"
    "@angular/animations": "npm:^19.0.0"
    "@angular/cli": "npm:^19.0.0"
    "@angular/common": "npm:^19.0.0"
    "@angular/compiler": "npm:^19.0.0"
    "@angular/compiler-cli": "npm:^19.0.0"
    "@angular/core": "npm:^19.0.0"
    "@angular/forms": "npm:^19.0.0"
    "@angular/language-service": "npm:^19.0.0"
    "@angular/platform-browser": "npm:^19.0.0"
    "@angular/platform-browser-dynamic": "npm:^19.0.0"
    "@angular/router": "npm:^19.0.0"
    "@capacitor/app": "npm:7.0.1"
    "@capacitor/assets": "npm:^3.0.5"
    "@capacitor/browser": "npm:^7.0.1"
    "@capacitor/cli": "npm:7.2.0"
    "@capacitor/core": "npm:7.2.0"
    "@capacitor/haptics": "npm:7.0.1"
    "@capacitor/ios": "npm:^7.2.0"
    "@capacitor/keyboard": "npm:7.0.1"
    "@capacitor/status-bar": "npm:7.0.1"
    "@capgo/inappbrowser": "npm:^7.9.3"
    "@emran-alhaddad/saudi-riyal-font": "npm:^1.0.3"
    "@hugeicons-pro/core-solid-rounded": "npm:^1.0.14"
    "@hugeicons-pro/core-stroke-rounded": "npm:^1.0.14"
    "@hugeicons/angular": "npm:^1.0.4"
    "@ionic/angular": "npm:^8.0.0"
    "@ionic/angular-toolkit": "npm:^12.0.0"
    "@ionic/storage-angular": "npm:^4.0.0"
    "@ngx-translate/core": "npm:^16.0.4"
    "@ngx-translate/http-loader": "npm:^16.0.1"
    "@splidejs/splide": "npm:^4.1.4"
    "@types/jasmine": "npm:~5.1.0"
    "@typescript-eslint/eslint-plugin": "npm:^8.18.0"
    "@typescript-eslint/parser": "npm:^8.18.0"
    eslint: "npm:^9.16.0"
    eslint-plugin-import: "npm:^2.29.1"
    eslint-plugin-jsdoc: "npm:^48.2.1"
    eslint-plugin-prefer-arrow: "npm:1.2.2"
    ionicons: "npm:^7.0.0"
    jasmine-core: "npm:~5.1.0"
    jasmine-spec-reporter: "npm:~5.0.0"
    karma: "npm:~6.4.0"
    karma-chrome-launcher: "npm:~3.2.0"
    karma-coverage: "npm:~2.2.0"
    karma-jasmine: "npm:~5.1.0"
    karma-jasmine-html-reporter: "npm:~2.1.0"
    rxjs: "npm:~7.8.0"
    tslib: "npm:^2.3.0"
    typescript: "npm:~5.6.3"
    zone.js: "npm:~0.15.0"
  languageName: unknown
  linkType: soft

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.14.0, acorn@npm:^8.4.1, acorn@npm:^8.8.2":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dbd36c1ed1d2fa3550140000371fcf721578095b18777b85a79df231ca093b08edc6858d75d6e48c73e431c174dcf9214edbd7e6fa5911b93bd8abfa54e47123
  languageName: node
  linkType: hard

"add-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "add-stream@npm:1.0.0"
  checksum: 10c0/985014a14e76ca4cb24e0fc58bb1556794cf38c5c8937de335a10584f50a371dc48e1c34a59391c7eb9c1fc908b4b86764df5d2756f701df6ba95d1ca2f63ddc
  languageName: node
  linkType: hard

"adjust-sourcemap-loader@npm:^4.0.0":
  version: 4.0.0
  resolution: "adjust-sourcemap-loader@npm:4.0.0"
  dependencies:
    loader-utils: "npm:^2.0.0"
    regex-parser: "npm:^2.2.11"
  checksum: 10c0/6a6e5bb8b670e4e1238c708f6163e92aa2ad0308fe5913de73c89e4cbf41738ee0bcc5552b94d0b7bf8be435ee49b78c6de8a6db7badd80762051e843c8aa14f
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10c0/a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ajv-formats@npm:3.0.1":
  version: 3.0.1
  resolution: "ajv-formats@npm:3.0.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10c0/168d6bca1ea9f163b41c8147bae537e67bd963357a5488a1eaf3abe8baa8eec806d4e45f15b10767e6020679315c7e1e5e6803088dfb84efa2b4e9353b83dd0a
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10c0/e43ba22e91b6a48d96224b83d260d3a3a561b42d391f8d3c6d2c1559f9aa5b253bfb306bc94bbeca1d967c014e15a6efe9a207309e95b3eaae07fcbcdc2af662
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10c0/18bec51f0171b83123ba1d8883c126e60c6f420cef885250898bf77a8d3e65e3bfb9e8564f497e30bdbe762a83e0d144a36931328616a973ee669dc74d4a9590
  languageName: node
  linkType: hard

"ajv@npm:8.17.1, ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-colors@npm:4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10c0/ec87a2f59902f74e61eada7f6e6fe20094a628dab765cfdbd03c3477599368768cffccdb5d3bb19a1b6c99126783a143b1fee31aab729b31ffe5836c7e5e28b9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-escapes@npm:^7.0.0":
  version: 7.0.0
  resolution: "ansi-escapes@npm:7.0.0"
  dependencies:
    environment: "npm:^1.0.0"
  checksum: 10c0/86e51e36fabef18c9c004af0a280573e828900641cea35134a124d2715e0c5a473494ab4ce396614505da77638ae290ff72dd8002d9747d2ee53f5d6bbe336be
  languageName: node
  linkType: hard

"ansi-html-community@npm:^0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 10c0/45d3a6f0b4f10b04fdd44bef62972e2470bfd917bf00439471fa7473d92d7cbe31369c73db863cc45dda115cb42527f39e232e9256115534b8ee5806b0caeed4
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0, ansi-styles@npm:^6.2.1":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"are-docs-informative@npm:^0.0.2":
  version: 0.0.2
  resolution: "are-docs-informative@npm:0.0.2"
  checksum: 10c0/f0326981bd699c372d268b526b170a28f2e1aec2cf99d7de0686083528427ecdf6ae41fef5d9988e224a5616298af747ad8a76e7306b0a7c97cc085a99636d60
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 10c0/070ff801a9d236a6caa647507bdcc7034530604844d64408149a26b9e87c2f97650055c0f049abd1efc024b334635c01f29e0b632b371ac3f26130f4cf65997a
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"aria-query@npm:5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: 10c0/003c7e3e2cff5540bf7a7893775fc614de82b0c5dde8ae823d47b7a28a9d4da1f7ed85f340bdb93d5649caa927755f0e31ecc7ab63edfdfc00c8ef07e505e03e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-ify@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-ify@npm:1.0.0"
  checksum: 10c0/75c9c072faac47bd61779c0c595e912fe660d338504ac70d10e39e1b8a4a0c9c87658703d619b9d1b70d324177ae29dc8d07dda0d0a15d005597bc4c5a59c70c
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    is-string: "npm:^1.0.7"
  checksum: 10c0/5b1004d203e85873b96ddc493f090c9672fd6c80d7a60b798da8a14bff8a670ff95db5aafc9abc14a211943f05220dacf8ea17638ae0af1a6a47b8c0b48ce370
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-shim-unscopables: "npm:^1.1.0"
  checksum: 10c0/82559310d2e57ec5f8fc53d7df420e3abf0ba497935de0a5570586035478ba7d07618cb18e2d4ada2da514c8fb98a034aaf5c06caa0a57e2f7f4c4adedef5956
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/d90e04dfbc43bb96b3d2248576753d1fb2298d2d972e29ca7ad5ec621f0d9e16ff8074dae647eac4f31f4fb7d3f561a7ac005fb01a71f51705a13b5af06a7d8a
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ba899ea22b9dc9bf276e773e98ac84638ed5e0236de06f13d63a90b18ca9e0ec7c97d622d899796e3773930b946cd2413d098656c0c5d8cc58c6f25c21e6bd54
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 10c0/c35c8d1a81bcd5474c0c57fe3f4bad1a4d46a5fa353cedcff7a54da315df60db71829e69104b859dff96c5d68af46bd2be259fe5e50dc6aa9df3b36bea0383ab
  languageName: node
  linkType: hard

"asap@npm:^2.0.0":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10c0/f63d439cc383db1b9c5c6080d1e240bd14dae745f15d11ec5da863e182bbeca70df6c8191cffef5deba0b566ef98834610a68be79ac6379c95eeb26e1b310e25
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"autoprefixer@npm:10.4.20":
  version: 10.4.20
  resolution: "autoprefixer@npm:10.4.20"
  dependencies:
    browserslist: "npm:^4.23.3"
    caniuse-lite: "npm:^1.0.30001646"
    fraction.js: "npm:^4.3.7"
    normalize-range: "npm:^0.1.2"
    picocolors: "npm:^1.0.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 10c0/e1f00978a26e7c5b54ab12036d8c13833fad7222828fc90914771b1263f51b28c7ddb5803049de4e77696cbd02bb25cfc3634e80533025bb26c26aacdf938940
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axobject-query@npm:4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 10c0/c470e4f95008f232eadd755b018cb55f16c03ccf39c027b941cd8820ac6b68707ce5d7368a46756db4256fbc91bb4ead368f84f7fb034b2b7932f082f6dc0775
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: 10c0/ec2f004d1daae04be8c5a1f8aeb7fea213c34025e279db4958eb0b82c1729ee25f7c6e89f92a5f65c8a9cf2d017ce27e3dda912403341d1781bd74528a4849d4
  languageName: node
  linkType: hard

"babel-loader@npm:9.2.1":
  version: 9.2.1
  resolution: "babel-loader@npm:9.2.1"
  dependencies:
    find-cache-dir: "npm:^4.0.0"
    schema-utils: "npm:^4.0.0"
  peerDependencies:
    "@babel/core": ^7.12.0
    webpack: ">=5"
  checksum: 10c0/efb82faff4c7c27e9c15bb28bf11c73200e61cf365118a9514e8d74dd489d0afc2a0d5aaa62cb4254eefc2ab631579224d95a03fd245410f28ea75e24de54ba4
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0, bare-events@npm:^2.5.4":
  version: 2.5.4
  resolution: "bare-events@npm:2.5.4"
  checksum: 10c0/877a9cea73d545e2588cdbd6fd01653e27dac48ad6b44985cdbae73e1f57f292d4ba52e25d1fba53674c1053c463d159f3d5c7bc36a2e6e192e389b499ddd627
  languageName: node
  linkType: hard

"bare-fs@npm:^4.0.1":
  version: 4.1.5
  resolution: "bare-fs@npm:4.1.5"
  dependencies:
    bare-events: "npm:^2.5.4"
    bare-path: "npm:^3.0.0"
    bare-stream: "npm:^2.6.4"
  peerDependencies:
    bare-buffer: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
  checksum: 10c0/af72ec30bb7844524faa14ae2b74d13b08920b1d839c638da4ad1abdda643958d0b86653d284878a2f9160072b603c9dce55c8cc29da8d84e14ffce1c5d42a01
  languageName: node
  linkType: hard

"bare-os@npm:^3.0.1":
  version: 3.6.1
  resolution: "bare-os@npm:3.6.1"
  checksum: 10c0/13064789b3d0d3051d6a89424e6d861c08be101798d69faa78821cffb428b36d1fd4e17c824d5a4939bcd96dbff42c11921494139c8e53c3e520bc0e3f83aeee
  languageName: node
  linkType: hard

"bare-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "bare-path@npm:3.0.0"
  dependencies:
    bare-os: "npm:^3.0.1"
  checksum: 10c0/56a3ca82a9f808f4976cb1188640ac206546ce0ddff582afafc7bd2a6a5b31c3bd16422653aec656eeada2830cfbaa433c6cbf6d6b4d9eba033d5e06d60d9a68
  languageName: node
  linkType: hard

"bare-stream@npm:^2.6.4":
  version: 2.6.5
  resolution: "bare-stream@npm:2.6.5"
  dependencies:
    streamx: "npm:^2.21.0"
  peerDependencies:
    bare-buffer: "*"
    bare-events: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
    bare-events:
      optional: true
  checksum: 10c0/1242286f8f3147e9fd353cdaa9cf53226a807ac0dde8177c13f1463aa4cd1f88e07407c883a1b322b901e9af2d1cd30aacd873529031132c384622972e0419df
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base64id@npm:2.0.0, base64id@npm:~2.0.0":
  version: 2.0.0
  resolution: "base64id@npm:2.0.0"
  checksum: 10c0/6919efd237ed44b9988cbfc33eca6f173a10e810ce50292b271a1a421aac7748ef232a64d1e6032b08f19aae48dce6ee8f66c5ae2c9e5066c82b884861d4d453
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 10c0/925a13897b4db80d4211082fe287bcf96d297af38e26448c857cee3e095c9792e3b8f26b37d268812e7f38a589f694609de8534a018b1937d7dc9f84e6b387c5
  languageName: node
  linkType: hard

"beasties@npm:0.3.2":
  version: 0.3.2
  resolution: "beasties@npm:0.3.2"
  dependencies:
    css-select: "npm:^5.1.0"
    css-what: "npm:^6.1.0"
    dom-serializer: "npm:^2.0.0"
    domhandler: "npm:^5.0.3"
    htmlparser2: "npm:^10.0.0"
    picocolors: "npm:^1.1.1"
    postcss: "npm:^8.4.49"
    postcss-media-query-parser: "npm:^0.2.3"
  checksum: 10c0/ed6d4356f8b0448ce360eabfba80bd3d9f8d6592a6dc2fa78467e6522da62fee87d8116d7b94aa695dc51bef18f332b3962435a414b759939264a8754702faa3
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 10c0/9604224b4c2ab3c43c075d92da15863077a9f59e5d4205f4e7e76acd0cd47e8d469ec5e5dba8d9b32aa233951893b29329ca56ac80c20ce094b4a647a66abae0
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 10c0/230520f1ff920b2d2ce3e372d77a33faa4fa60d802fe01ca4ffbc321ee06023fe9a741ac02793ee778040a16b7e497f7d60c504d1c402b8fdab6f03bb785a25f
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bl@npm:^4.0.3, bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/02847e1d2cb089c9dc6958add42e3cdeaf07d13f575973963335ac0fdece563a50ac770ac4c8fa06492d2dd276f6cc3b7f08c7cd9c7a7ad0f8d388b2a28def5f
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3, body-parser@npm:^1.19.0":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"bonjour-service@npm:^1.2.1":
  version: 1.3.0
  resolution: "bonjour-service@npm:1.3.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    multicast-dns: "npm:^7.2.5"
  checksum: 10c0/5721fd9f9bb968e9cc16c1e8116d770863dd2329cb1f753231de1515870648c225142b7eefa71f14a5c22bc7b37ddd7fdeb018700f28a8c936d50d4162d433c7
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.0":
  version: 0.1.0
  resolution: "bplist-creator@npm:0.1.0"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/86f5fe95f34abd369b381abf0f726e220ecebd60a3d932568ae94895ccf1989a87553e4aee9ab3cfb4f35e6f72319f52aa73028165eec82819ed39f15189d493
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.1":
  version: 0.3.1
  resolution: "bplist-parser@npm:0.3.1"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/00940a60214e8f58246264d389db8817b7f7f968cd544ec4a5843e33f810c7a07294a92060fc507104a1a2921212c053fe8e941fb2129b9b4da5fbb12a08e95c
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.3.2":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/4dc307c11d2511a01255e87e370d4ab6f1962b35fdc27605fd4ce9a557a259c2dc9f87822617ddb1f7aa062a71e30ef20d6103329ac7ce235628f637fb0ed763
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.5, browserslist@npm:^4.23.0, browserslist@npm:^4.23.3, browserslist@npm:^4.24.0, browserslist@npm:^4.24.4":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001688"
    electron-to-chromium: "npm:^1.5.73"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.1"
  bin:
    browserslist: cli.js
  checksum: 10c0/db7ebc1733cf471e0b490b4f47e3e2ea2947ce417192c9246644e92c667dd56a71406cc58f62ca7587caf828364892e9952904a02b7aead752bc65b62a37cfe9
  languageName: node
  linkType: hard

"buffer-crc32@npm:~0.2.3":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: 10c0/cb0a8ddf5cf4f766466db63279e47761eb825693eeba6a5a95ee4ec8cb8f81ede70aa7f9d8aeec083e781d47154290eb5d4d26b3f7a465ec57fb9e7d59c47150
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"bundle-name@npm:^4.1.0":
  version: 4.1.0
  resolution: "bundle-name@npm:4.1.0"
  dependencies:
    run-applescript: "npm:^7.0.0"
  checksum: 10c0/8e575981e79c2bcf14d8b1c027a3775c095d362d1382312f444a7c861b0e21513c0bd8db5bd2b16e50ba0709fa622d4eab6b53192d222120305e68359daece29
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.0, cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase-keys@npm:^6.2.2":
  version: 6.2.2
  resolution: "camelcase-keys@npm:6.2.2"
  dependencies:
    camelcase: "npm:^5.3.1"
    map-obj: "npm:^4.0.0"
    quick-lru: "npm:^4.0.1"
  checksum: 10c0/bf1a28348c0f285c6c6f68fb98a9d088d3c0269fed0cdff3ea680d5a42df8a067b4de374e7a33e619eb9d5266a448fe66c2dd1f8e0c9209ebc348632882a3526
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0, camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001646, caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001713
  resolution: "caniuse-lite@npm:1.0.30001713"
  checksum: 10c0/f5468abfe73ce30e29cc8bde2ea67df2aab69032bdd93345e0640efefb76b7901c84fe1d28d591a797e65fe52fc24cae97060bb5552f9f9740322aff95ce2f9d
  languageName: node
  linkType: hard

"chalk@npm:2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 10c0/96e4731b9ec8050cbb56ab684e8c48d6c33f7826b755802d14e3ebfdc51c57afeece3ea39bc6b09acc359e4363525388b915e16640c1378053820f5e70d0f27d
  languageName: node
  linkType: hard

"chevrotain@npm:7.1.1":
  version: 7.1.1
  resolution: "chevrotain@npm:7.1.1"
  dependencies:
    regexp-to-ast: "npm:0.5.0"
  checksum: 10c0/3fbbb7a30fb87a4cd141a28bdfa2851f54fde4099aa92071442b47605dfc5974eee0388ec25a517087fcea4dcc1f0ce6b371bc975591346327829aa83b3c843d
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.1, chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10c0/a58b9df05bb452f7d105d9e7229ac82fa873741c0c40ddcc7bb82f8a909fbe3f7814c9ebe9bc9a2bef9b737c0ec6e2d699d179048ef06ad3ec46315df0ebe6ad
  languageName: node
  linkType: hard

"chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 10c0/ed57952a84cc0c802af900cf7136de643d3aba2eecb59d29344bc2f3f9bf703a301b9d84cdc71f82c3ffc9ccde831b0d92f5b45f91727d6c9da62f23aef9d9db
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 10c0/3058da7a5f4934b87cf6a90ef5fb68ebc5f7d06f143ed5a4650208e5d7acae47bc03ec844b29fbf5ba7e46e8daa6acecc878f7983a4f4bb7271593da91e61ff5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10c0/1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10c0/92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-cursor@npm:^5.0.0":
  version: 5.0.0
  resolution: "cli-cursor@npm:5.0.0"
  dependencies:
    restore-cursor: "npm:^5.0.0"
  checksum: 10c0/7ec62f69b79f6734ab209a3e4dbdc8af7422d44d360a7cb1efa8a0887bbe466a6e625650c466fe4359aee44dbe2dc0b6994b583d40a05d0808a5cb193641d220
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"cli-truncate@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-truncate@npm:4.0.0"
  dependencies:
    slice-ansi: "npm:^5.0.0"
    string-width: "npm:^7.0.0"
  checksum: 10c0/d7f0b73e3d9b88cb496e6c086df7410b541b56a43d18ade6a573c9c18bd001b1c3fba1ad578f741a4218fdc794d042385f8ac02c25e1c295a2d8b9f3cb86eb4c
  languageName: node
  linkType: hard

"cli-width@npm:^4.1.0":
  version: 4.1.0
  resolution: "cli-width@npm:4.1.0"
  checksum: 10c0/1fbd56413578f6117abcaf858903ba1f4ad78370a4032f916745fa2c7e390183a9d9029cf837df320b0fdce8137668e522f60a30a5f3d6529ff3872d265a955f
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10c0/35229b1bb48647e882104cac374c9a18e34bbf0bace0e2cf03000326b6ca3050d6b59545d91e17bfe3705f4a0e2988787aa5cde6331bf5cbbf0164732cef6492
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/6035f5daf7383470cef82b3d3db00bec70afb3423538c50394386ffbbab135e26c3689c41791f911fa71b62d13d3863c712fdd70f0fbdffd938a1e6fd09aac00
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10c0/637753615aa24adf0f2d505947a1bb75e63964309034a1cf56ba4b1f30af155201edd38d26ffe26911adaae267a3c138b344a4947d39f5fc1b6d6108125aa758
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"colorette@npm:^2.0.10, colorette@npm:^2.0.20":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10c0/e94116ff33b0ff56f3b83b9ace895e5bf87c2a7a47b3401b8c3f3226e050d5ef76cf4072fb3325f9dc24d1698f9b730baf4e05eeaf861d74a1883073f4c98a40
  languageName: node
  linkType: hard

"colors@npm:1.4.0":
  version: 1.4.0
  resolution: "colors@npm:1.4.0"
  checksum: 10c0/9af357c019da3c5a098a301cf64e3799d27549d8f185d86f79af23069e4f4303110d115da98483519331f6fb71c8568d5688fa1c6523600044fd4a54e97c4efb
  languageName: node
  linkType: hard

"commander@npm:8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 10c0/8b043bb8322ea1c39664a1598a95e0495bfe4ca2fad0d84a92d7d1d8d213e2a155b441d2470c8e08de7c4a28cf2bc6e169211c49e1b21d9f7edc6ae4d9356060
  languageName: node
  linkType: hard

"commander@npm:^12.1.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10c0/6e1996680c083b3b897bfc1cfe1c58dfbcd9842fd43e1aaf8a795fbc237f65efcc860a3ef457b318e73f29a4f4a28f6403c3d653d021d960e4632dd45bde54a9
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:^9.3.0":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: 10c0/5f7784fbda2aaec39e89eb46f06a999e00224b3763dc65976e05929ec486e174fe9aac2655f03ba6a5e83875bd173be5283dc19309b7c65954701c02025b3c1d
  languageName: node
  linkType: hard

"comment-parser@npm:1.4.1":
  version: 1.4.1
  resolution: "comment-parser@npm:1.4.1"
  checksum: 10c0/d6c4be3f5be058f98b24f2d557f745d8fe1cc9eb75bebbdccabd404a0e1ed41563171b16285f593011f8b6a5ec81f564fb1f2121418ac5cbf0f49255bf0840dd
  languageName: node
  linkType: hard

"common-path-prefix@npm:^3.0.0":
  version: 3.0.0
  resolution: "common-path-prefix@npm:3.0.0"
  checksum: 10c0/c4a74294e1b1570f4a8ab435285d185a03976c323caa16359053e749db4fde44e3e6586c29cd051100335e11895767cbbd27ea389108e327d62f38daf4548fdb
  languageName: node
  linkType: hard

"compare-func@npm:^2.0.0":
  version: 2.0.0
  resolution: "compare-func@npm:2.0.0"
  dependencies:
    array-ify: "npm:^1.0.0"
    dot-prop: "npm:^5.1.0"
  checksum: 10c0/78bd4dd4ed311a79bd264c9e13c36ed564cde657f1390e699e0f04b8eee1fc06ffb8698ce2dfb5fbe7342d509579c82d4e248f08915b708f77f7b72234086cc3
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/804d3c8430939f4fd88e5128333f311b4035f6425a7f2959d74cfb5c98ef3a3e3e18143208f3f9d0fcae4cd3bcf3d2fbe525e0fcb955e6e146e070936f025a24
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^2.0.0":
  version: 2.0.0
  resolution: "connect-history-api-fallback@npm:2.0.0"
  checksum: 10c0/90fa8b16ab76e9531646cc70b010b1dbd078153730c510d3142f6cf07479ae8a812c5a3c0e40a28528dd1681a62395d0cfdef67da9e914c4772ac85d69a3ed87
  languageName: node
  linkType: hard

"connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: "npm:2.6.9"
    finalhandler: "npm:1.1.2"
    parseurl: "npm:~1.3.3"
    utils-merge: "npm:1.0.1"
  checksum: 10c0/f120c6116bb16a0a7d2703c0b4a0cd7ed787dc5ec91978097bf62aa967289020a9f41a9cd3c3276a7b92aaa36f382d2cd35fed7138fd466a55c8e9fdbed11ca8
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^5.0.12":
  version: 5.0.13
  resolution: "conventional-changelog-angular@npm:5.0.13"
  dependencies:
    compare-func: "npm:^2.0.0"
    q: "npm:^1.5.1"
  checksum: 10c0/bca711b835fe01d75e3500b738f6525c91a12096218e917e9fd81bf9accf157f904fee16f88c523fd5462fb2a7cb1d060eb79e9bc9a3ccb04491f0c383b43231
  languageName: node
  linkType: hard

"conventional-changelog-atom@npm:^2.0.8":
  version: 2.0.8
  resolution: "conventional-changelog-atom@npm:2.0.8"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/1c7e971e8ba58564397c2dfc9a7522f46bad315844ae782db66e27b2d584f22c21a757a429400657c2eef915690e8fd04bddfc3f8e9504d1fadccd8d0758217b
  languageName: node
  linkType: hard

"conventional-changelog-codemirror@npm:^2.0.8":
  version: 2.0.8
  resolution: "conventional-changelog-codemirror@npm:2.0.8"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/467c8c0daec0424acad6b30aa8897ea5e07c327352d3daae50f3a5427584bcb47d5ac5a3167eb7aeb818ebb856e2e81b19cab9a256fe6f21ad96e4a751599325
  languageName: node
  linkType: hard

"conventional-changelog-conventionalcommits@npm:^4.5.0":
  version: 4.6.3
  resolution: "conventional-changelog-conventionalcommits@npm:4.6.3"
  dependencies:
    compare-func: "npm:^2.0.0"
    lodash: "npm:^4.17.15"
    q: "npm:^1.5.1"
  checksum: 10c0/f3b5e6132ec03dad4aa4a2b5ac47ee0e2ae8be6d0fa53a131c722412ce7c02a742c190790f15b5ab4983a31ce90b7066ce1f3f3d5cc4253aa3484ee414259bd2
  languageName: node
  linkType: hard

"conventional-changelog-core@npm:^4.2.1":
  version: 4.2.4
  resolution: "conventional-changelog-core@npm:4.2.4"
  dependencies:
    add-stream: "npm:^1.0.0"
    conventional-changelog-writer: "npm:^5.0.0"
    conventional-commits-parser: "npm:^3.2.0"
    dateformat: "npm:^3.0.0"
    get-pkg-repo: "npm:^4.0.0"
    git-raw-commits: "npm:^2.0.8"
    git-remote-origin-url: "npm:^2.0.0"
    git-semver-tags: "npm:^4.1.1"
    lodash: "npm:^4.17.15"
    normalize-package-data: "npm:^3.0.0"
    q: "npm:^1.5.1"
    read-pkg: "npm:^3.0.0"
    read-pkg-up: "npm:^3.0.0"
    through2: "npm:^4.0.0"
  checksum: 10c0/4c9f30350250298d9bbb56988b3093ec7de593499a796609c5877115533362815434ff6df3493649e20b1b40399fef3d42032f39e8279bb8df192b89e6e32e69
  languageName: node
  linkType: hard

"conventional-changelog-ember@npm:^2.0.9":
  version: 2.0.9
  resolution: "conventional-changelog-ember@npm:2.0.9"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/bc37a1ec320b56f9831ec6a156d77444743944cdc06ff23b1175a3a23063b907b31fad402566a281b722da1bc9fd687db993cc8dbe9a9baf6e38af24541ccfbc
  languageName: node
  linkType: hard

"conventional-changelog-eslint@npm:^3.0.9":
  version: 3.0.9
  resolution: "conventional-changelog-eslint@npm:3.0.9"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/340b3be510e6713e37f641f0efcb2d8d2bc0b2c1bc38e7c1e2107f69432606290661d43cbc5971b418dd87cd92c2acb86af857264643a607cd8f29887e28683d
  languageName: node
  linkType: hard

"conventional-changelog-express@npm:^2.0.6":
  version: 2.0.6
  resolution: "conventional-changelog-express@npm:2.0.6"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/11a02868847d7d1c585bd38cdd7e39636aefde3ef83138044d859d31c23afc1a82a3cab26c8b8aaae2f536b457b011232325c3ed3f2d6a9ec564522dae265ae2
  languageName: node
  linkType: hard

"conventional-changelog-jquery@npm:^3.0.11":
  version: 3.0.11
  resolution: "conventional-changelog-jquery@npm:3.0.11"
  dependencies:
    q: "npm:^1.5.1"
  checksum: 10c0/5662ff1bee271f6f7f2ca893b84942ec01e4a48299701b3323383dde3e461301c65f248dbcfa8219742258e96b1547ba5f21e66f4785fbc39cbe3074d46d71a4
  languageName: node
  linkType: hard

"conventional-changelog-jshint@npm:^2.0.9":
  version: 2.0.9
  resolution: "conventional-changelog-jshint@npm:2.0.9"
  dependencies:
    compare-func: "npm:^2.0.0"
    q: "npm:^1.5.1"
  checksum: 10c0/3048c3a02b173836f5c2f9c326bac7e80386e7591b9734d4f3a91e7dfe87329fde03414c62fdebe718a82f29e61b1122343186180e7173a47513487b3cfb463d
  languageName: node
  linkType: hard

"conventional-changelog-preset-loader@npm:^2.3.4":
  version: 2.3.4
  resolution: "conventional-changelog-preset-loader@npm:2.3.4"
  checksum: 10c0/a978bcd5fc2eb12b56bc03ec59705af32e521fd27b98a209a26767c2078d423e7d8e30c09d45547371631790f0387453434c73c4541521a7473dce14d5360c7d
  languageName: node
  linkType: hard

"conventional-changelog-writer@npm:^5.0.0":
  version: 5.0.1
  resolution: "conventional-changelog-writer@npm:5.0.1"
  dependencies:
    conventional-commits-filter: "npm:^2.0.7"
    dateformat: "npm:^3.0.0"
    handlebars: "npm:^4.7.7"
    json-stringify-safe: "npm:^5.0.1"
    lodash: "npm:^4.17.15"
    meow: "npm:^8.0.0"
    semver: "npm:^6.0.0"
    split: "npm:^1.0.0"
    through2: "npm:^4.0.0"
  bin:
    conventional-changelog-writer: cli.js
  checksum: 10c0/268b56a3e4db07ad24da7134788c889ecd024cf2e7c0bfe8ca76f83e5db79f057538c45500b052a77b7933c4d0f47e2e807c6e756cbd5ad9db365744c9ce0e7f
  languageName: node
  linkType: hard

"conventional-changelog@npm:^3.1.4":
  version: 3.1.25
  resolution: "conventional-changelog@npm:3.1.25"
  dependencies:
    conventional-changelog-angular: "npm:^5.0.12"
    conventional-changelog-atom: "npm:^2.0.8"
    conventional-changelog-codemirror: "npm:^2.0.8"
    conventional-changelog-conventionalcommits: "npm:^4.5.0"
    conventional-changelog-core: "npm:^4.2.1"
    conventional-changelog-ember: "npm:^2.0.9"
    conventional-changelog-eslint: "npm:^3.0.9"
    conventional-changelog-express: "npm:^2.0.6"
    conventional-changelog-jquery: "npm:^3.0.11"
    conventional-changelog-jshint: "npm:^2.0.9"
    conventional-changelog-preset-loader: "npm:^2.3.4"
  checksum: 10c0/8065d5d742a400ab6d73ea5a42af746c3ec51e081e5ea542b00ebb220f904828002a04ae5841d5588a242773f8112f28bc353bf700fb0b2bda182fac6505c7a7
  languageName: node
  linkType: hard

"conventional-commits-filter@npm:^2.0.7":
  version: 2.0.7
  resolution: "conventional-commits-filter@npm:2.0.7"
  dependencies:
    lodash.ismatch: "npm:^4.4.0"
    modify-values: "npm:^1.0.0"
  checksum: 10c0/df06fb29285b473614f5094e983d26fcc14cd0f64b2cbb2f65493fc8bd47c077c2310791d26f4b2b719e9585aaade95370e73230bff6647163164a18b9dfaa07
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^3.2.0":
  version: 3.2.4
  resolution: "conventional-commits-parser@npm:3.2.4"
  dependencies:
    JSONStream: "npm:^1.0.4"
    is-text-path: "npm:^1.0.1"
    lodash: "npm:^4.17.15"
    meow: "npm:^8.0.0"
    split2: "npm:^3.0.0"
    through2: "npm:^4.0.0"
  bin:
    conventional-commits-parser: cli.js
  checksum: 10c0/122d7d7f991a04c8e3f703c0e4e9a25b2ecb20906f497e4486cb5c2acd9c68f6d9af745f7e79cb407538f50e840b33399274ac427b20971b98b335d1b66d3d17
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.1, convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: 10c0/281da55454bf8126cbc6625385928c43479f2060984180c42f3a86c8b8c12720a24eac260624a7d1e090004028d2dee78602330578ceec1a08e27cb8bb0a8a5b
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"cookie@npm:~0.7.2":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 10c0/9596e8ccdbf1a3a88ae02cf5ee80c1c50959423e1022e4e60b91dd87c622af1da309253d8abdb258fb5e3eacb4f08e579dc58b4897b8087574eee0fd35dfa5d2
  languageName: node
  linkType: hard

"copy-anything@npm:^2.0.1":
  version: 2.0.6
  resolution: "copy-anything@npm:2.0.6"
  dependencies:
    is-what: "npm:^3.14.1"
  checksum: 10c0/2702998a8cc015f9917385b7f16b0d85f1f6e5e2fd34d99f14df584838f492f49aa0c390d973684c687e895c5c58d08b308a0400ac3e1e3d6fa1e5884a5402ad
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:12.0.2":
  version: 12.0.2
  resolution: "copy-webpack-plugin@npm:12.0.2"
  dependencies:
    fast-glob: "npm:^3.3.2"
    glob-parent: "npm:^6.0.1"
    globby: "npm:^14.0.0"
    normalize-path: "npm:^3.0.0"
    schema-utils: "npm:^4.2.0"
    serialize-javascript: "npm:^6.0.2"
  peerDependencies:
    webpack: ^5.1.0
  checksum: 10c0/1a2715a1280a37b81b7040b89ed962db4aa75475b164f84f266fa4e81f209269b13f8bff10b104dff7558854bafedcdd4f30c40fd23ecd8fa28af45516b459cd
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.41.0
  resolution: "core-js-compat@npm:3.41.0"
  dependencies:
    browserslist: "npm:^4.24.4"
  checksum: 10c0/92d2c748d3dd1c4e3b6cee6b6683b9212db9bc0a6574d933781210daf3baaeb76334ed4636eb8935b45802aa8d9235ab604c9a262694e02a2fa17ad0f6976829
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cors@npm:~2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 10c0/373702b7999409922da80de4a61938aabba6929aea5b6fd9096fefb9e8342f626c0ebd7507b0e8b0b311380744cc985f27edebc0a26e0ddb784b54e1085de761
  languageName: node
  linkType: hard

"cosmiconfig@npm:^9.0.0":
  version: 9.0.0
  resolution: "cosmiconfig@npm:9.0.0"
  dependencies:
    env-paths: "npm:^2.2.1"
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/1c1703be4f02a250b1d6ca3267e408ce16abfe8364193891afc94c2d5c060b69611fdc8d97af74b7e6d5d1aac0ab2fb94d6b079573146bc2d756c2484ce5f0ee
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 10c0/157cbc59b2430ae9a90034a5f3a1b398b6738bf510f713edc4d4e45e169bc514d3d99dd34d8d01ca7ae7830b5b8b537e46ae8f3c8f932371b0875c0151d7ec91
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 10c0/288589b2484fe787f9e146f56c4be90b940018f17af1b152e4dde12309042ff5a2bf69e949aab8b8ac253948381529cc6f3e5a2427b73643a71ff177fa122b37
  languageName: node
  linkType: hard

"css-loader@npm:7.1.2":
  version: 7.1.2
  resolution: "css-loader@npm:7.1.2"
  dependencies:
    icss-utils: "npm:^5.1.0"
    postcss: "npm:^8.4.33"
    postcss-modules-extract-imports: "npm:^3.1.0"
    postcss-modules-local-by-default: "npm:^4.0.5"
    postcss-modules-scope: "npm:^3.2.0"
    postcss-modules-values: "npm:^4.0.0"
    postcss-value-parser: "npm:^4.2.0"
    semver: "npm:^7.5.4"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    webpack: ^5.27.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: 10c0/edec9ed71e3c416c9c6ad41c138834c94baf7629de3b97a3337ae8cec4a45e05c57bdb7c4b4d267229fc04b8970d0d1c0734ded8dcd0ac8c7c286b36facdbbf0
  languageName: node
  linkType: hard

"css-select@npm:^4.2.1":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.0.1"
    domhandler: "npm:^4.3.1"
    domutils: "npm:^2.8.0"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/a489d8e5628e61063d5a8fe0fa1cc7ae2478cb334a388a354e91cf2908154be97eac9fa7ed4dffe87a3e06cf6fcaa6016553115335c4fd3377e13dac7bd5a8e1
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1, css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10c0/6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"custom-event@npm:~1.0.0":
  version: 1.0.1
  resolution: "custom-event@npm:1.0.1"
  checksum: 10c0/86cd8497328b1e17dcda894c8df34a73b7a99f915123940d39b33c709482b2d3a2e689cd5e79e4775eb4167227689f57a2ae2f99a3f0bc9c54c0ac1b06853bd5
  languageName: node
  linkType: hard

"dargs@npm:^7.0.0":
  version: 7.0.0
  resolution: "dargs@npm:7.0.0"
  checksum: 10c0/ec7f6a8315a8fa2f8b12d39207615bdf62b4d01f631b96fbe536c8ad5469ab9ed710d55811e564d0d5c1d548fc8cb6cc70bf0939f2415790159f5a75e0f96c92
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"date-format@npm:^4.0.14":
  version: 4.0.14
  resolution: "date-format@npm:4.0.14"
  checksum: 10c0/1c67a4d77c677bb880328c81d81f5b9ed7fbf672bdaff74e5a0f7314b21188f3a829b06acf120c70cc1df876a7724e3e5c23d511e86d64656a3035a76ac3930b
  languageName: node
  linkType: hard

"dateformat@npm:^3.0.0":
  version: 3.0.3
  resolution: "dateformat@npm:3.0.3"
  checksum: 10c0/2effb8bef52ff912f87a05e4adbeacff46353e91313ad1ea9ed31412db26849f5a0fcc7e3ce36dbfb84fc6c881a986d5694f84838ad0da7000d5150693e78678
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.3.6, debug@npm:^4.4.0":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"debug@npm:~4.3.1, debug@npm:~4.3.2, debug@npm:~4.3.4":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"decamelize-keys@npm:^1.1.0":
  version: 1.1.1
  resolution: "decamelize-keys@npm:1.1.1"
  dependencies:
    decamelize: "npm:^1.1.0"
    map-obj: "npm:^1.0.0"
  checksum: 10c0/4ca385933127437658338c65fb9aead5f21b28d3dd3ccd7956eb29aab0953b5d3c047fbc207111672220c71ecf7a4d34f36c92851b7bbde6fca1a02c541bdd7d
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.0, decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10c0/bd89d23141b96d80577e70c54fb226b2f40e74a6817652b80a116d7befb8758261ad073a8895648a29cc0a5947021ab66705cb542fa9c143c82022b27c5b175e
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"default-browser-id@npm:^5.0.0":
  version: 5.0.0
  resolution: "default-browser-id@npm:5.0.0"
  checksum: 10c0/957fb886502594c8e645e812dfe93dba30ed82e8460d20ce39c53c5b0f3e2afb6ceaec2249083b90bdfbb4cb0f34e1f73fde3d68cac00becdbcfd894156b5ead
  languageName: node
  linkType: hard

"default-browser@npm:^5.2.1":
  version: 5.2.1
  resolution: "default-browser@npm:5.2.1"
  dependencies:
    bundle-name: "npm:^4.1.0"
    default-browser-id: "npm:^5.0.0"
  checksum: 10c0/73f17dc3c58026c55bb5538749597db31f9561c0193cd98604144b704a981c95a466f8ecc3c2db63d8bfd04fb0d426904834cfc91ae510c6aeb97e13c5167c4d
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 10c0/5ab0b2bf3fa58b3a443140bbd4cd3db1f91b985cc8a246d330b9ac3fc0b6a325a6d82bddc0b055123d745b3f9931afeea74a5ec545439a1630b9c8512b0eeb49
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"del@npm:^6.0.0":
  version: 6.1.1
  resolution: "del@npm:6.1.1"
  dependencies:
    globby: "npm:^11.0.1"
    graceful-fs: "npm:^4.2.4"
    is-glob: "npm:^4.0.1"
    is-path-cwd: "npm:^2.2.0"
    is-path-inside: "npm:^3.0.2"
    p-map: "npm:^4.0.0"
    rimraf: "npm:^3.0.2"
    slash: "npm:^3.0.0"
  checksum: 10c0/8a095c5ccade42c867a60252914ae485ec90da243d735d1f63ec1e64c1cfbc2b8810ad69a29ab6326d159d4fddaa2f5bad067808c42072351ec458efff86708f
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 10c0/acb24aaf936ef9a227b6be6d495f0d2eb20108a9a6ad40585c5bda1a897031512fef6484e4fdbb80bd249fdaa82841fa1039f416ece03188e677ba11bcfda249
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0, detect-libc@npm:^2.0.1, detect-libc@npm:^2.0.2":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 10c0/88095bda8f90220c95f162bf92cad70bd0e424913e655c20578600e35b91edc261af27531cf160a331e185c0ced93944bc7e09939143225f56312d7fd800fdb7
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 10c0/f039f601790f2e9d4654e499913259a798b1f5246ae24f86ab5e8bd4aaf3bce50484234c494f11fb00aecb0c6e2733aa7b1cf3f530865640b65fbbd65b2c4e09
  languageName: node
  linkType: hard

"dezalgo@npm:^1.0.4":
  version: 1.0.4
  resolution: "dezalgo@npm:1.0.4"
  dependencies:
    asap: "npm:^2.0.0"
    wrappy: "npm:1"
  checksum: 10c0/8a870ed42eade9a397e6141fe5c025148a59ed52f1f28b1db5de216b4d57f0af7a257070c3af7ce3d5508c1ce9dd5009028a76f4b2cc9370dc56551d2355fad8
  languageName: node
  linkType: hard

"di@npm:^0.0.1":
  version: 0.0.1
  resolution: "di@npm:0.0.1"
  checksum: 10c0/fbca4cc93e8c493d50f82df3a9ecaa5d8b2935674aabddeb8f68db3ab03c942c201f9c3d920de094407392ee6f488eac16b96f500c0ea6b408634864b7b939d1
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 10c0/81b91f9d39c4eaca068eb0c1eb0e4afbdc5bb2941d197f513dd596b820b956fef43485876226d65d497bebc15666aa2aa82c679e84f65d5f2bfbf14ee46e32c1
  languageName: node
  linkType: hard

"diff@npm:^5.1.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 10c0/aed0941f206fe261ecb258dc8d0ceea8abbde3ace5827518ff8d302f0fc9cc81ce116c4d8f379151171336caf0516b79e01abdc1ed1201b6440d895a66689eb4
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"dns-packet@npm:^5.2.2":
  version: 5.6.1
  resolution: "dns-packet@npm:5.6.1"
  dependencies:
    "@leichtgewicht/ip-codec": "npm:^2.0.1"
  checksum: 10c0/8948d3d03063fb68e04a1e386875f8c3bcc398fc375f535f2b438fad8f41bf1afa6f5e70893ba44f4ae884c089247e0a31045722fa6ff0f01d228da103f1811d
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/b6416aaff1f380bf56c3b552f31fdf7a69b45689368deca72d28636f41c16bb28ec3ebc40ace97db4c1afc0ceeb8120e8492fe0046841c94c2933b2e30a7d5ac
  languageName: node
  linkType: hard

"dom-serialize@npm:^2.2.1":
  version: 2.2.1
  resolution: "dom-serialize@npm:2.2.1"
  dependencies:
    custom-event: "npm:~1.0.0"
    ent: "npm:~2.2.0"
    extend: "npm:^3.0.0"
    void-elements: "npm:^2.0.0"
  checksum: 10c0/ceb6e62b73c658986ca4c9b8b2fae358d8ae914eb06712d137da595a327c3bbca45a762f412a6d181f892ce5e3cffb855c2db2b64c53ad0534b2a0ad8e65b05e
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.2.0"
    entities: "npm:^2.0.0"
  checksum: 10c0/67d775fa1ea3de52035c98168ddcd59418356943b5eccb80e3c8b3da53adb8e37edb2cc2f885802b7b1765bf5022aec21dfc32910d7f9e6de4c3148f095ab5e0
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0, domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: "npm:^2.2.0"
  checksum: 10c0/5c199c7468cb052a8b5ab80b13528f0db3d794c64fc050ba793b574e158e67c93f8336e87fd81e9d5ee43b0e04aea4d8b93ed7be4899cb726a1601b3ba18538b
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: "npm:^1.0.1"
    domelementtype: "npm:^2.2.0"
    domhandler: "npm:^4.2.0"
  checksum: 10c0/d58e2ae01922f0dd55894e61d18119924d88091837887bf1438f2327f32c65eb76426bd9384f81e7d6dcfb048e0f83c19b222ad7101176ad68cdc9c695b563db
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.2.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"dot-prop@npm:^5.1.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: "npm:^2.0.0"
  checksum: 10c0/93f0d343ef87fe8869320e62f2459f7e70f49c6098d948cc47e060f4a3f827d0ad61e83cb82f2bd90cd5b9571b8d334289978a43c0f98fea4f0e99ee8faa0599
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.137
  resolution: "electron-to-chromium@npm:1.5.137"
  checksum: 10c0/678613e0a3d023563a1acca4d8103a69d389168efeb3b78c1fcc683ed0778d81bfb00c6f621d6535f3fa9530664fc948fc8f2ed27e7548d46cd3987d4b0add6a
  languageName: node
  linkType: hard

"elementtree@npm:^0.1.7":
  version: 0.1.7
  resolution: "elementtree@npm:0.1.7"
  dependencies:
    sax: "npm:1.1.4"
  checksum: 10c0/669e56d5092382ceaaa2022f3c391b0f6702e47876095b0e00a790181b7f6b5f49fe6e67ebe8339fd6b1b1b66ffe53c87dd47f1e550661ea55722279bf24cad3
  languageName: node
  linkType: hard

"emoji-regex@npm:^10.3.0":
  version: 10.4.0
  resolution: "emoji-regex@npm:10.4.0"
  checksum: 10c0/a3fcedfc58bfcce21a05a5f36a529d81e88d602100145fcca3dc6f795e3c8acc4fc18fe773fbf9b6d6e9371205edb3afa2668ec3473fa2aa7fd47d2a9d46482d
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 10c0/7dc4394b7b910444910ad64b812392159a21e1a7ecc637c775a440227dcb4f80eff7fe61f4453a7d7603fa23d23d30cc93fe9e4b5ed985b88d6441cd4a35117b
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"engine.io-parser@npm:~5.2.1":
  version: 5.2.3
  resolution: "engine.io-parser@npm:5.2.3"
  checksum: 10c0/ed4900d8dbef470ab3839ccf3bfa79ee518ea8277c7f1f2759e8c22a48f64e687ea5e474291394d0c94f84054749fd93f3ef0acb51fa2f5f234cc9d9d8e7c536
  languageName: node
  linkType: hard

"engine.io@npm:~6.6.0":
  version: 6.6.4
  resolution: "engine.io@npm:6.6.4"
  dependencies:
    "@types/cors": "npm:^2.8.12"
    "@types/node": "npm:>=10.0.0"
    accepts: "npm:~1.3.4"
    base64id: "npm:2.0.0"
    cookie: "npm:~0.7.2"
    cors: "npm:~2.8.5"
    debug: "npm:~4.3.1"
    engine.io-parser: "npm:~5.2.1"
    ws: "npm:~8.17.1"
  checksum: 10c0/845761163f8ea7962c049df653b75dafb6b3693ad6f59809d4474751d7b0392cbf3dc2730b8a902ff93677a91fd28711d34ab29efd348a8a4b49c6b0724021ab
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10c0/4cffd9b125225184e2abed9fdf0ed3dbd2224c873b165d0838fd066cde32e0918626cba2f1f4bf6860762f13a7e2364fd89a82b99566be2873d813573ac71846
  languageName: node
  linkType: hard

"ent@npm:~2.2.0":
  version: 2.2.2
  resolution: "ent@npm:2.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    punycode: "npm:^1.4.1"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/83673cc952bb1ca01473460eb4f1289448d887ef2bfcdd142bfe83cd20a794a4393b6bca543922bf1eb913d1ae0ab69ca2d2f1f6a5e9f3de6e68464b3a3b9096
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 10c0/7fba6af1f116300d2ba1c5673fc218af1961b20908638391b4e1e6d5850314ee2ac3ec22d741b3a8060479911c99305164aed19b6254bde75e7e6b1b2c3f3aa3
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.3.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10c0/b82a7bd5de282860f3c36a91e815e41e874fd036c83956a568b82729678492eb088359d6f7e0a4f5c00776427263fcba04959b8340fefa430c39b9bce770427e
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0, env-paths@npm:^2.2.1":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"env-paths@npm:^3.0.0":
  version: 3.0.0
  resolution: "env-paths@npm:3.0.0"
  checksum: 10c0/76dec878cee47f841103bacd7fae03283af16f0702dad65102ef0a556f310b98a377885e0f32943831eb08b5ab37842a323d02529f3dfd5d0a40ca71b01b435f
  languageName: node
  linkType: hard

"environment@npm:^1.0.0":
  version: 1.1.0
  resolution: "environment@npm:1.1.0"
  checksum: 10c0/fb26434b0b581ab397039e51ff3c92b34924a98b2039dcb47e41b7bca577b9dbf134a8eadb364415c74464b682e2d3afe1a4c0eb9873dc44ea814c5d3103331d
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"errno@npm:^0.1.1":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: "npm:~1.0.1"
  bin:
    errno: cli.js
  checksum: 10c0/83758951967ec57bf00b5f5b7dc797e6d65a6171e57ea57adcf1bd1a0b477fd9b5b35fae5be1ff18f4090ed156bce1db749fe7e317aac19d485a5d150f6a4936
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.23.9
  resolution: "es-abstract@npm:1.23.9"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.0"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.0"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.3"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.3"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.18"
  checksum: 10c0/1de229c9e08fe13c17fe5abaec8221545dfcd57e51f64909599a6ae896df84b8fd2f7d16c60cb00d7bf495b9298ca3581aded19939d4b7276854a4b066f8422b
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1, es-module-lexer@npm:^1.5.3":
  version: 1.6.0
  resolution: "es-module-lexer@npm:1.6.0"
  checksum: 10c0/667309454411c0b95c476025929881e71400d74a746ffa1ff4cb450bd87f8e33e8eef7854d68e401895039ac0bac64e7809acbebb6253e055dd49ea9e3ea9212
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/1b9702c8a1823fc3ef39035a4e958802cf294dd21e917397c561d0b3e195f383b978359816b1732d02b255ccf63e1e4815da0065b95db8d7c992037be3bbbcdb
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"esbuild-wasm@npm:0.25.1":
  version: 0.25.1
  resolution: "esbuild-wasm@npm:0.25.1"
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/9cc20c0f1c31c686f26202b86279a80307225ac82e52f1713d2971638baf7afd7e89ab5602648f53e1b9c331b7bfea99a76a75e38bb310ecb18c655fa7a9fd63
  languageName: node
  linkType: hard

"esbuild@npm:0.25.1, esbuild@npm:^0.25.0":
  version: 0.25.1
  resolution: "esbuild@npm:0.25.1"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.1"
    "@esbuild/android-arm": "npm:0.25.1"
    "@esbuild/android-arm64": "npm:0.25.1"
    "@esbuild/android-x64": "npm:0.25.1"
    "@esbuild/darwin-arm64": "npm:0.25.1"
    "@esbuild/darwin-x64": "npm:0.25.1"
    "@esbuild/freebsd-arm64": "npm:0.25.1"
    "@esbuild/freebsd-x64": "npm:0.25.1"
    "@esbuild/linux-arm": "npm:0.25.1"
    "@esbuild/linux-arm64": "npm:0.25.1"
    "@esbuild/linux-ia32": "npm:0.25.1"
    "@esbuild/linux-loong64": "npm:0.25.1"
    "@esbuild/linux-mips64el": "npm:0.25.1"
    "@esbuild/linux-ppc64": "npm:0.25.1"
    "@esbuild/linux-riscv64": "npm:0.25.1"
    "@esbuild/linux-s390x": "npm:0.25.1"
    "@esbuild/linux-x64": "npm:0.25.1"
    "@esbuild/netbsd-arm64": "npm:0.25.1"
    "@esbuild/netbsd-x64": "npm:0.25.1"
    "@esbuild/openbsd-arm64": "npm:0.25.1"
    "@esbuild/openbsd-x64": "npm:0.25.1"
    "@esbuild/sunos-x64": "npm:0.25.1"
    "@esbuild/win32-arm64": "npm:0.25.1"
    "@esbuild/win32-ia32": "npm:0.25.1"
    "@esbuild/win32-x64": "npm:0.25.1"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/80fca30dd0f21aec23fdfab34f0a8d5f55df5097dd7f475f2ab561d45662c32ee306f5649071cd1a0ba0614b164c48ca3dc3ee1551a4daf204b8af90e4d893f5
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: 10c0/0ea8a24a72328a51fd95aa8f660dcca74c1429806737cf10261ab90cfcaaf62fd1eff664b76a44270868e0a932711a81b250053942595bcd00a93b1c1575dd61
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10c0/4d8b46dcd525d71276f9be9ffac1d2be61c9d54cc53c992e6333cf957840dee09381842b1acbbb15fc6b255ebab99cd481c5007ab438e5455a14abe1a0468558
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.29.1":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": "npm:^1.1.0"
    array-includes: "npm:^3.1.8"
    array.prototype.findlastindex: "npm:^1.2.5"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.12.0"
    hasown: "npm:^2.0.2"
    is-core-module: "npm:^2.15.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    object.groupby: "npm:^1.0.3"
    object.values: "npm:^1.2.0"
    semver: "npm:^6.3.1"
    string.prototype.trimend: "npm:^1.0.8"
    tsconfig-paths: "npm:^3.15.0"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 10c0/e21d116ddd1900e091ad120b3eb68c5dd5437fe2c930f1211781cd38b246f090a6b74d5f3800b8255a0ed29782591521ad44eb21c5534960a8f1fb4040fd913a
  languageName: node
  linkType: hard

"eslint-plugin-jsdoc@npm:^48.2.1":
  version: 48.11.0
  resolution: "eslint-plugin-jsdoc@npm:48.11.0"
  dependencies:
    "@es-joy/jsdoccomment": "npm:~0.46.0"
    are-docs-informative: "npm:^0.0.2"
    comment-parser: "npm:1.4.1"
    debug: "npm:^4.3.5"
    escape-string-regexp: "npm:^4.0.0"
    espree: "npm:^10.1.0"
    esquery: "npm:^1.6.0"
    parse-imports: "npm:^2.1.1"
    semver: "npm:^7.6.3"
    spdx-expression-parse: "npm:^4.0.0"
    synckit: "npm:^0.9.1"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 10c0/f78bac109e62f838c14f90ebd572a06a865f2896a16201c9324cb92be25b5ba8deb54ee1d8ea36232ee53a41c177d5d5ac80662c0fe2479d1e1e1e7633385659
  languageName: node
  linkType: hard

"eslint-plugin-prefer-arrow@npm:1.2.2":
  version: 1.2.2
  resolution: "eslint-plugin-prefer-arrow@npm:1.2.2"
  peerDependencies:
    eslint: ">=2.0.0"
  checksum: 10c0/f3896fc84d2560780c5001a423ae082ad250b0764378e78deb8bb1ed32de6474be3f681ccd54caf0749a39f50f535fef076fc60f9969208fb22f496ec0194f04
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10c0/d30ef9dc1c1cbdece34db1539a4933fe3f9b14e1ffb27ecc85987902ee663ad7c9473bbd49a9a03195a373741e62e2f807c4938992e019b511993d163450e70a
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.0.2, eslint-scope@npm:^8.3.0":
  version: 8.3.0
  resolution: "eslint-scope@npm:8.3.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/23bf54345573201fdf06d29efa345ab508b355492f6c6cc9e2b9f6d02b896f369b6dd5315205be94b8853809776c4d13353b85c6b531997b164ff6c3328ecf5b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10c0/2ed81c663b147ca6f578312919483eb040295bbab759e5a371953456c636c5b49a559883e2677112453728d66293c0a4c90ab11cab3428cf02a0236d2e738269
  languageName: node
  linkType: hard

"eslint@npm:^9.16.0":
  version: 9.24.0
  resolution: "eslint@npm:9.24.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.0"
    "@eslint/core": "npm:^0.12.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.24.0"
    "@eslint/plugin-kit": "npm:^0.2.7"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/f758ff1b9d2f2af5335f562f3f40aa8f71607b3edca33f7616840a222ed224555aeb3ac6943cc86e4f9ac5dc124a60bbfde624d054fb235631a8c04447e39ecc
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.1.0, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/272beeaca70d0a1a047d61baff64db04664a33d7cfb5d144f84bc8a5c6194c6c8ebe9cc594093ca53add88baa23e59b01e69e8a0160ab32eac570482e165c462
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0, esquery@npm:^1.6.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10c0/9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10c0/5f6d97cbcbac47be798e6355e3a7639a84ee1f7d9b199a07017f1d2f1e2fe236004d14fa5dfaeba661f94ea57805385e326236a6debbc7145c8877fbc0297c6b
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10c0/4ba5c00c506e6c786b4d6262cfbce90ddc14c10d4667e5c83ae993c9de88aa856033994dd2b35b83e8dc1170e224e66a319fa80adc4c32adcd2379bbc75da814
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"expand-template@npm:^2.0.3":
  version: 2.0.3
  resolution: "expand-template@npm:2.0.3"
  checksum: 10c0/1c9e7afe9acadf9d373301d27f6a47b34e89b3391b1ef38b7471d381812537ef2457e620ae7f819d2642ce9c43b189b3583813ec395e2938319abe356a9b2f51
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:^4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: "npm:^0.7.0"
    iconv-lite: "npm:^0.4.24"
    tmp: "npm:^0.0.33"
  checksum: 10c0/c98f1ba3efdfa3c561db4447ff366a6adb5c1e2581462522c56a18bf90dfe4da382f9cd1feee3e330108c3595a854b218272539f311ba1b3298f841eb0fbf339
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 10c0/d53f6f786875e8b0529f784b59b4b05d4b5c31c651710496440006a398389a579c8dbcd2081311478b5bf77f4b0b21de69109c5a4eabea9d8e8783d1eb864e4c
  languageName: node
  linkType: hard

"fast-glob@npm:3.3.3, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.2, fast-glob@npm:^3.3.3":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10c0/74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: "npm:>=0.5.1"
  checksum: 10c0/c6052a0bb322778ce9f89af92890f6f4ce00d5ec92418a35e5f4c6864a4fe736fec0bcebd47eac7c0f0e979b01530746b1c85c83cb04bae789271abf19737420
  languageName: node
  linkType: hard

"fd-slicer@npm:~1.1.0":
  version: 1.1.0
  resolution: "fd-slicer@npm:1.1.0"
  dependencies:
    pend: "npm:~1.2.0"
  checksum: 10c0/304dd70270298e3ffe3bcc05e6f7ade2511acc278bc52d025f8918b48b6aa3b77f10361bddfadfe2a28163f7af7adbdce96f4d22c31b2f648ba2901f0c5fc20e
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/6ccc33be16945ee7bc841e1b4178c0b4cf18d3804894cb482aa514651c962a162f96da7ffc6ebfaf0df311689fb70091b04dd6caffe28d56b9ebdc0e7ccadfdd
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/6a96e1f5caab085628c11d9fdceb82ba608d5e426c6913d4d918409baa271037a47f28fbba73279e8ad614f0b8fa71ea791d265e408d760793829edd8c2f4584
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"find-cache-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "find-cache-dir@npm:4.0.0"
  dependencies:
    common-path-prefix: "npm:^3.0.0"
    pkg-dir: "npm:^7.0.0"
  checksum: 10c0/0faa7956974726c8769671de696d24c643ca1e5b8f7a2401283caa9e07a5da093293e0a0f4bd18c920ec981d2ef945c7f5b946cde268dfc9077d833ad0293cff
  languageName: node
  linkType: hard

"find-up@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: "npm:^2.0.0"
  checksum: 10c0/c080875c9fe28eb1962f35cbe83c683796a0321899f1eed31a37577800055539815de13d53495049697d3ba313013344f843bb9401dd337a1b832be5edfc6840
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"find-up@npm:^6.3.0":
  version: 6.3.0
  resolution: "find-up@npm:6.3.0"
  dependencies:
    locate-path: "npm:^7.1.0"
    path-exists: "npm:^5.0.0"
  checksum: 10c0/07e0314362d316b2b13f7f11ea4692d5191e718ca3f7264110127520f3347996349bf9e16805abae3e196805814bc66ef4bff2b8904dc4a6476085fc9b0eba07
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.7, flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"formidable@npm:^3.5.1":
  version: 3.5.4
  resolution: "formidable@npm:3.5.4"
  dependencies:
    "@paralleldrive/cuid2": "npm:^2.2.2"
    dezalgo: "npm:^1.0.4"
    once: "npm:^1.4.0"
  checksum: 10c0/3a311ce57617eb8f532368e91c0f2bbfb299a0f1a35090e085bd6ca772298f196fbb0b66f0d4b5549d7bf3c5e1844439338d4402b7b6d1fedbe206ad44a931f8
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: 10c0/df291391beea9ab4c263487ffd9d17fed162dbb736982dee1379b2a8cc94e4e24e46ed508c6d278aded9080ba51872f1bc5f3a5fd8d7c74e5f105b508ac28711
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 10c0/a0cde99085f0872f4d244e83e03a46aa387b74f5a5af750896c6b05e9077fac00e9932fdf5aef84f2f16634cd473c63037d7a512576da7d5c2b9163d1909f3a8
  languageName: node
  linkType: hard

"fs-extra@npm:10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-extra@npm:^11.2.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f95e996186ff45463059feb115a22fb048bdaf7e487ecee8a8646c78ed8fdca63630e3077d4c16ce677051f5e60d3355a06f3cd61f3ca43f48cc58822a44d0a
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10c0/259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-east-asian-width@npm:^1.0.0":
  version: 1.3.0
  resolution: "get-east-asian-width@npm:1.3.0"
  checksum: 10c0/1a049ba697e0f9a4d5514c4623781c5246982bdb61082da6b5ae6c33d838e52ce6726407df285cdbb27ec1908b333cf2820989bd3e986e37bb20979437fdf34b
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-pkg-repo@npm:^4.0.0":
  version: 4.2.1
  resolution: "get-pkg-repo@npm:4.2.1"
  dependencies:
    "@hutson/parse-repository-url": "npm:^3.0.0"
    hosted-git-info: "npm:^4.0.0"
    through2: "npm:^2.0.0"
    yargs: "npm:^16.2.0"
  bin:
    get-pkg-repo: src/cli.js
  checksum: 10c0/1338d2e048a594da4a34e7dd69d909376d72784f5ba50963a242b4b35db77533786f618b3f6a9effdee2af20af4917a3b7cf12533b4575d7f9c163886be1fb62
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"git-raw-commits@npm:^2.0.8":
  version: 2.0.11
  resolution: "git-raw-commits@npm:2.0.11"
  dependencies:
    dargs: "npm:^7.0.0"
    lodash: "npm:^4.17.15"
    meow: "npm:^8.0.0"
    split2: "npm:^3.0.0"
    through2: "npm:^4.0.0"
  bin:
    git-raw-commits: cli.js
  checksum: 10c0/c9cee7ce11a6703098f028d7e47986d5d3e4147d66640086734d6ee2472296b8711f91b40ad458e95acac1bc33cf2898059f1dc890f91220ff89c5fcc609ab64
  languageName: node
  linkType: hard

"git-remote-origin-url@npm:^2.0.0":
  version: 2.0.0
  resolution: "git-remote-origin-url@npm:2.0.0"
  dependencies:
    gitconfiglocal: "npm:^1.0.0"
    pify: "npm:^2.3.0"
  checksum: 10c0/3a846ce98ed36b2d0b801e8ec1ab299a236cfc6fa264bfdf9f42301abfdfd8715c946507fd83a10b9db449eb609ac6f8a2a341daf52e3af0000367487f486355
  languageName: node
  linkType: hard

"git-semver-tags@npm:^4.1.1":
  version: 4.1.1
  resolution: "git-semver-tags@npm:4.1.1"
  dependencies:
    meow: "npm:^8.0.0"
    semver: "npm:^6.0.0"
  bin:
    git-semver-tags: cli.js
  checksum: 10c0/cd8c91c666901f8dd6381f4cef2aec32aa3f39e517bd8d8491f9133adf956dde9e0487d510fa0f12042fa474f21a8a88b4aa56db8b473979c7491109c57b7016
  languageName: node
  linkType: hard

"gitconfiglocal@npm:^1.0.0":
  version: 1.0.0
  resolution: "gitconfiglocal@npm:1.0.0"
  dependencies:
    ini: "npm:^1.3.2"
  checksum: 10c0/cfcb16344834113199f209f2758ced778dc30e075ddb49b5dde659b4dd2deadee824db0a1b77e1303cb594d9e8b2240da18c67705f657aa76affb444aa349005
  languageName: node
  linkType: hard

"github-from-package@npm:0.0.0":
  version: 0.0.0
  resolution: "github-from-package@npm:0.0.0"
  checksum: 10c0/737ee3f52d0a27e26332cde85b533c21fcdc0b09fb716c3f8e522cfaa9c600d4a631dec9fcde179ec9d47cca89017b7848ed4d6ae6b6b78f936c06825b1fcc12
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.1, glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 10c0/0486925072d7a916f052842772b61c3e86247f0a80cc0deb9b5a3e8a1a9faad5b04fb6f58986a09f34d3e96cd2a22a24b7e9882fb1cf904c31e9a310de96c429
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^11.0.0":
  version: 11.0.1
  resolution: "glob@npm:11.0.1"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^4.0.1"
    minimatch: "npm:^10.0.0"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^2.0.0"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/2b32588be52e9e90f914c7d8dec32f3144b81b84054b0f70e9adfebf37cd7014570489f2a79d21f7801b9a4bd4cca94f426966bfd00fb64a5b705cfe10da3a03
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.7":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^9.2.0":
  version: 9.3.5
  resolution: "glob@npm:9.3.5"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    minimatch: "npm:^8.0.2"
    minipass: "npm:^4.2.4"
    path-scurry: "npm:^1.6.1"
  checksum: 10c0/2f6c2b9ee019ee21dc258ae97a88719614591e4c979cb4580b1b9df6f0f778a3cb38b4bdaf18dfa584637ea10f89a3c5f2533a5e449cf8741514ad18b0951f2e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"globby@npm:^11.0.1":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"globby@npm:^14.0.0":
  version: 14.1.0
  resolution: "globby@npm:14.1.0"
  dependencies:
    "@sindresorhus/merge-streams": "npm:^2.1.0"
    fast-glob: "npm:^3.3.3"
    ignore: "npm:^7.0.3"
    path-type: "npm:^6.0.0"
    slash: "npm:^5.1.0"
    unicorn-magic: "npm:^0.3.0"
  checksum: 10c0/527a1063c5958255969620c6fa4444a2b2e9278caddd571d46dfbfa307cb15977afb746e84d682ba5b6c94fc081e8997f80ff05dd235441ba1cb16f86153e58e
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"gradle-to-js@npm:^2.0.0":
  version: 2.0.1
  resolution: "gradle-to-js@npm:2.0.1"
  dependencies:
    lodash.merge: "npm:^4.6.2"
  bin:
    gradle-to-js: cli.js
  checksum: 10c0/06d1751261a33877a17a04dce00d7ae19712c910de45d59dc5b044f7e9fc9a71e44d3da2523138218ccc35e793fbdf66189c889e75c6facb7a37692646b1dba1
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 10c0/7ae34ba286a3434f1993ebd1cc9c9e6b6d8ea672182db28b1afc0a7119229552fa7031e3e5f3cd32a76430ece4e94b7da6f12af2eb39d6239a7693e4bd63a998
  languageName: node
  linkType: hard

"handlebars@npm:^4.7.7":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: "npm:^1.2.5"
    neo-async: "npm:^2.6.2"
    source-map: "npm:^0.6.1"
    uglify-js: "npm:^3.1.4"
    wordwrap: "npm:^1.0.0"
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 10c0/7aff423ea38a14bb379316f3857fe0df3c5d66119270944247f155ba1f08e07a92b340c58edaa00cfe985c21508870ee5183e0634dcb53dd405f35c93ef7f10d
  languageName: node
  linkType: hard

"hard-rejection@npm:^2.1.0":
  version: 2.1.0
  resolution: "hard-rejection@npm:2.1.0"
  checksum: 10c0/febc3343a1ad575aedcc112580835b44a89a89e01f400b4eda6e8110869edfdab0b00cd1bd4c3bfec9475a57e79e0b355aecd5be46454b6a62b9a359af60e564
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"he@npm:1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10c0/a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: 10c0/317cbc6b1bbbe23c2a40ae23f3dafe9fa349ce42a89a36f930e3f9c0530c179a3882d2ef1e4141a4c3674d6faaea862138ec55b43ad6f75e387fda2483a13c70
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.0.0, hosted-git-info@npm:^4.0.1":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: "npm:^6.0.0"
  checksum: 10c0/150fbcb001600336d17fdbae803264abed013548eea7946c2264c49ebe2ebd8c4441ba71dd23dd8e18c65de79d637f98b22d4760ba5fb2e0b15d62543d0fff07
  languageName: node
  linkType: hard

"hosted-git-info@npm:^8.0.0":
  version: 8.1.0
  resolution: "hosted-git-info@npm:8.1.0"
  dependencies:
    lru-cache: "npm:^10.0.1"
  checksum: 10c0/53cc838ecaa7d4aa69a81d9d8edc362c9d415f67b76ad38cdd781d2a2f5b45ad0aa9f9b013fb4ea54a9f64fd2365d0b6386b5a24bdf4cb90c80477cf3175aaa2
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: "npm:^2.0.1"
    obuf: "npm:^1.0.0"
    readable-stream: "npm:^2.0.1"
    wbuf: "npm:^1.1.0"
  checksum: 10c0/55b9e824430bab82a19d079cb6e33042d7d0640325678c9917fcc020c61d8a08ca671b6c942c7f0aae9bb6e4b67ffb50734a72f9e21d66407c3138c1983b70f0
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"htmlparser2@npm:^10.0.0":
  version: 10.0.0
  resolution: "htmlparser2@npm:10.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.1"
    entities: "npm:^6.0.0"
  checksum: 10c0/47cfa37e529c86a7ba9a1e0e6f951ad26ef8ca5af898ab6e8916fa02c0264c1453b4a65f28b7b8a7f9d0d29b5a70abead8203bf8b3f07bc69407e85e7d9a68e4
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 10c0/8bb9b716f5fc55f54a451da7f49b9c695c3e45498a789634daec26b61e4add7c85613a4a9e53726c39d09de7a163891ecd6eb5809adb64500a840fd86fe81d03
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.3"
    setprototypeof: "npm:1.1.0"
    statuses: "npm:>= 1.4.0 < 2"
  checksum: 10c0/17ec4046ee974477778bfdd525936c254b872054703ec2caa4d6f099566b8adade636ae6aeeacb39302c5cd6e28fb407ebd937f500f5010d0b6850750414ff78
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.10
  resolution: "http-parser-js@npm:0.5.10"
  checksum: 10c0/8bbcf1832a8d70b2bd515270112116333add88738a2cc05bfb94ba6bde3be4b33efee5611584113818d2bcf654fdc335b652503be5a6b4c0b95e46f214187d93
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:3.0.3":
  version: 3.0.3
  resolution: "http-proxy-middleware@npm:3.0.3"
  dependencies:
    "@types/http-proxy": "npm:^1.17.15"
    debug: "npm:^4.3.6"
    http-proxy: "npm:^1.18.1"
    is-glob: "npm:^4.0.3"
    is-plain-object: "npm:^5.0.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/c4d68a10d8d42f02e59f7dc8249c98d1ac03aecee177b42c2d8b6a0cb6b71c6688e759e5387f4cdb570150070ca1c6808b38010cbdf67f4500a2e75671a36e05
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:^2.0.7":
  version: 2.0.9
  resolution: "http-proxy-middleware@npm:2.0.9"
  dependencies:
    "@types/http-proxy": "npm:^1.17.8"
    http-proxy: "npm:^1.18.1"
    is-glob: "npm:^4.0.1"
    is-plain-obj: "npm:^3.0.0"
    micromatch: "npm:^4.0.2"
  peerDependencies:
    "@types/express": ^4.17.13
  peerDependenciesMeta:
    "@types/express":
      optional: true
  checksum: 10c0/8e9032af625f7c9f2f0d318f6cdb14eb725cc16ffe7b4ccccea25cf591fa819bb7c3bb579e0b543e0ae9c73059b505a6d728290c757bff27bae526a6ed11c05e
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: "npm:^4.0.0"
    follow-redirects: "npm:^1.0.0"
    requires-port: "npm:^1.0.0"
  checksum: 10c0/148dfa700a03fb421e383aaaf88ac1d94521dfc34072f6c59770528c65250983c2e4ec996f2f03aa9f3fe46cd1270a593126068319311e3e8d9e610a37533e94
  languageName: node
  linkType: hard

"https-proxy-agent@npm:7.0.6, https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"hyperdyperid@npm:^1.2.0":
  version: 1.2.0
  resolution: "hyperdyperid@npm:1.2.0"
  checksum: 10c0/885ba3177c7181d315a856ee9c0005ff8eb5dcb1ce9e9d61be70987895d934d84686c37c981cceeb53216d4c9c15c1cc25f1804e84cc6a74a16993c5d7fd0893
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/39c92936fabd23169c8611d2b5cc39e39d10b19b0d223352f20a7579f75b39d5f786114a6b8fc62bee8c5fed59ba9e0d38f7219a4db383e324fb3061664b043d
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore-walk@npm:^7.0.0":
  version: 7.0.0
  resolution: "ignore-walk@npm:7.0.0"
  dependencies:
    minimatch: "npm:^9.0.0"
  checksum: 10c0/3754bcde369a53a92c1d0835ea93feb6c5b2934984d3f5a8f9dd962d13ac33ee3a9e930901a89b5d46fc061870639d983f497186afdfe3484e135f2ad89f5577
  languageName: node
  linkType: hard

"ignore@npm:7.0.3, ignore@npm:^7.0.3":
  version: 7.0.3
  resolution: "ignore@npm:7.0.3"
  checksum: 10c0/8e21637513cbcd888a4873d34d5c651a2e24b3c4c9a6b159335a26bed348c3c386c51d6fab23577f59140e1b226323138fbd50e63882d4568fd12aa6c822029e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"image-size@npm:~0.5.0":
  version: 0.5.5
  resolution: "image-size@npm:0.5.5"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/655204163af06732f483a9fe7cce9dff4a29b7b2e88f5c957a5852e8143fa750f5e54b1955a2ca83de99c5220dbd680002d0d4e09140b01433520f4d5a0b1f4c
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: 10c0/f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.1.1
  resolution: "immutable@npm:5.1.1"
  checksum: 10c0/5fd129ee9e448884003cc4f9e43bb91bab3b39dfeb3b49ddfb8bd563e0620eb47ae1f5b3ef96615d3ec38b52ab9a966dcacf9e39df00ed1a8ad062ddfba01cdf
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 10c0/6e56402373149ea076a434072671f9982f5fad030c7662be0332122fe6c0fa490acb3cc1010d90b6eff8d640b1167d77674add52dfd1bb85d545cf29e80e73e7
  languageName: node
  linkType: hard

"ini@npm:5.0.0, ini@npm:^5.0.0":
  version: 5.0.0
  resolution: "ini@npm:5.0.0"
  checksum: 10c0/657491ce766cbb4b335ab221ee8f72b9654d9f0e35c32fe5ff2eb7ab8c5ce72237ff6456555b50cde88e6507a719a70e28e327b450782b4fc20c90326ec8c1a8
  languageName: node
  linkType: hard

"ini@npm:^1.3.2, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"ini@npm:^2.0.0":
  version: 2.0.0
  resolution: "ini@npm:2.0.0"
  checksum: 10c0/2e0c8f386369139029da87819438b20a1ff3fe58372d93fb1a86e9d9344125ace3a806b8ec4eb160a46e64cbc422fe68251869441676af49b7fc441af2389c25
  languageName: node
  linkType: hard

"ini@npm:^4.1.1":
  version: 4.1.3
  resolution: "ini@npm:4.1.3"
  checksum: 10c0/0d27eff094d5f3899dd7c00d0c04ea733ca03a8eb6f9406ce15daac1a81de022cb417d6eaff7e4342451ffa663389c565ffc68d6825eaf686bf003280b945764
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"ionicons@npm:^7.0.0, ionicons@npm:^7.2.2":
  version: 7.4.0
  resolution: "ionicons@npm:7.4.0"
  dependencies:
    "@stencil/core": "npm:^4.0.3"
  checksum: 10c0/0f0e9b5a390514e864f098fbc6b69823fa2cde1758d0e43ca6a9a3b19d3a15b595c913e5a4445945bbbf779454c710fd101edb00f5f1a658dafd9073cc10b0b6
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"ipaddr.js@npm:^2.1.0":
  version: 2.2.0
  resolution: "ipaddr.js@npm:2.2.0"
  checksum: 10c0/e4ee875dc1bd92ac9d27e06cfd87cdb63ca786ff9fd7718f1d4f7a8ef27db6e5d516128f52d2c560408cbb75796ac2f83ead669e73507c86282d45f84c5abbb6
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0, is-core-module@npm:^2.5.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: 10c0/d2c4f8e6d3e34df75a5defd44991b6068afad4835bb783b902fa12d13ebdb8f41b2a199dcb0b5ed2cb78bfee9e4c0bbdb69c2d9646f4106464674d3e697a5856
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: 10c0/df2a717e813567db0f659c306d61f2f804d480752526886954a2a3e2246c7745fd07a52b5fecf2b68caf0a6c79dcdace6166fdf29cc76ed9975cc334f0a018b8
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-fullwidth-code-point@npm:5.0.0"
  dependencies:
    get-east-asian-width: "npm:^1.0.0"
  checksum: 10c0/cd591b27d43d76b05fa65ed03eddce57a16e1eca0b7797ff7255de97019bcaf0219acfc0c4f7af13319e13541f2a53c0ace476f442b13267b9a6a7568f2b65c8
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: 10c0/a8efb0e84f6197e6ff5c64c52890fa9acb49b7b74fed4da7c95383965da6f0fa592b4dbd5e38a79f87fc108196937acdbcd758fcefc9b140e479b39ce1fcd1cd
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 10c0/dd47904dbf286cd20aa58c5192161be1a67138485b9836d5a70433b21a45442e9611b8498b8ab1f839fc962c7620667a50535fdfb4a6bc7989b8858645c06b4d
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-network-error@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-network-error@npm:1.1.0"
  checksum: 10c0/89eef83c2a4cf43d853145ce175d1cf43183b7a58d48c7a03e7eed4eb395d0934c1f6d101255cdd8c8c2980ab529bfbe5dd9edb24e1c3c28d2b3c814469b5b7d
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: 10c0/85044ed7ba8bd169e2c2af3a178cacb92a97aa75de9569d02efef7f443a824b5e153eba72b9ae3aca6f8ce81955271aa2dc7da67a8b720575d3e38104208cb4e
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 10c0/afce71533a427a759cd0329301c18950333d7589533c2c90205bd3fdcf7b91eb92d1940493190567a433134d2128ec9325de2fd281e05be1920fbee9edd22e0a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10c0/cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 10c0/daaee1805add26f781b413fdf192fc91d52409583be30ace35c82607d440da63cc4cac0ac55136716688d6c0a2c6ef3edb2254fecbd1fe06056d6bd15975ee8c
  languageName: node
  linkType: hard

"is-plain-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-plain-obj@npm:3.0.0"
  checksum: 10c0/8e6483bfb051d42ec9c704c0ede051a821c6b6f9a6c7a3e3b55aa855e00981b0580c8f3b1f5e2e62649b39179b1abfee35d6f8086d999bfaa32c1908d29b07bc
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: 10c0/893e42bad832aae3511c71fd61c0bf61aa3a6d853061c62a307261842727d0d25f761ce9379f7ba7226d6179db2a3157efa918e7fe26360f3bf0842d9f28942c
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-text-path@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-text-path@npm:1.0.1"
  dependencies:
    text-extensions: "npm:^1.0.0"
  checksum: 10c0/61c8650c29548febb6bf69e9541fc11abbbb087a0568df7bc471ba264e95fb254def4e610631cbab4ddb0a1a07949d06416f4ebeaf37875023fb184cdb87ee84
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 10c0/00cbe3455c3756be68d2542c416cab888aebd5012781d6819749fefb15162ff23e38501fe681b3d751c73e8ff561ac09a5293eba6f58fdf0178462ce6dcb3453
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-what@npm:^3.14.1":
  version: 3.14.1
  resolution: "is-what@npm:3.14.1"
  checksum: 10c0/4b770b85454c877b6929a84fd47c318e1f8c2ff70fd72fd625bc3fde8e0c18a6e57345b6e7aa1ee9fbd1c608d27cfe885df473036c5c2e40cd2187250804a2c7
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"is-wsl@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-wsl@npm:3.1.0"
  dependencies:
    is-inside-container: "npm:^1.0.0"
  checksum: 10c0/d3317c11995690a32c362100225e22ba793678fe8732660c6de511ae71a0ff05b06980cf21f98a6bf40d7be0e9e9506f859abe00a1118287d63e53d0a3d06947
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isbinaryfile@npm:^4.0.8":
  version: 4.0.10
  resolution: "isbinaryfile@npm:4.0.10"
  checksum: 10c0/0703d8cfeb69ed79e6d173120f327450011a066755150a6bbf97ffecec1069a5f2092777868315b21359098c84b54984871cad1abce877ad9141fb2caf3dcabf
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:6.0.3":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10c0/a1894e060dd2a3b9f046ffdc87b44c00a35516f5e6b7baf4910369acca79e506fc5323a816f811ae23d82334b38e3ddeb8b3b331bd2c860540793b59a8689128
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.1.0":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10c0/8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.1":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10c0/19e4cc405016f2c906dff271a76715b3e881fa9faeb3f09a86cb99b8512b3a5ed19cadfe0b54c17ca0e54c1142c9c6de9330d65506e35873994e06634eebeb66
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.0.5":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jackspeak@npm:^4.0.1":
  version: 4.1.0
  resolution: "jackspeak@npm:4.1.0"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
  checksum: 10c0/08a6a24a366c90b83aef3ad6ec41dcaaa65428ffab8d80bc7172add0fbb8b134a34f415ad288b2a6fbd406526e9a62abdb40ed4f399fbe00cb45c44056d4dce0
  languageName: node
  linkType: hard

"jasmine-core@npm:^4.1.0":
  version: 4.6.1
  resolution: "jasmine-core@npm:4.6.1"
  checksum: 10c0/3d038b7f6f6f0d3cb56cdb4d2f0323a9d84f3a64a03746f9329a2d5a5166ec5e0ad3232d72ceb4f357cf2f120fdb86310715eaeb174f325833515fd0792a6860
  languageName: node
  linkType: hard

"jasmine-core@npm:~5.1.0":
  version: 5.1.2
  resolution: "jasmine-core@npm:5.1.2"
  checksum: 10c0/bca92fd9c69eb3ed6e11d9de2e06939ba2c4157acaf15f9a19efc87a10af6a6937acbc424726e34ea8b2cc06b3d6fe733fbd2cfd3c9a258b3528567f89d8a578
  languageName: node
  linkType: hard

"jasmine-spec-reporter@npm:~5.0.0":
  version: 5.0.2
  resolution: "jasmine-spec-reporter@npm:5.0.2"
  dependencies:
    colors: "npm:1.4.0"
  checksum: 10c0/18b5ce95c4de29b0bd3fe095021e39ffbbfd36e3feb29ba8d5d4d7653fc6474f9340b22acf74d5dac876890df15cd5420eae6b44472da0f01be4733b78881227
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "npm:*"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/8c4737ffd03887b3c6768e4cc3ca0269c0336c1e4b1b120943958ddb035ed2a0fc6acab6dc99631720a3720af4e708ff84fb45382ad1e83c27946adf3623969b
  languageName: node
  linkType: hard

"jiti@npm:^1.20.0":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 10c0/77b61989c758ff32407cdae8ddc77f85e18e1a13fc4977110dbd2e05fc761842f5f71bce684d9a01316e1c4263971315a111385759951080bbfe17cbb5de8f7a
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsdoc-type-pratt-parser@npm:~4.0.0":
  version: 4.0.0
  resolution: "jsdoc-type-pratt-parser@npm:4.0.0"
  checksum: 10c0/b23ef7bbbe2f56d72630d1c5a233dc9fecaff399063d373c57bef136908c1b05e723dac107177303c03ccf8d75aa51507510b282aa567600477479c5ea0c36d1
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: 10c0/2f1287a7c833e397c9ddd361a78638e828fc523038bb3441fd4fc144cfd2c6cd4963ffb9e207e648cf7b692600f1e1e524e965c32df5152120910e4903a47dcb
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^4.0.0":
  version: 4.0.0
  resolution: "json-parse-even-better-errors@npm:4.0.0"
  checksum: 10c0/84cd9304a97e8fb2af3937bf53acb91c026aeb859703c332684e688ea60db27fc2242aa532a84e1883fdcbe1e5c1fb57c2bef38e312021aa1cd300defc63cf16
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10c0/7dbf35cd0411d1d648dceb6d59ce5857ec939e52e4afc37601aa3da611f0987d5cee5b38d58329ceddf3ed48bd7215229c8d52059ab01f2444a338bf24ed0f37
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10c0/9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonc-parser@npm:3.3.1, jsonc-parser@npm:^3.0.0":
  version: 3.3.1
  resolution: "jsonc-parser@npm:3.3.1"
  checksum: 10c0/269c3ae0a0e4f907a914bf334306c384aabb9929bd8c99f909275ebd5c2d3bc70b9bcd119ad794f339dec9f24b6a4ee9cd5a8ab2e6435e730ad4075388fc2ab6
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0, jsonparse@npm:^1.3.1":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 10c0/89bc68080cd0a0e276d4b5ab1b79cacd68f562467008d176dc23e16e97d4efec9e21741d92ba5087a8433526a45a7e6a9d5ef25408696c402ca1cfbc01a90bf0
  languageName: node
  linkType: hard

"karma-chrome-launcher@npm:~3.2.0":
  version: 3.2.0
  resolution: "karma-chrome-launcher@npm:3.2.0"
  dependencies:
    which: "npm:^1.2.1"
  checksum: 10c0/0cec1ae7d922110dc29cee36389d597157c82f019c8917259f9fa93d1f5ee8e19141c2eb74bfe30797cdb3adbc51a6b65fd18a9ebc1527c725c4acf62cd46d04
  languageName: node
  linkType: hard

"karma-coverage@npm:~2.2.0":
  version: 2.2.1
  resolution: "karma-coverage@npm:2.2.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.2.0"
    istanbul-lib-instrument: "npm:^5.1.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^4.0.1"
    istanbul-reports: "npm:^3.0.5"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/6496bb56b19b60e3f24a64e4da712a640a4f047fa271a40e321fca3e399e808246a38d434a1b77db4cc54d8f71164ebcb6cf310ae75c99ef957b7010b5d90f49
  languageName: node
  linkType: hard

"karma-jasmine-html-reporter@npm:~2.1.0":
  version: 2.1.0
  resolution: "karma-jasmine-html-reporter@npm:2.1.0"
  peerDependencies:
    jasmine-core: ^4.0.0 || ^5.0.0
    karma: ^6.0.0
    karma-jasmine: ^5.0.0
  checksum: 10c0/c20b0b52eada370d66fb06eda1a767f2043fecc6c4647691c889f4f62aa0f8948811dc64f033ebab02085a2e27e79099034ebe1699facf4e24e6bbc5ad0c203f
  languageName: node
  linkType: hard

"karma-jasmine@npm:~5.1.0":
  version: 5.1.0
  resolution: "karma-jasmine@npm:5.1.0"
  dependencies:
    jasmine-core: "npm:^4.1.0"
  peerDependencies:
    karma: ^6.0.0
  checksum: 10c0/827843d2b4af5396c35de6911d15304955bd7376f96527f46285beb0178510aa401ff123a010a9ee3f13aeeeb56f64a9a3e22d7d61fb58ee76a2845b153a9d20
  languageName: node
  linkType: hard

"karma-source-map-support@npm:1.4.0":
  version: 1.4.0
  resolution: "karma-source-map-support@npm:1.4.0"
  dependencies:
    source-map-support: "npm:^0.5.5"
  checksum: 10c0/76085abfa85858e471621386a96842d03b3ae0ff12a997b47a7f51f3e38cd9934792f23cd4bdfb14f49323367a4154ccf8f45c7cbbff88bedd3e1d2580d21ae6
  languageName: node
  linkType: hard

"karma@npm:~6.4.0":
  version: 6.4.4
  resolution: "karma@npm:6.4.4"
  dependencies:
    "@colors/colors": "npm:1.5.0"
    body-parser: "npm:^1.19.0"
    braces: "npm:^3.0.2"
    chokidar: "npm:^3.5.1"
    connect: "npm:^3.7.0"
    di: "npm:^0.0.1"
    dom-serialize: "npm:^2.2.1"
    glob: "npm:^7.1.7"
    graceful-fs: "npm:^4.2.6"
    http-proxy: "npm:^1.18.1"
    isbinaryfile: "npm:^4.0.8"
    lodash: "npm:^4.17.21"
    log4js: "npm:^6.4.1"
    mime: "npm:^2.5.2"
    minimatch: "npm:^3.0.4"
    mkdirp: "npm:^0.5.5"
    qjobs: "npm:^1.2.0"
    range-parser: "npm:^1.2.1"
    rimraf: "npm:^3.0.2"
    socket.io: "npm:^4.7.2"
    source-map: "npm:^0.6.1"
    tmp: "npm:^0.2.1"
    ua-parser-js: "npm:^0.7.30"
    yargs: "npm:^16.1.1"
  bin:
    karma: bin/karma
  checksum: 10c0/1658c4b7396c0edf6f048289182e075b561902e02992e1a3eb72f56f67090ff0c7ad7c91ab099e88a790c60f9500c5a6f974d75f1769e3ea2dfccda52876ec0b
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2, kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"kleur@npm:^4.1.4, kleur@npm:^4.1.5":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 10c0/e9de6cb49657b6fa70ba2d1448fd3d691a5c4370d8f7bbf1c2f64c24d461270f2117e1b0afe8cb3114f13bbd8e51de158c2a224953960331904e636a5e4c0f2a
  languageName: node
  linkType: hard

"launch-editor@npm:^2.6.1":
  version: 2.10.0
  resolution: "launch-editor@npm:2.10.0"
  dependencies:
    picocolors: "npm:^1.0.0"
    shell-quote: "npm:^1.8.1"
  checksum: 10c0/8b5a26be6b0da1da039ed2254b837dea0651a6406ea4dc4c9a5b28ea72862f1b12880135c495baf9d8a08997473b44034172506781744cf82e155451a40b7d51
  languageName: node
  linkType: hard

"less-loader@npm:12.2.0":
  version: 12.2.0
  resolution: "less-loader@npm:12.2.0"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    less: ^3.5.0 || ^4.0.0
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: 10c0/54eea545727930801d2ccc0b586332cd07d0f922b14ab7c8b3f03199944d770ac363081081ed2fda5f23da904336367cb2bb40007c033970dce25f7f9c906ba2
  languageName: node
  linkType: hard

"less@npm:4.2.2":
  version: 4.2.2
  resolution: "less@npm:4.2.2"
  dependencies:
    copy-anything: "npm:^2.0.1"
    errno: "npm:^0.1.1"
    graceful-fs: "npm:^4.1.2"
    image-size: "npm:~0.5.0"
    make-dir: "npm:^2.1.0"
    mime: "npm:^1.4.1"
    needle: "npm:^3.1.0"
    parse-node-version: "npm:^1.0.1"
    source-map: "npm:~0.6.0"
    tslib: "npm:^2.3.0"
  dependenciesMeta:
    errno:
      optional: true
    graceful-fs:
      optional: true
    image-size:
      optional: true
    make-dir:
      optional: true
    mime:
      optional: true
    needle:
      optional: true
    source-map:
      optional: true
  bin:
    lessc: bin/lessc
  checksum: 10c0/d472c203a41fb3722a9bf5677f5348e59d8b6589bf2e3933a77c305b42b2ebbe1e3accf145f05b6d2415ba1dad08add7803646947bf867eec7a2a279d758d99a
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"license-webpack-plugin@npm:4.0.2":
  version: 4.0.2
  resolution: "license-webpack-plugin@npm:4.0.2"
  dependencies:
    webpack-sources: "npm:^3.0.0"
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-sources:
      optional: true
  checksum: 10c0/6014492b22c5f28a4d367057b5b2c1214b83c73785157fea130d5b877b50ed8820d8d8e73e96b3437c455b5b5c6817b36837da093239f95b534be43c0cdcfedc
  languageName: node
  linkType: hard

"lie@npm:3.1.1":
  version: 3.1.1
  resolution: "lie@npm:3.1.1"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/d62685786590351b8e407814acdd89efe1cb136f05cb9236c5a97b2efdca1f631d2997310ad2d565c753db7596799870140e4777c9c9b8c44a0f6bf42d1804a1
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"listr2@npm:8.2.5":
  version: 8.2.5
  resolution: "listr2@npm:8.2.5"
  dependencies:
    cli-truncate: "npm:^4.0.0"
    colorette: "npm:^2.0.20"
    eventemitter3: "npm:^5.0.1"
    log-update: "npm:^6.1.0"
    rfdc: "npm:^1.4.1"
    wrap-ansi: "npm:^9.0.0"
  checksum: 10c0/f5a9599514b00c27d7eb32d1117c83c61394b2a985ec20e542c798bf91cf42b19340215701522736f5b7b42f557e544afeadec47866e35e5d4f268f552729671
  languageName: node
  linkType: hard

"lmdb@npm:3.2.6":
  version: 3.2.6
  resolution: "lmdb@npm:3.2.6"
  dependencies:
    "@lmdb/lmdb-darwin-arm64": "npm:3.2.6"
    "@lmdb/lmdb-darwin-x64": "npm:3.2.6"
    "@lmdb/lmdb-linux-arm": "npm:3.2.6"
    "@lmdb/lmdb-linux-arm64": "npm:3.2.6"
    "@lmdb/lmdb-linux-x64": "npm:3.2.6"
    "@lmdb/lmdb-win32-x64": "npm:3.2.6"
    msgpackr: "npm:^1.11.2"
    node-addon-api: "npm:^6.1.0"
    node-gyp: "npm:latest"
    node-gyp-build-optional-packages: "npm:5.2.2"
    ordered-binary: "npm:^1.5.3"
    weak-lru-cache: "npm:^1.2.2"
  dependenciesMeta:
    "@lmdb/lmdb-darwin-arm64":
      optional: true
    "@lmdb/lmdb-darwin-x64":
      optional: true
    "@lmdb/lmdb-linux-arm":
      optional: true
    "@lmdb/lmdb-linux-arm64":
      optional: true
    "@lmdb/lmdb-linux-x64":
      optional: true
    "@lmdb/lmdb-win32-x64":
      optional: true
  bin:
    download-lmdb-prebuilds: bin/download-prebuilds.js
  checksum: 10c0/1b7a4e17351f41ae5cbe79a8db7782f34f24484ffbcba6614b91c7d5d4431284c55d8912065e50d05598de0d6dcd0417608d3705d930a207fbf76019219cc43d
  languageName: node
  linkType: hard

"load-json-file@npm:^4.0.0":
  version: 4.0.0
  resolution: "load-json-file@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    parse-json: "npm:^4.0.0"
    pify: "npm:^3.0.0"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/6b48f6a0256bdfcc8970be2c57f68f10acb2ee7e63709b386b2febb6ad3c86198f840889cdbe71d28f741cbaa2f23a7771206b138cd1bdd159564511ca37c1d5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: 10c0/a44d78aae0907a72f73966fe8b82d1439c8c485238bd5a864b1b9a2a3257832effa858790241e6b37876b5446a78889adf2fcc8dd897ce54c089ecc0a0ce0bf0
  languageName: node
  linkType: hard

"loader-utils@npm:3.3.1":
  version: 3.3.1
  resolution: "loader-utils@npm:3.3.1"
  checksum: 10c0/f2af4eb185ac5bf7e56e1337b666f90744e9f443861ac521b48f093fb9e8347f191c8960b4388a3365147d218913bc23421234e7788db69f385bacfefa0b4758
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^2.1.2"
  checksum: 10c0/d5654a77f9d339ec2a03d88221a5a695f337bf71eb8dea031b3223420bb818964ba8ed0069145c19b095f6c8b8fd386e602a3fc7ca987042bd8bb1dcc90d7100
  languageName: node
  linkType: hard

"localforage@npm:^1.9.0":
  version: 1.10.0
  resolution: "localforage@npm:1.10.0"
  dependencies:
    lie: "npm:3.1.1"
  checksum: 10c0/00f19f1f97002e6721587ed5017f502d58faf80dae567d5065d4d1ee0caf0762f40d2e2dba7f0ef7d3f14ee6203242daae9ecad97359bfc10ecff36df11d85a3
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: "npm:^2.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 10c0/24efa0e589be6aa3c469b502f795126b26ab97afa378846cb508174211515633b770aa0ba610cab113caedab8d2a4902b061a08aaed5297c12ab6f5be4df0133
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"locate-path@npm:^7.1.0":
  version: 7.2.0
  resolution: "locate-path@npm:7.2.0"
  dependencies:
    p-locate: "npm:^6.0.0"
  checksum: 10c0/139e8a7fe11cfbd7f20db03923cacfa5db9e14fa14887ea121345597472b4a63c1a42a8a5187defeeff6acf98fd568da7382aa39682d38f0af27433953a97751
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.ismatch@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.ismatch@npm:4.4.0"
  checksum: 10c0/8f96a5dc4b8d3fc5a033dcb259d0c3148a1044fa4d02b4a0e8dce0fa1f2ef3ec4ac131e20b5cb2c985a4e9bcb1c37c0aa5af2cef70094959389617347b8fc645
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: 10c0/67f445a9ffa76db1989d0fa98586e5bc2fd5247260dafb8ad93d9f0ccd5896d53fb830b0e54dade5ad838b9de2006c826831a3c528913093af20dff8bd24aca6
  languageName: node
  linkType: hard

"log-update@npm:^6.1.0":
  version: 6.1.0
  resolution: "log-update@npm:6.1.0"
  dependencies:
    ansi-escapes: "npm:^7.0.0"
    cli-cursor: "npm:^5.0.0"
    slice-ansi: "npm:^7.1.0"
    strip-ansi: "npm:^7.1.0"
    wrap-ansi: "npm:^9.0.0"
  checksum: 10c0/4b350c0a83d7753fea34dcac6cd797d1dc9603291565de009baa4aa91c0447eab0d3815a05c8ec9ac04fdfffb43c82adcdb03ec1fceafd8518e1a8c1cff4ff89
  languageName: node
  linkType: hard

"log4js@npm:^6.4.1":
  version: 6.9.1
  resolution: "log4js@npm:6.9.1"
  dependencies:
    date-format: "npm:^4.0.14"
    debug: "npm:^4.3.4"
    flatted: "npm:^3.2.7"
    rfdc: "npm:^1.3.0"
    streamroller: "npm:^3.1.5"
  checksum: 10c0/05846e48f72d662800c8189bd178c42b4aa2f0c574cfc90a1942cf90b76f621c44019e26796c8fd88da1b6f0fe8272cba607cbaad6ae6ede50a7a096b58197ea
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^11.0.0":
  version: 11.1.0
  resolution: "lru-cache@npm:11.1.0"
  checksum: 10c0/85c312f7113f65fae6a62de7985348649937eb34fb3d212811acbf6704dc322a421788aca253b62838f1f07049a84cc513d88f494e373d3756514ad263670a64
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"magic-string@npm:0.30.17":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: "npm:^4.0.1"
    semver: "npm:^5.6.0"
  checksum: 10c0/ada869944d866229819735bee5548944caef560d7a8536ecbc6536edca28c72add47cc4f6fc39c54fb25d06b58da1f8994cf7d9df7dadea047064749efc085d8
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 10c0/171e458d86854c6b3fc46610cfacf0b45149ba043782558c6875d9f42f222124384ad0b468c92e996d815a8a2003817a710c0a160e49c1c394626f76fa45396f
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.0, make-fetch-happen@npm:^14.0.1, make-fetch-happen@npm:^14.0.2, make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: 10c0/ccca88395e7d38671ed9f5652ecf471ecd546924be2fb900836b9da35e068a96687d96a5f93dcdfa94d9a27d649d2f10a84595590f89a347fb4dda47629dcc52
  languageName: node
  linkType: hard

"map-obj@npm:^4.0.0":
  version: 4.3.0
  resolution: "map-obj@npm:4.3.0"
  checksum: 10c0/1c19e1c88513c8abdab25c316367154c6a0a6a0f77e3e8c391bb7c0e093aefed293f539d026dc013d86219e5e4c25f23b0003ea588be2101ccd757bacc12d43b
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"memfs@npm:^4.6.0":
  version: 4.17.0
  resolution: "memfs@npm:4.17.0"
  dependencies:
    "@jsonjoy.com/json-pack": "npm:^1.0.3"
    "@jsonjoy.com/util": "npm:^1.3.0"
    tree-dump: "npm:^1.0.1"
    tslib: "npm:^2.0.0"
  checksum: 10c0/2901f69e80e1fbefa8aafe994a253fff6f34eb176d8b80d57476311611e516a11ab4dd93f852c8739fe04f2b57d6a4ca7a1828fa0bd401ce631bcac214b3d58b
  languageName: node
  linkType: hard

"meow@npm:^8.0.0":
  version: 8.1.2
  resolution: "meow@npm:8.1.2"
  dependencies:
    "@types/minimist": "npm:^1.2.0"
    camelcase-keys: "npm:^6.2.2"
    decamelize-keys: "npm:^1.1.0"
    hard-rejection: "npm:^2.1.0"
    minimist-options: "npm:4.1.0"
    normalize-package-data: "npm:^3.0.0"
    read-pkg-up: "npm:^7.0.1"
    redent: "npm:^3.0.0"
    trim-newlines: "npm:^3.0.0"
    type-fest: "npm:^0.18.0"
    yargs-parser: "npm:^20.2.3"
  checksum: 10c0/9a8d90e616f783650728a90f4ea1e5f763c1c5260369e6596b52430f877f4af8ecbaa8c9d952c93bbefd6d5bda4caed6a96a20ba7d27b511d2971909b01922a2
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"mergexml@npm:^1.2.3":
  version: 1.2.4
  resolution: "mergexml@npm:1.2.4"
  dependencies:
    "@xmldom/xmldom": "npm:^0.7.0"
    formidable: "npm:^3.5.1"
    xpath: "npm:0.0.27"
  checksum: 10c0/7e78109d2256ae2005b341b6c4b427b0373b2fa71f0b8a8d9018aab231efed578e4d4442335e0e2e277beafcacdefa22d0517112bc32d45bf90358116e3f2739
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27, mime-types@npm:^2.1.31, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0, mime@npm:^1.4.1":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:^2.5.2":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"mimic-function@npm:^5.0.0":
  version: 5.0.1
  resolution: "mimic-function@npm:5.0.1"
  checksum: 10c0/f3d9464dd1816ecf6bdf2aec6ba32c0728022039d992f178237d8e289b48764fee4131319e72eedd4f7f094e22ded0af836c3187a7edc4595d28dd74368fd81d
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10c0/0d6f07ce6e03e9e4445bee655202153bdb8a98d67ee8dc965ac140900d7a2688343e6b4c9a72cfc9ef2f7944dfd76eef4ab2482eb7b293a68b84916bac735362
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 10c0/7e207bd5c20401b292de291f02913230cb1163abca162044f7db1d951fa245b174dc00869d40dd9a9f32a885ad6a5f3e767ee104cf278f399cb4e92d3f582d5c
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:2.9.2":
  version: 2.9.2
  resolution: "mini-css-extract-plugin@npm:2.9.2"
  dependencies:
    schema-utils: "npm:^4.0.0"
    tapable: "npm:^2.2.1"
  peerDependencies:
    webpack: ^5.0.0
  checksum: 10c0/5d3218dbd7db48b572925ddac05162a7415bf81b321f1a0c07016ec643cb5720c8a836ae68d45f5de826097a3013b601706c9c5aacb7f610dc2041b271de2ce0
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10c0/96730e5601cd31457f81a296f521eb56036e6f69133c0b18c13fe941109d53ad23a4204d946a0d638d7f3099482a0cec8c9bb6d642604612ce43ee536be3dddd
  languageName: node
  linkType: hard

"minimatch@npm:3.0.5":
  version: 3.0.5
  resolution: "minimatch@npm:3.0.5"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/f398652d0d260137c289c270a4ac98ebe0a27cd316fa0fac72b096e96cbdc89f71d80d47ac7065c716ba3b0b730783b19180bd85a35f9247535d2adfe96bba76
  languageName: node
  linkType: hard

"minimatch@npm:^10.0.0":
  version: 10.0.1
  resolution: "minimatch@npm:10.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/e6c29a81fe83e1877ad51348306be2e8aeca18c88fdee7a99df44322314279e15799e41d7cb274e4e8bb0b451a3bc622d6182e157dfa1717d6cda75e9cd8cd5d
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^8.0.2":
  version: 8.0.4
  resolution: "minimatch@npm:8.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/a0a394c356dd5b4cb7f821720841a82fa6f07c9c562c5b716909d1b6ec5e56a7e4c4b5029da26dd256b7d2b3a3f38cbf9ddd8680e887b9b5282b09c05501c1ca
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist-options@npm:4.1.0":
  version: 4.1.0
  resolution: "minimist-options@npm:4.1.0"
  dependencies:
    arrify: "npm:^1.0.1"
    is-plain-obj: "npm:^1.1.0"
    kind-of: "npm:^6.0.3"
  checksum: 10c0/7871f9cdd15d1e7374e5b013e2ceda3d327a06a8c7b38ae16d9ef941e07d985e952c589e57213f7aa90a8744c60aed9524c0d85e501f5478382d9181f2763f54
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.3, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^4.2.4":
  version: 4.2.8
  resolution: "minipass@npm:4.2.8"
  checksum: 10c0/4ea76b030d97079f4429d6e8a8affd90baf1b6a1898977c8ccce4701c5a2ba2792e033abc6709373f25c2c4d4d95440d9d5e9464b46b7b76ca44d2ce26d939ce
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp-classic@npm:^0.5.2, mkdirp-classic@npm:^0.5.3":
  version: 0.5.3
  resolution: "mkdirp-classic@npm:0.5.3"
  checksum: 10c0/95371d831d196960ddc3833cc6907e6b8f67ac5501a6582f47dfae5eb0f092e9f8ce88e0d83afcae95d6e2b61a01741ba03714eeafb6f7a6e9dcc158ac85b168
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.5":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"modify-values@npm:^1.0.0":
  version: 1.0.1
  resolution: "modify-values@npm:1.0.1"
  checksum: 10c0/6acb1b82aaf7a02f9f7b554b20cbfc159f223a79c66b0a257511c5933d50b85e12ea1220b0a90a2af6f80bc29ff784f929a52a51881867a93ae6a12ce87a729a
  languageName: node
  linkType: hard

"mrmime@npm:2.0.1":
  version: 2.0.1
  resolution: "mrmime@npm:2.0.1"
  checksum: 10c0/af05afd95af202fdd620422f976ad67dc18e6ee29beb03dd1ce950ea6ef664de378e44197246df4c7cdd73d47f2e7143a6e26e473084b9e4aa2095c0ad1e1761
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"msgpackr-extract@npm:^3.0.2":
  version: 3.0.3
  resolution: "msgpackr-extract@npm:3.0.3"
  dependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-darwin-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-win32-x64": "npm:3.0.3"
    node-gyp: "npm:latest"
    node-gyp-build-optional-packages: "npm:5.2.2"
  dependenciesMeta:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-darwin-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-win32-x64":
      optional: true
  bin:
    download-msgpackr-prebuilds: bin/download-prebuilds.js
  checksum: 10c0/e504fd8bf86a29d7527c83776530ee6dc92dcb0273bb3679fd4a85173efead7f0ee32fb82c8410a13c33ef32828c45f81118ffc0fbed5d6842e72299894623b4
  languageName: node
  linkType: hard

"msgpackr@npm:^1.11.2":
  version: 1.11.2
  resolution: "msgpackr@npm:1.11.2"
  dependencies:
    msgpackr-extract: "npm:^3.0.2"
  dependenciesMeta:
    msgpackr-extract:
      optional: true
  checksum: 10c0/7d2e81ca82c397b2352d470d6bc8f4a967fe4fe14f8fc1fc9906b23009fdfb543999b1ad29c700b8861581e0b6bf903d6f0fefb69a09375cbca6d4d802e6c906
  languageName: node
  linkType: hard

"multicast-dns@npm:^7.2.5":
  version: 7.2.5
  resolution: "multicast-dns@npm:7.2.5"
  dependencies:
    dns-packet: "npm:^5.2.2"
    thunky: "npm:^1.0.2"
  bin:
    multicast-dns: cli.js
  checksum: 10c0/5120171d4bdb1577764c5afa96e413353bff530d1b37081cb29cccc747f989eb1baf40574fe8e27060fc1aef72b59c042f72b9b208413de33bcf411343c69057
  languageName: node
  linkType: hard

"mute-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "mute-stream@npm:1.0.0"
  checksum: 10c0/dce2a9ccda171ec979a3b4f869a102b1343dee35e920146776780de182f16eae459644d187e38d59a3d37adf85685e1c17c38cf7bfda7e39a9880f7a1d10a74c
  languageName: node
  linkType: hard

"mute-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "mute-stream@npm:2.0.0"
  checksum: 10c0/2cf48a2087175c60c8dcdbc619908b49c07f7adcfc37d29236b0c5c612d6204f789104c98cc44d38acab7b3c96f4a3ec2cfdc4934d0738d876dbefa2a12c69f4
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"napi-build-utils@npm:^2.0.0":
  version: 2.0.0
  resolution: "napi-build-utils@npm:2.0.0"
  checksum: 10c0/5833aaeb5cc5c173da47a102efa4680a95842c13e0d9cc70428bd3ee8d96bb2172f8860d2811799b5daa5cbeda779933601492a2028a6a5351c6d0fcf6de83db
  languageName: node
  linkType: hard

"native-run@npm:^2.0.0, native-run@npm:^2.0.1":
  version: 2.0.1
  resolution: "native-run@npm:2.0.1"
  dependencies:
    "@ionic/utils-fs": "npm:^3.1.7"
    "@ionic/utils-terminal": "npm:^2.3.4"
    bplist-parser: "npm:^0.3.2"
    debug: "npm:^4.3.4"
    elementtree: "npm:^0.1.7"
    ini: "npm:^4.1.1"
    plist: "npm:^3.1.0"
    split2: "npm:^4.2.0"
    through2: "npm:^4.0.2"
    tslib: "npm:^2.6.2"
    yauzl: "npm:^2.10.0"
  bin:
    native-run: bin/native-run
  checksum: 10c0/332877c49a852210bea34a9a8dc8b59f77d03bfcbf70e044dabe0122bcb3e6972f196951160cd2258e19dbf47840d3225e978261ece40e5093af8d2a77b6afa2
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"needle@npm:^3.1.0":
  version: 3.3.1
  resolution: "needle@npm:3.3.1"
  dependencies:
    iconv-lite: "npm:^0.6.3"
    sax: "npm:^1.2.4"
  bin:
    needle: bin/needle
  checksum: 10c0/233b9315d47b735867d03e7a018fb665ee6cacf3a83b991b19538019cf42b538a3e85ca745c840b4c5e9a0ffdca76472f941363bf7c166214ae8cbc650fd4d39
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"node-abi@npm:^3.3.0":
  version: 3.75.0
  resolution: "node-abi@npm:3.75.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c43a2409407df3737848fd96202b0a49e15039994aecce963969e9ef7342a8fc544aba94e0bfd8155fb9de5f5fe9a4b6ccad8bf509e7c46caf096fc4491d63f2
  languageName: node
  linkType: hard

"node-addon-api@npm:^6.1.0":
  version: 6.1.0
  resolution: "node-addon-api@npm:6.1.0"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/d2699c4ad15740fd31482a3b6fca789af7723ab9d393adc6ac45250faaee72edad8f0b10b2b9d087df0de93f1bdc16d97afdd179b26b9ebc9ed68b569faa4bac
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/fb32a206276d608037fa1bcd7e9921e177fe992fc610d098aa3128baca3c0050fc1e014fa007e9b3874cf865ddb4f5bd9f43ccb7cbbbe4efaff6a83e920b17e9
  languageName: node
  linkType: hard

"node-fetch@npm:2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-forge@npm:^1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 10c0/e882819b251a4321f9fc1d67c85d1501d3004b4ee889af822fd07f64de3d1a8e272ff00b689570af0465d65d6bf5074df9c76e900e0aff23e60b847f2a46fbe8
  languageName: node
  linkType: hard

"node-gyp-build-optional-packages@npm:5.2.2":
  version: 5.2.2
  resolution: "node-gyp-build-optional-packages@npm:5.2.2"
  dependencies:
    detect-libc: "npm:^2.0.1"
  bin:
    node-gyp-build-optional-packages: bin.js
    node-gyp-build-optional-packages-optional: optional.js
    node-gyp-build-optional-packages-test: build-test.js
  checksum: 10c0/c81128c6f91873381be178c5eddcbdf66a148a6a89a427ce2bcd457593ce69baf2a8662b6d22cac092d24aa9c43c230dec4e69b3a0da604503f4777cd77e282b
  languageName: node
  linkType: hard

"node-gyp@npm:^11.0.0, node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-html-parser@npm:5.4.2":
  version: 5.4.2
  resolution: "node-html-parser@npm:5.4.2"
  dependencies:
    css-select: "npm:^4.2.1"
    he: "npm:1.2.0"
  checksum: 10c0/5a46ce4dc29dcb656067a977ef977d09328b21d1e26e6105176230bb151970cf7deb2db0dd084abeb98106ac79a83102232ad0d9a45d0a686f3eb6931a048663
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.3.2, normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: "npm:^2.1.4"
    resolve: "npm:^1.10.0"
    semver: "npm:2 || 3 || 4 || 5"
    validate-npm-package-license: "npm:^3.0.1"
  checksum: 10c0/357cb1646deb42f8eb4c7d42c4edf0eec312f3628c2ef98501963cc4bbe7277021b2b1d977f982b2edce78f5a1014613ce9cf38085c3df2d76730481357ca504
  languageName: node
  linkType: hard

"normalize-package-data@npm:^3.0.0":
  version: 3.0.3
  resolution: "normalize-package-data@npm:3.0.3"
  dependencies:
    hosted-git-info: "npm:^4.0.1"
    is-core-module: "npm:^2.5.0"
    semver: "npm:^7.3.4"
    validate-npm-package-license: "npm:^3.0.1"
  checksum: 10c0/e5d0f739ba2c465d41f77c9d950e291ea4af78f8816ddb91c5da62257c40b76d8c83278b0d08ffbcd0f187636ebddad20e181e924873916d03e6e5ea2ef026be
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 10c0/bf39b73a63e0a42ad1a48c2bd1bda5a07ede64a7e2567307a407674e595bcff0fa0d57e8e5f1e7fa5e91000797c7615e13613227aaaa4d6d6e87f5bd5cc95de6
  languageName: node
  linkType: hard

"npm-bundled@npm:^4.0.0":
  version: 4.0.0
  resolution: "npm-bundled@npm:4.0.0"
  dependencies:
    npm-normalize-package-bin: "npm:^4.0.0"
  checksum: 10c0/e6e20caefbc6a41138d3767ec998f6a2cf55f33371c119417a556ff6052390a2ffeb3b465a74aea127fb211ddfcb7db776620faf12b64e48e60e332b25b5b8a0
  languageName: node
  linkType: hard

"npm-install-checks@npm:^7.1.0":
  version: 7.1.1
  resolution: "npm-install-checks@npm:7.1.1"
  dependencies:
    semver: "npm:^7.1.1"
  checksum: 10c0/3cfd705ef3f70add31a32b4a5462d16e0f06d9df636072483fb43c854414a1cc128f496e84a8d9c12c1f1820307b7a3c275643589c564dac3c870eb636f8eea4
  languageName: node
  linkType: hard

"npm-normalize-package-bin@npm:^4.0.0":
  version: 4.0.0
  resolution: "npm-normalize-package-bin@npm:4.0.0"
  checksum: 10c0/1fa546fcae8eaab61ef9b9ec237b6c795008da50e1883eae030e9e38bb04ffa32c5aabcef9a0400eae3dc1f91809bcfa85e437ce80d677c69b419d1d9cacf0ab
  languageName: node
  linkType: hard

"npm-package-arg@npm:12.0.2, npm-package-arg@npm:^12.0.0":
  version: 12.0.2
  resolution: "npm-package-arg@npm:12.0.2"
  dependencies:
    hosted-git-info: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    validate-npm-package-name: "npm:^6.0.0"
  checksum: 10c0/a507046ca0999862d6f1a4878d2e22d47a728062b49d670ea7a965b0b555fc84ba4473daf34eb72c711b68aeb02e4f567fdb410d54385535cb7e4d85aaf49544
  languageName: node
  linkType: hard

"npm-packlist@npm:^9.0.0":
  version: 9.0.0
  resolution: "npm-packlist@npm:9.0.0"
  dependencies:
    ignore-walk: "npm:^7.0.0"
  checksum: 10c0/3eb9e877fff81ed1f97b86a387a13a7d0136a26c4c21d8fab7e49be653e71d604ba63091ec80e3a0b1d1fd879639eab91ddda1a8df45d7631795b83911f2f9b8
  languageName: node
  linkType: hard

"npm-pick-manifest@npm:10.0.0, npm-pick-manifest@npm:^10.0.0":
  version: 10.0.0
  resolution: "npm-pick-manifest@npm:10.0.0"
  dependencies:
    npm-install-checks: "npm:^7.1.0"
    npm-normalize-package-bin: "npm:^4.0.0"
    npm-package-arg: "npm:^12.0.0"
    semver: "npm:^7.3.5"
  checksum: 10c0/946e791f6164a04dbc3340749cd7521d4d1f60accb2d0ca901375314b8425c8a12b34b4b70e2850462cc898fba5fa8d1f283221bf788a1d37276f06a85c4562a
  languageName: node
  linkType: hard

"npm-registry-fetch@npm:^18.0.0":
  version: 18.0.2
  resolution: "npm-registry-fetch@npm:18.0.2"
  dependencies:
    "@npmcli/redact": "npm:^3.0.0"
    jsonparse: "npm:^1.3.1"
    make-fetch-happen: "npm:^14.0.0"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minizlib: "npm:^3.0.1"
    npm-package-arg: "npm:^12.0.0"
    proc-log: "npm:^5.0.0"
  checksum: 10c0/43e02befb393f67d5014d690a96d55f0b5f837a3eb9a79b17738ff0e3a1f081968480f2f280d1ad77a088ebd88c196793d929b0e4d24a8389a324dfd4006bc39
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"object-assign@npm:^4":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/cd4327e6c3369cfa805deb4cbbe919bfb7d3aeebf0bcaba291bb568ea7169f8f8cdbcabe2f00b40db0c20cd20f08e11b5f3a5a36fb7dd3fe04850c50db3bf83b
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
  checksum: 10c0/60d0455c85c736fbfeda0217d1a77525956f76f7b2495edeca9e9bbf8168a45783199e77b894d30638837c654d0cc410e0e02cbfcf445bc8de71c3da1ede6a9c
  languageName: node
  linkType: hard

"object.values@npm:^1.2.0":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 10c0/520aaac7ea701618eacf000fc96ae458e20e13b0569845800fc582f81b386731ab22d55354b4915d58171db00e79cfcd09c1638c02f89577ef092b38c65b7d81
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1, on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/c904f9e518b11941eb60279a3cbfaf1289bd0001f600a950255b1dede9fe3df8cd74f38483550b3bb9485165166acb5db500c3b4c4337aec2815c88c96fcc2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10c0/f649e65c197bf31505a4c0444875db0258e198292f34b884d73c2f751e91792ef96bb5cf89aa0f4fecc2e4dc662461dda606b1274b0e564f539cae5d2f5fc32f
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"onetime@npm:^7.0.0":
  version: 7.0.0
  resolution: "onetime@npm:7.0.0"
  dependencies:
    mimic-function: "npm:^5.0.0"
  checksum: 10c0/5cb9179d74b63f52a196a2e7037ba2b9a893245a5532d3f44360012005c9cadb60851d56716ebff18a6f47129dab7168022445df47c2aff3b276d92585ed1221
  languageName: node
  linkType: hard

"open@npm:10.1.0, open@npm:^10.0.3":
  version: 10.1.0
  resolution: "open@npm:10.1.0"
  dependencies:
    default-browser: "npm:^5.2.1"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^3.1.0"
  checksum: 10c0/c86d0b94503d5f735f674158d5c5d339c25ec2927562f00ee74590727292ed23e1b8d9336cb41ffa7e1fa4d3641d29b199b4ea37c78cb557d72b511743e90ebb
  languageName: node
  linkType: hard

"open@npm:^8.4.0":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"ora@npm:5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: "npm:^4.1.0"
    chalk: "npm:^4.1.0"
    cli-cursor: "npm:^3.1.0"
    cli-spinners: "npm:^2.5.0"
    is-interactive: "npm:^1.0.0"
    is-unicode-supported: "npm:^0.1.0"
    log-symbols: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/10ff14aace236d0e2f044193362b22edce4784add08b779eccc8f8ef97195cae1248db8ec1ec5f5ff076f91acbe573f5f42a98c19b78dba8c54eefff983cae85
  languageName: node
  linkType: hard

"ordered-binary@npm:^1.5.3":
  version: 1.5.3
  resolution: "ordered-binary@npm:1.5.3"
  checksum: 10c0/2b67c90c79071f54344762fcecac256c3c6fe02a3ce1d349c7cab38a55a6137320b13022d6dd26faac462d887f48a32e04693a3ae30592185f290c793b92de03
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10c0/f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: "npm:^1.0.0"
  checksum: 10c0/5c1b1d53d180b2c7501efb04b7c817448e10efe1ba46f4783f8951994d5027e4cd88f36ad79af50546682594c4ebd11702ac4b9364c47f8074890e2acad0edee
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-limit@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-limit@npm:4.0.0"
  dependencies:
    yocto-queue: "npm:^1.0.0"
  checksum: 10c0/a56af34a77f8df2ff61ddfb29431044557fcbcb7642d5a3233143ebba805fc7306ac1d448de724352861cb99de934bc9ab74f0d16fe6a5460bdbdf938de875ad
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: "npm:^1.1.0"
  checksum: 10c0/82da4be88fb02fd29175e66021610c881938d3cc97c813c71c1a605fac05617d57fd5d3b337494a6106c0edb2a37c860241430851411f1b265108cead34aee67
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-locate@npm:^6.0.0":
  version: 6.0.0
  resolution: "p-locate@npm:6.0.0"
  dependencies:
    p-limit: "npm:^4.0.0"
  checksum: 10c0/d72fa2f41adce59c198270aa4d3c832536c87a1806e0f69dffb7c1a7ca998fb053915ca833d90f166a8c082d3859eabfed95f01698a3214c20df6bb8de046312
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-retry@npm:^6.2.0":
  version: 6.2.1
  resolution: "p-retry@npm:6.2.1"
  dependencies:
    "@types/retry": "npm:0.12.2"
    is-network-error: "npm:^1.0.0"
    retry: "npm:^0.13.1"
  checksum: 10c0/10d014900107da2c7071ad60fffe4951675f09930b7a91681643ea224ae05649c05001d9e78436d902fe8b116d520dd1f60e72e091de097e2640979d56f3fb60
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 10c0/757ba31de5819502b80c447826fac8be5f16d3cb4fbf9bc8bc4971dba0682e84ac33e4b24176ca7058c69e29f64f34d8d9e9b08e873b7b7bb0aa89d620fa224a
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pacote@npm:20.0.0":
  version: 20.0.0
  resolution: "pacote@npm:20.0.0"
  dependencies:
    "@npmcli/git": "npm:^6.0.0"
    "@npmcli/installed-package-contents": "npm:^3.0.0"
    "@npmcli/package-json": "npm:^6.0.0"
    "@npmcli/promise-spawn": "npm:^8.0.0"
    "@npmcli/run-script": "npm:^9.0.0"
    cacache: "npm:^19.0.0"
    fs-minipass: "npm:^3.0.0"
    minipass: "npm:^7.0.2"
    npm-package-arg: "npm:^12.0.0"
    npm-packlist: "npm:^9.0.0"
    npm-pick-manifest: "npm:^10.0.0"
    npm-registry-fetch: "npm:^18.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    sigstore: "npm:^3.0.0"
    ssri: "npm:^12.0.0"
    tar: "npm:^6.1.11"
  bin:
    pacote: bin/index.js
  checksum: 10c0/435c385446ecc81b1eb1584f4fa3cb102e630a22877f39b5c1a92eddfeaf222bd027b205e32632be2801e3bcbe525165cdffb5ceca5c13bbc81f8132fe1ba49e
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-imports@npm:^2.1.1":
  version: 2.2.1
  resolution: "parse-imports@npm:2.2.1"
  dependencies:
    es-module-lexer: "npm:^1.5.3"
    slashes: "npm:^3.0.12"
  checksum: 10c0/bc541ce4ef2ff77d53247de39a956e0ee7a1a4b9b175c3e0f898222fe7994595f011491154db4ed408cbaf5049ede9d0b6624125565be208e973a54420cbe069
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: "npm:^1.3.1"
    json-parse-better-errors: "npm:^1.0.1"
  checksum: 10c0/8d80790b772ccb1bcea4e09e2697555e519d83d04a77c2b4237389b813f82898943a93ffff7d0d2406203bdd0c30dcf95b1661e3a53f83d0e417f053957bef32
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-node-version@npm:^1.0.1":
  version: 1.0.1
  resolution: "parse-node-version@npm:1.0.1"
  checksum: 10c0/999cd3d7da1425c2e182dce82b226c6dc842562d3ed79ec47f5c719c32a7f6c1a5352495b894fc25df164be7f2ede4224758255da9902ddef81f2b77ba46bb2c
  languageName: node
  linkType: hard

"parse5-html-rewriting-stream@npm:7.0.0":
  version: 7.0.0
  resolution: "parse5-html-rewriting-stream@npm:7.0.0"
  dependencies:
    entities: "npm:^4.3.0"
    parse5: "npm:^7.0.0"
    parse5-sax-parser: "npm:^7.0.0"
  checksum: 10c0/658d3e2bae038e515bcce6ab6fba9484332d641f3ba82a6450649e1105492fe0a353101dbf751bddfc063509d06b55260bd4567970df3eaaa8391ae79d25ffbf
  languageName: node
  linkType: hard

"parse5-sax-parser@npm:^7.0.0":
  version: 7.0.0
  resolution: "parse5-sax-parser@npm:7.0.0"
  dependencies:
    parse5: "npm:^7.0.0"
  checksum: 10c0/6b4184354f5ee75c2ec16ab4c7f4703e40d710375ed6c08f82aa425cda22b7ba4a2f43a0925bc6eb6fc88610ab0877693a8d9e10f5a0c21504fd6f97dbd130e9
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10c0/829d37a0c709215a887e410a7118d754f8e1afd7edb529db95bc7bbf8045fb0266a7b67801331d8e8d9d073ea75793624ec27ce9ff3b96862c3b9008f4d68e80
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 10c0/17d6a5664bc0a11d48e2b2127d28a0e58822c6740bde30403f08013da599182289c56518bec89407e3f31d3c2b6b296a4220bc3f867f0911fee6952208b04167
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-exists@npm:^5.0.0":
  version: 5.0.0
  resolution: "path-exists@npm:5.0.0"
  checksum: 10c0/b170f3060b31604cde93eefdb7392b89d832dfbc1bed717c9718cbe0f230c1669b7e75f87e19901da2250b84d092989a0f9e44d2ef41deb09aa3ad28e691a40a
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1, path-scurry@npm:^1.6.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-scurry@npm:^2.0.0":
  version: 2.0.0
  resolution: "path-scurry@npm:2.0.0"
  dependencies:
    lru-cache: "npm:^11.0.0"
    minipass: "npm:^7.1.2"
  checksum: 10c0/3da4adedaa8e7ef8d6dc4f35a0ff8f05a9b4d8365f2b28047752b62d4c1ad73eec21e37b1579ef2d075920157856a3b52ae8309c480a6f1a8bbe06ff8e52b33c
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"path-type@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-type@npm:3.0.0"
  dependencies:
    pify: "npm:^3.0.0"
  checksum: 10c0/1332c632f1cac15790ebab8dd729b67ba04fc96f81647496feb1c2975d862d046f41e4b975dbd893048999b2cc90721f72924ad820acc58c78507ba7141a8e56
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"path-type@npm:^6.0.0":
  version: 6.0.0
  resolution: "path-type@npm:6.0.0"
  checksum: 10c0/55baa8b1187d6dc683d5a9cfcc866168d6adff58e5db91126795376d818eee46391e00b2a4d53e44d844c7524a7d96aa68cc68f4f3e500d3d069a39e6535481c
  languageName: node
  linkType: hard

"pend@npm:~1.2.0":
  version: 1.2.0
  resolution: "pend@npm:1.2.0"
  checksum: 10c0/8a87e63f7a4afcfb0f9f77b39bb92374afc723418b9cb716ee4257689224171002e07768eeade4ecd0e86f1fa3d8f022994219fb45634f2dbd78c6803e452458
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:4.0.2, picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10c0/551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 10c0/fead19ed9d801f1b1fcd0638a1ac53eabbb0945bf615f2f8806a8b646565a04a1b0e7ef115c951d225f042cca388fdc1cd3add46d10d1ed6951c20bd2998af10
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10c0/6f9d404b0d47a965437403c9b90eca8bb2536407f03de165940e62e72c8c8b75adda5516c6b9b23675a5877cc0bcac6bdfb0ef0e39414cd2476d5495da40e7cf
  languageName: node
  linkType: hard

"piscina@npm:4.8.0":
  version: 4.8.0
  resolution: "piscina@npm:4.8.0"
  dependencies:
    "@napi-rs/nice": "npm:^1.0.1"
  dependenciesMeta:
    "@napi-rs/nice":
      optional: true
  checksum: 10c0/963ee0dc0862e936c88357b21b0b4fa32407ab21e9600756504411f368dcfae7478c8a19e13d0dd8afed56a8252a8e5967ee4413aa33dd436751b7ee2804531e
  languageName: node
  linkType: hard

"pkg-dir@npm:^7.0.0":
  version: 7.0.0
  resolution: "pkg-dir@npm:7.0.0"
  dependencies:
    find-up: "npm:^6.3.0"
  checksum: 10c0/1afb23d2efb1ec9d8b2c4a0c37bf146822ad2774f074cb05b853be5dca1b40815c5960dd126df30ab8908349262a266f31b771e877235870a3b8fd313beebec5
  languageName: node
  linkType: hard

"plist@npm:^3.0.4, plist@npm:^3.0.5, plist@npm:^3.1.0":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.5.1"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/db19ba50faafc4103df8e79bcd6b08004a56db2a9dd30b3e5c8b0ef30398ef44344a674e594d012c8fc39e539a2b72cb58c60a76b4b4401cbbc7c8f6b028d93d
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss-loader@npm:8.1.1":
  version: 8.1.1
  resolution: "postcss-loader@npm:8.1.1"
  dependencies:
    cosmiconfig: "npm:^9.0.0"
    jiti: "npm:^1.20.0"
    semver: "npm:^7.5.4"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    postcss: ^7.0.0 || ^8.0.1
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: 10c0/86cde94cd4c7c39892ef9bd4bf09342f422a21789654038694cf2b23c37c0ed9550c73608f656426a6631f0ade1eca82022781831e93d5362afe2f191388b85e
  languageName: node
  linkType: hard

"postcss-media-query-parser@npm:^0.2.3":
  version: 0.2.3
  resolution: "postcss-media-query-parser@npm:0.2.3"
  checksum: 10c0/252c8cf24f0e9018516b0d70b7b3d6f5b52e81c4bab2164b49a4e4c1b87bb11f5dbe708c0076990665cb24c70d5fd2f3aee9c922b0f67c7c619e051801484688
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.1.0":
  version: 3.1.0
  resolution: "postcss-modules-extract-imports@npm:3.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/402084bcab376083c4b1b5111b48ec92974ef86066f366f0b2d5b2ac2b647d561066705ade4db89875a13cb175b33dd6af40d16d32b2ea5eaf8bac63bd2bf219
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.5":
  version: 4.2.0
  resolution: "postcss-modules-local-by-default@npm:4.2.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
    postcss-selector-parser: "npm:^7.0.0"
    postcss-value-parser: "npm:^4.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/b0b83feb2a4b61f5383979d37f23116c99bc146eba1741ca3cf1acca0e4d0dbf293ac1810a6ab4eccbe1ee76440dd0a9eb2db5b3bba4f99fc1b3ded16baa6358
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.2.0":
  version: 3.2.1
  resolution: "postcss-modules-scope@npm:3.2.1"
  dependencies:
    postcss-selector-parser: "npm:^7.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/bd2d81f79e3da0ef6365b8e2c78cc91469d05b58046b4601592cdeef6c4050ed8fe1478ae000a1608042fc7e692cb51fecbd2d9bce3f4eace4d32e883ffca10b
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/dd18d7631b5619fb9921b198c86847a2a075f32e0c162e0428d2647685e318c487a2566cc8cc669fc2077ef38115cde7a068e321f46fb38be3ad49646b639dbc
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^7.0.0":
  version: 7.1.0
  resolution: "postcss-selector-parser@npm:7.1.0"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/0fef257cfd1c0fe93c18a3f8a6e739b4438b527054fd77e9a62730a89b2d0ded1b59314a7e4aaa55bc256204f40830fecd2eb50f20f8cb7ab3a10b52aa06c8aa
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:8.5.2, postcss@npm:^8.2.14, postcss@npm:^8.4.33, postcss@npm:^8.4.49":
  version: 8.5.2
  resolution: "postcss@npm:8.5.2"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/3044d49bc725029ab62292e8bf9849741251b95f3b754e191bf8b4025414d40ec3b4ac05c5a563d4b50060b5c8e96683eb4d783d8d8fa3867eb7b763cbe66127
  languageName: node
  linkType: hard

"postcss@npm:^8.5.3":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/b75510d7b28c3ab728c8733dd01538314a18c52af426f199a3c9177e63eb08602a3938bfb66b62dc01350b9aed62087eabbf229af97a1659eb8d3513cec823b3
  languageName: node
  linkType: hard

"prebuild-install@npm:^7.1.1":
  version: 7.1.3
  resolution: "prebuild-install@npm:7.1.3"
  dependencies:
    detect-libc: "npm:^2.0.0"
    expand-template: "npm:^2.0.3"
    github-from-package: "npm:0.0.0"
    minimist: "npm:^1.2.3"
    mkdirp-classic: "npm:^0.5.3"
    napi-build-utils: "npm:^2.0.0"
    node-abi: "npm:^3.3.0"
    pump: "npm:^3.0.0"
    rc: "npm:^1.2.7"
    simple-get: "npm:^4.0.0"
    tar-fs: "npm:^2.0.0"
    tunnel-agent: "npm:^0.6.0"
  bin:
    prebuild-install: bin.js
  checksum: 10c0/25919a42b52734606a4036ab492d37cfe8b601273d8dfb1fa3c84e141a0a475e7bad3ab848c741d2f810cef892fcf6059b8c7fe5b29f98d30e0c29ad009bedff
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier@npm:>=2.4.0, prettier@npm:^2.7.1":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 10c0/463ea8f9a0946cd5b828d8cf27bd8b567345cf02f56562d5ecde198b91f47a76b7ac9eae0facd247ace70e927143af6135e8cf411986b8cb8478784a4d6d724a
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 10c0/5b9272c602e4f4472a215e58daff88f802923b84bc39c8860376bb1c0e42aaf18c25d69ad974bd06ec6db6f544b783edecd5502cd3d184748d99080d68e4be5f
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"punycode@npm:^1.4.1":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: 10c0/354b743320518aef36f77013be6e15da4db24c2b4f62c5f1eb0529a6ed02fbaf1cb52925785f6ab85a962f2b590d9cd5ad730b70da72b5f180e2556b8bd3ca08
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"q@npm:^1.5.1":
  version: 1.5.1
  resolution: "q@npm:1.5.1"
  checksum: 10c0/7855fbdba126cb7e92ef3a16b47ba998c0786ec7fface236e3eb0135b65df36429d91a86b1fff3ab0927b4ac4ee88a2c44527c7c3b8e2a37efbec9fe34803df4
  languageName: node
  linkType: hard

"qjobs@npm:^1.2.0":
  version: 1.2.0
  resolution: "qjobs@npm:1.2.0"
  checksum: 10c0/772207772b856a3b1ec673b11a6cda074f1b82821644f2d042504b438ea3ea1fe918555547491e717e8694ec105379fe5139fc5ddd7937b21f7712bb648ed01d
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-lru@npm:^4.0.1":
  version: 4.0.1
  resolution: "quick-lru@npm:4.0.1"
  checksum: 10c0/f9b1596fa7595a35c2f9d913ac312fede13d37dc8a747a51557ab36e11ce113bbe88ef4c0154968845559a7709cb6a7e7cbe75f7972182451cd45e7f057a334d
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"rc@npm:^1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"read-pkg-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "read-pkg-up@npm:3.0.0"
  dependencies:
    find-up: "npm:^2.0.0"
    read-pkg: "npm:^3.0.0"
  checksum: 10c0/2cd0a180260b0d235990e6e9c8c2330a03882d36bc2eba8930e437ef23ee52a68a894e7e1ccb1c33f03bcceb270a861ee5f7eac686f238857755e2cddfb48ffd
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: "npm:^4.1.0"
    read-pkg: "npm:^5.2.0"
    type-fest: "npm:^0.8.1"
  checksum: 10c0/82b3ac9fd7c6ca1bdc1d7253eb1091a98ff3d195ee0a45386582ce3e69f90266163c34121e6a0a02f1630073a6c0585f7880b3865efcae9c452fa667f02ca385
  languageName: node
  linkType: hard

"read-pkg@npm:^3.0.0":
  version: 3.0.0
  resolution: "read-pkg@npm:3.0.0"
  dependencies:
    load-json-file: "npm:^4.0.0"
    normalize-package-data: "npm:^2.3.2"
    path-type: "npm:^3.0.0"
  checksum: 10c0/65acf2df89fbcd506b48b7ced56a255ba00adf7ecaa2db759c86cc58212f6fd80f1f0b7a85c848551a5d0685232e9b64f45c1fd5b48d85df2761a160767eeb93
  languageName: node
  linkType: hard

"read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": "npm:^2.4.0"
    normalize-package-data: "npm:^2.5.0"
    parse-json: "npm:^5.0.0"
    type-fest: "npm:^0.6.0"
  checksum: 10c0/b51a17d4b51418e777029e3a7694c9bd6c578a5ab99db544764a0b0f2c7c0f58f8a6bc101f86a6fceb8ba6d237d67c89acf6170f6b98695d0420ddc86cf109fb
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.0.0, readable-stream@npm:^3.0.6, readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10c0/60a14f7619dec48c9c850255cd523e2717001b0e179dc7037cfa0895da7b9e9ab07532d324bfb118d73a710887d1e35f79c495fa91582784493e085d18c72c62
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: "npm:^4.0.0"
    strip-indent: "npm:^3.0.0"
  checksum: 10c0/d64a6b5c0b50eb3ddce3ab770f866658a2b9998c678f797919ceb1b586bab9259b311407280bd80b804e2a7c7539b19238ae6a2a20c843f1a7fcff21d48c2eae
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.2.0":
  version: 0.2.2
  resolution: "reflect-metadata@npm:0.2.2"
  checksum: 10c0/1cd93a15ea291e420204955544637c264c216e7aac527470e393d54b4bb075f10a17e60d8168ec96600c7e0b9fcc0cb0bb6e91c3fbf5b0d8c9056f04e6ac1ec2
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": "npm:^7.8.4"
  checksum: 10c0/7cfe6931ec793269701994a93bab89c0cc95379191fad866270a7fea2adfec67ea62bb5b374db77058b60ba4509319d9b608664d0d288bd9989ca8dbd08fae90
  languageName: node
  linkType: hard

"regex-parser@npm:^2.2.11":
  version: 2.3.1
  resolution: "regex-parser@npm:2.3.1"
  checksum: 10c0/a256f79c8b465e6765eb65799417200f8ee81f68cc202cc5563a02713e61ad51f6280672f8edee072ef37c5301a90f8d1a71cefb6ec3ed2ca0d1d88587286219
  languageName: node
  linkType: hard

"regexp-to-ast@npm:0.5.0":
  version: 0.5.0
  resolution: "regexp-to-ast@npm:0.5.0"
  checksum: 10c0/16d3c3905fb24866c3bff689ab177c1e63a7283a3cd1ba95987ef86020526f9827f5c60794197311f0e8a967889131142fe7a2e5ed3523ffe2ac9f55052e1566
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"replace@npm:^1.1.0":
  version: 1.2.2
  resolution: "replace@npm:1.2.2"
  dependencies:
    chalk: "npm:2.4.2"
    minimatch: "npm:3.0.5"
    yargs: "npm:^15.3.1"
  bin:
    replace: bin/replace.js
    search: bin/search.js
  checksum: 10c0/5b7a27a942556017dd3d97772110f5e8b6d4a966dfef10a59ed5534b5e926a2b6872557a8db5218918fdc1956ae9cb2a14be425f2782a36fb23a3dc990e6c921
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10c0/db91467d9ead311b4111cbd73a4e67fa7820daed2989a32f7023785a2659008c6d119752d9c4ac011ae07e537eb86523adff99804c5fdb39cd3a017f9b401bb6
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: 10c0/b2bfdd09db16c082c4326e573a82c0771daaf7b53b9ce8ad60ea46aa6e30aaf475fe9b164800b89f93b748d2c234d8abff945d2551ba47bf5698e04cd7713267
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-url-loader@npm:5.0.0":
  version: 5.0.0
  resolution: "resolve-url-loader@npm:5.0.0"
  dependencies:
    adjust-sourcemap-loader: "npm:^4.0.0"
    convert-source-map: "npm:^1.7.0"
    loader-utils: "npm:^2.0.0"
    postcss: "npm:^8.2.14"
    source-map: "npm:0.6.1"
  checksum: 10c0/53eef3620332f2fc35a4deffaa4395064b2ffd1bc28be380faa3f1e99c2fb7bbf0f705700b4539387d5b6c39586df54a92cd5d031606f19de4bf9e0ff1b6a522
  languageName: node
  linkType: hard

"resolve@npm:1.22.10, resolve@npm:^1.10.0, resolve@npm:^1.14.2, resolve@npm:^1.22.4":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.10.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"restore-cursor@npm:^5.0.0":
  version: 5.1.0
  resolution: "restore-cursor@npm:5.1.0"
  dependencies:
    onetime: "npm:^7.0.0"
    signal-exit: "npm:^4.1.0"
  checksum: 10c0/c2ba89131eea791d1b25205bdfdc86699767e2b88dee2a590b1a6caa51737deac8bad0260a5ded2f7c074b7db2f3a626bcf1fcf3cdf35974cbeea5e2e6764f60
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10c0/9ae822ee19db2163497e074ea919780b1efa00431d197c7afdb950e42bf109196774b92a49fc9821f0b8b328a98eea6017410bfc5e8a0fc19c85c6d11adb3772
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0, rfdc@npm:^1.4.1":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 10c0/4614e4292356cafade0b6031527eea9bc90f2372a22c012313be1dcc69a3b90c7338158b414539be863fa95bfcb2ddcd0587be696841af4e6679d85e62c060c7
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:^4.4.1":
  version: 4.4.1
  resolution: "rimraf@npm:4.4.1"
  dependencies:
    glob: "npm:^9.2.0"
  bin:
    rimraf: dist/cjs/src/bin.js
  checksum: 10c0/8c5e142d26d8b222be9dc9a1a41ba48e95d8f374e813e66a8533e87c6180174fcb3f573b9b592eca12740ebf8b78526d136acd971d4a790763d6f2232c34fa24
  languageName: node
  linkType: hard

"rimraf@npm:^6.0.1":
  version: 6.0.1
  resolution: "rimraf@npm:6.0.1"
  dependencies:
    glob: "npm:^11.0.0"
    package-json-from-dist: "npm:^1.0.0"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/b30b6b072771f0d1e73b4ca5f37bb2944ee09375be9db5f558fcd3310000d29dfcfa93cf7734d75295ad5a7486dc8e40f63089ced1722a664539ffc0c3ece8c6
  languageName: node
  linkType: hard

"rollup@npm:4.34.8, rollup@npm:^4.30.1":
  version: 4.34.8
  resolution: "rollup@npm:4.34.8"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.34.8"
    "@rollup/rollup-android-arm64": "npm:4.34.8"
    "@rollup/rollup-darwin-arm64": "npm:4.34.8"
    "@rollup/rollup-darwin-x64": "npm:4.34.8"
    "@rollup/rollup-freebsd-arm64": "npm:4.34.8"
    "@rollup/rollup-freebsd-x64": "npm:4.34.8"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.34.8"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.34.8"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-arm64-musl": "npm:4.34.8"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-x64-gnu": "npm:4.34.8"
    "@rollup/rollup-linux-x64-musl": "npm:4.34.8"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.34.8"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.34.8"
    "@rollup/rollup-win32-x64-msvc": "npm:4.34.8"
    "@types/estree": "npm:1.0.6"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/b9e711e33413112fbb761107c3fddc4561dfc74335c393542a829a85ccfb2763bfd17bf2422d84a2e9bee7646e5367018973e97005fdf64e49c2e209612f0eb6
  languageName: node
  linkType: hard

"run-applescript@npm:^7.0.0":
  version: 7.0.0
  resolution: "run-applescript@npm:7.0.0"
  checksum: 10c0/bd821bbf154b8e6c8ecffeaf0c33cebbb78eb2987476c3f6b420d67ab4c5301faa905dec99ded76ebb3a7042b4e440189ae6d85bbbd3fc6e8d493347ecda8bfe
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rxjs@npm:7.8.1":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/3c49c1ecd66170b175c9cacf5cef67f8914dcbc7cd0162855538d365c83fea631167cacb644b3ce533b2ea0e9a4d0b12175186985f89d75abe73dbd8f7f06f68
  languageName: node
  linkType: hard

"rxjs@npm:~7.8.0":
  version: 7.8.2
  resolution: "rxjs@npm:7.8.2"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/1fcd33d2066ada98ba8f21fcbbcaee9f0b271de1d38dc7f4e256bfbc6ffcdde68c8bfb69093de7eeb46f24b1fb820620bf0223706cff26b4ab99a7ff7b2e2c45
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sass-loader@npm:16.0.5":
  version: 16.0.5
  resolution: "sass-loader@npm:16.0.5"
  dependencies:
    neo-async: "npm:^2.6.2"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
    sass: ^1.3.0
    sass-embedded: "*"
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    node-sass:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    webpack:
      optional: true
  checksum: 10c0/216422b7b9e6e3f22739dc96887d883d2415f188d5c47631fd28c80608b5fae71167b26d0c74a1e917614e4d494fa73b1190ad5ca2f587c1afee84dc1d30f003
  languageName: node
  linkType: hard

"sass@npm:1.85.0":
  version: 1.85.0
  resolution: "sass@npm:1.85.0"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^5.0.2"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  dependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    sass: sass.js
  checksum: 10c0/a1af0c0596ae1904f66337d0c70a684db6e12210f97be4326cc3dcf18b0f956d7bc45ab2bcc7a8422d433d3eb3c9cb2cc8e60b2dafbdd01fb1ae5a23f5424690
  languageName: node
  linkType: hard

"sax@npm:1.1.4":
  version: 1.1.4
  resolution: "sax@npm:1.1.4"
  checksum: 10c0/805371d5c326f48e6b180e059315d9e97be8e38620538bc7d5debb05c1b2f6ffc14c5f26a50688d5e61a79949e2c8ff2781410de6718d3323137ae7e7a32cbe5
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0, sax@npm:^1.2.4":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10c0/6bf86318a254c5d898ede6bd3ded15daf68ae08a5495a2739564eb265cd13bcc64a07ab466fb204f67ce472bb534eb8612dac587435515169593f4fffa11de7c
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0, schema-utils@npm:^4.2.0, schema-utils@npm:^4.3.0":
  version: 4.3.0
  resolution: "schema-utils@npm:4.3.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10c0/c23f0fa73ef71a01d4a2bb7af4c91e0d356ec640e071aa2d06ea5e67f042962bb7ac7c29a60a295bb0125878801bc3209197a2b8a833dd25bd38e37c3ed21427
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: 10c0/01cc52edd29feddaf379efb4328aededa633f0ac43c64b11a8abd075ff34f05b0d280882c4fbcbdf1a0658202c9cd2ea8d5985174dcf9a2dac7e3a4996fa9b67
  languageName: node
  linkType: hard

"selfsigned@npm:^2.4.1":
  version: 2.4.1
  resolution: "selfsigned@npm:2.4.1"
  dependencies:
    "@types/node-forge": "npm:^1.3.0"
    node-forge: "npm:^1"
  checksum: 10c0/521829ec36ea042f7e9963bf1da2ed040a815cf774422544b112ec53b7edc0bc50a0f8cc2ae7aa6cc19afa967c641fd96a15de0fc650c68651e41277d2e1df09
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10c0/e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:7.7.1, semver@npm:^7.0.0, semver@npm:^7.1.1, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10c0/2dd09ef4b65a1289ba24a788b1423a035581bef60817bea1f01eda8e3bda623f86357665fe7ac1b50f6d4f583f97db9615b3f07b2a2e8cbcb75033965f771dd2
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: "npm:~1.3.4"
    batch: "npm:0.6.1"
    debug: "npm:2.6.9"
    escape-html: "npm:~1.0.3"
    http-errors: "npm:~1.6.2"
    mime-types: "npm:~2.1.17"
    parseurl: "npm:~1.3.2"
  checksum: 10c0/a666471a24196f74371edf2c3c7bcdd82adbac52f600804508754b5296c3567588bf694258b19e0cb23a567acfa20d9721bfdaed3286007b81f9741ada8a3a9c
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 10c0/a77b20876689c6a89c3b42f0c3596a9cae02f90fc902570cbd97198e9e8240382086c9303ad043e88cee10f61eae19f1004e51d885395a1e9bf49f9ebed12872
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10c0/7bab09613a1b9f480c85a9823aebec533015579fa055ba6634aa56ba1f984380670eaf33b8217502931872aa1401c9fcadaa15f9f604d631536df475b05bcf1e
  languageName: node
  linkType: hard

"sharp@npm:0.32.6":
  version: 0.32.6
  resolution: "sharp@npm:0.32.6"
  dependencies:
    color: "npm:^4.2.3"
    detect-libc: "npm:^2.0.2"
    node-addon-api: "npm:^6.1.0"
    node-gyp: "npm:latest"
    prebuild-install: "npm:^7.1.1"
    semver: "npm:^7.5.4"
    simple-get: "npm:^4.0.1"
    tar-fs: "npm:^3.0.4"
    tunnel-agent: "npm:^0.6.0"
  checksum: 10c0/f6a756fec5051ef2f9341e0543cde1da4e822982dd5398010baad92e2262bd177e08b753eb19b2fbee30f2fcb0e8756f24088fafc48293a364e9a8f8dc65a300
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.8.1":
  version: 1.8.2
  resolution: "shell-quote@npm:1.8.2"
  checksum: 10c0/85fdd44f2ad76e723d34eb72c753f04d847ab64e9f1f10677e3f518d0e5b0752a176fd805297b30bb8c3a1556ebe6e77d2288dbd7b7b0110c7e941e9e9c20ce1
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"sigstore@npm:^3.0.0":
  version: 3.1.0
  resolution: "sigstore@npm:3.1.0"
  dependencies:
    "@sigstore/bundle": "npm:^3.1.0"
    "@sigstore/core": "npm:^2.0.0"
    "@sigstore/protobuf-specs": "npm:^0.4.0"
    "@sigstore/sign": "npm:^3.1.0"
    "@sigstore/tuf": "npm:^3.1.0"
    "@sigstore/verify": "npm:^2.1.0"
  checksum: 10c0/c037f5526e698ec6de8654f6be6b6fa52bf52f2ffcd78109cdefc6d824bbb8390324522dcb0f84d57a674948ac53aef34dd77f9de66c91bcd91d0af56bb91c7e
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 10c0/62f7508e674414008910b5397c1811941d457dfa0db4fd5aa7fa0409eb02c3609608dfcd7508cace75b3a0bf67a2a77990711e32cd213d2c76f4fd12ee86d776
  languageName: node
  linkType: hard

"simple-get@npm:^4.0.0, simple-get@npm:^4.0.1":
  version: 4.0.1
  resolution: "simple-get@npm:4.0.1"
  dependencies:
    decompress-response: "npm:^6.0.0"
    once: "npm:^1.3.1"
    simple-concat: "npm:^1.0.0"
  checksum: 10c0/b0649a581dbca741babb960423248899203165769747142033479a7dc5e77d7b0fced0253c731cd57cf21e31e4d77c9157c3069f4448d558ebc96cf9e1eebcf0
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.3.1
  resolution: "simple-plist@npm:1.3.1"
  dependencies:
    bplist-creator: "npm:0.1.0"
    bplist-parser: "npm:0.3.1"
    plist: "npm:^3.0.5"
  checksum: 10c0/3d5adeb705815338b1f4615c52584d540b12575337a0e0688f0a2b19a6a4162769cd8a3a36e9eb2b0fc9e27d63dcba8b9088a13e93eabcb7cdec5fe90ec5b0a5
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slash@npm:^5.1.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 10c0/eb48b815caf0bdc390d0519d41b9e0556a14380f6799c72ba35caf03544d501d18befdeeef074bc9c052acf69654bc9e0d79d7f1de0866284137a40805299eb3
  languageName: node
  linkType: hard

"slashes@npm:^3.0.12":
  version: 3.0.12
  resolution: "slashes@npm:3.0.12"
  checksum: 10c0/71ca2a1fcd1ab6814b0fdb8cf9c33a3d54321deec2aa8d173510f0086880201446021a9b9e6a18561f7c472b69a2145977c6a8fb9c53a8ff7be31778f203d175
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10c0/6c25678db1270d4793e0327620f1e0f9f5bea4630123f51e9e399191bc52c87d6e6de53ed33538609e5eacbd1fab769fae00f3705d08d029f02102a540648918
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: "npm:^6.0.0"
    is-fullwidth-code-point: "npm:^4.0.0"
  checksum: 10c0/2d4d40b2a9d5cf4e8caae3f698fe24ae31a4d778701724f578e984dcb485ec8c49f0c04dab59c401821e80fcdfe89cace9c66693b0244e40ec485d72e543914f
  languageName: node
  linkType: hard

"slice-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "slice-ansi@npm:7.1.0"
  dependencies:
    ansi-styles: "npm:^6.2.1"
    is-fullwidth-code-point: "npm:^5.0.0"
  checksum: 10c0/631c971d4abf56cf880f034d43fcc44ff883624867bf11ecbd538c47343911d734a4656d7bc02362b40b89d765652a7f935595441e519b59e2ad3f4d5d6fe7ca
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socket.io-adapter@npm:~2.5.2":
  version: 2.5.5
  resolution: "socket.io-adapter@npm:2.5.5"
  dependencies:
    debug: "npm:~4.3.4"
    ws: "npm:~8.17.1"
  checksum: 10c0/04a5a2a9c4399d1b6597c2afc4492ab1e73430cc124ab02b09e948eabf341180b3866e2b61b5084cb899beb68a4db7c328c29bda5efb9207671b5cb0bc6de44e
  languageName: node
  linkType: hard

"socket.io-parser@npm:~4.2.4":
  version: 4.2.4
  resolution: "socket.io-parser@npm:4.2.4"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
  checksum: 10c0/9383b30358fde4a801ea4ec5e6860915c0389a091321f1c1f41506618b5cf7cd685d0a31c587467a0c4ee99ef98c2b99fb87911f9dfb329716c43b587f29ca48
  languageName: node
  linkType: hard

"socket.io@npm:^4.7.2":
  version: 4.8.1
  resolution: "socket.io@npm:4.8.1"
  dependencies:
    accepts: "npm:~1.3.4"
    base64id: "npm:~2.0.0"
    cors: "npm:~2.8.5"
    debug: "npm:~4.3.2"
    engine.io: "npm:~6.6.0"
    socket.io-adapter: "npm:~2.5.2"
    socket.io-parser: "npm:~4.2.4"
  checksum: 10c0/acf931a2bb235be96433b71da3d8addc63eeeaa8acabd33dc8d64e12287390a45f1e9f389a73cf7dc336961cd491679741b7a016048325c596835abbcc017ca9
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.24":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: "npm:^0.11.3"
    uuid: "npm:^8.3.2"
    websocket-driver: "npm:^0.7.4"
  checksum: 10c0/aa102c7d921bf430215754511c81ea7248f2dcdf268fbdb18e4d8183493a86b8793b164c636c52f474a886f747447c962741df2373888823271efdb9d2594f33
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-loader@npm:5.0.0":
  version: 5.0.0
  resolution: "source-map-loader@npm:5.0.0"
  dependencies:
    iconv-lite: "npm:^0.6.3"
    source-map-js: "npm:^1.0.2"
  peerDependencies:
    webpack: ^5.72.1
  checksum: 10c0/104c1c2620903e839adb4ec4f2356aa2184151a465855c9b8357aa4f2d215119b2917404c8746b19dd46fac4f2f0be3f69d56c32cb9ae6ba9b42eddd912944e7
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.21, source-map-support@npm:^0.5.5, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:0.6.1, source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"source-map@npm:0.7.4":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: "npm:^3.0.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 10c0/49208f008618b9119208b0dadc9208a3a55053f4fd6a0ae8116861bd22696fc50f4142a35ebfdb389e05ccf2de8ad142573fefc9e26f670522d899f7b2fe7386
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: 10c0/37217b7762ee0ea0d8b7d0c29fd48b7e4dfb94096b109d6255b589c561f57da93bf4e328c0290046115961b9209a8051ad9f525e48d433082fc79f496a4ea940
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: "npm:^2.1.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 10c0/6f8a41c87759fa184a58713b86c6a8b028250f158159f1d03ed9d1b6ee4d9eefdc74181c8ddc581a341aa971c3e7b79e30b59c23b05d2436d5de1c30bdef7171
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^4.0.0":
  version: 4.0.0
  resolution: "spdx-expression-parse@npm:4.0.0"
  dependencies:
    spdx-exceptions: "npm:^2.1.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 10c0/965c487e77f4fb173f1c471f3eef4eb44b9f0321adc7f93d95e7620da31faa67d29356eb02523cd7df8a7fc1ec8238773cdbf9e45bd050329d2b26492771b736
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: 10c0/ecb24c698d8496aa9efe23e0b1f751f8a7a89faedcdfcbfabae772b546c2db46ccde8f3bc447a238eb86bbcd4f73fea88720ef3b8394f7896381bec3d7736411
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: "npm:^4.1.0"
    detect-node: "npm:^2.0.4"
    hpack.js: "npm:^2.1.6"
    obuf: "npm:^1.1.2"
    readable-stream: "npm:^3.0.6"
    wbuf: "npm:^1.7.3"
  checksum: 10c0/eaf7440fa90724fffc813c386d4a8a7427d967d6e46d7c51d8f8a533d1a6911b9823ea9218703debbae755337e85f110185d7a00ae22ec5c847077b908ce71bb
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: "npm:^4.1.0"
    handle-thing: "npm:^2.0.0"
    http-deceiver: "npm:^1.2.7"
    select-hose: "npm:^2.0.0"
    spdy-transport: "npm:^3.0.0"
  checksum: 10c0/983509c0be9d06fd00bb9dff713c5b5d35d3ffd720db869acdd5ad7aa6fc0e02c2318b58f75328957d8ff772acdf1f7d19382b6047df342044ff3e2d6805ccdf
  languageName: node
  linkType: hard

"split2@npm:^3.0.0":
  version: 3.2.2
  resolution: "split2@npm:3.2.2"
  dependencies:
    readable-stream: "npm:^3.0.0"
  checksum: 10c0/2dad5603c52b353939befa3e2f108f6e3aff42b204ad0f5f16dd12fd7c2beab48d117184ce6f7c8854f9ee5ffec6faae70d243711dd7d143a9f635b4a285de4e
  languageName: node
  linkType: hard

"split2@npm:^4.2.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10c0/b292beb8ce9215f8c642bb68be6249c5a4c7f332fc8ecadae7be5cbdf1ea95addc95f0459ef2e7ad9d45fd1064698a097e4eb211c83e772b49bc0ee423e91534
  languageName: node
  linkType: hard

"split@npm:^1.0.0":
  version: 1.0.1
  resolution: "split@npm:1.0.1"
  dependencies:
    through: "npm:2"
  checksum: 10c0/7f489e7ed5ff8a2e43295f30a5197ffcb2d6202c9cf99357f9690d645b19c812bccf0be3ff336fea5054cda17ac96b91d67147d95dbfc31fbb5804c61962af85
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2, statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 10c0/14a351f0a066eaa08c8c64a74f4aedd87dd7a8e59d4be224703da33dca3eb370828ee6c0ae3fff59a9c743e8098728fc95c5f052ae7741672a31e6b1430ba50a
  languageName: node
  linkType: hard

"streamroller@npm:^3.1.5":
  version: 3.1.5
  resolution: "streamroller@npm:3.1.5"
  dependencies:
    date-format: "npm:^4.0.14"
    debug: "npm:^4.3.4"
    fs-extra: "npm:^8.1.0"
  checksum: 10c0/0bdeec34ad37487d959ba908f17067c938f544db88b5bb1669497a67a6b676413229ce5a6145c2812d06959ebeb8842e751076647d4b323ca06be612963b9099
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0, streamx@npm:^2.21.0":
  version: 2.22.0
  resolution: "streamx@npm:2.22.0"
  dependencies:
    bare-events: "npm:^2.2.0"
    fast-fifo: "npm:^1.3.2"
    text-decoder: "npm:^1.1.0"
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 10c0/f5017998a5b6360ba652599d20ef308c8c8ab0e26c8e5f624f0706f0ea12624e94fdf1ec18318124498529a1b106a1ab1c94a1b1e1ad6c2eec7cb9c8ac1b9198
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string-width@npm:^7.0.0":
  version: 7.2.0
  resolution: "string-width@npm:7.2.0"
  dependencies:
    emoji-regex: "npm:^10.3.0"
    get-east-asian-width: "npm:^1.0.0"
    strip-ansi: "npm:^7.1.0"
  checksum: 10c0/eb0430dd43f3199c7a46dcbf7a0b34539c76fe3aa62763d0b0655acdcbdf360b3f66f3d58ca25ba0205f42ea3491fa00f09426d3b7d3040e506878fc7664c9b9
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1, strip-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10c0/51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: "npm:^1.0.0"
  checksum: 10c0/ae0deaf41c8d1001c5d4fbe16cb553865c1863da4fae036683b474fa926af9fc121e155cb3fc57a68262b2ae7d5b8420aa752c97a6428c315d00efe2a3875679
  languageName: node
  linkType: hard

"strip-json-comments@npm:3.1.1, strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"symbol-observable@npm:4.0.0":
  version: 4.0.0
  resolution: "symbol-observable@npm:4.0.0"
  checksum: 10c0/5e9a3ab08263a6be8cbee76587ad5880dcc62a47002787ed5ebea56b1eb30dc87da6f0183d67e88286806799fbe21c69077fbd677be4be2188e92318d6c6f31d
  languageName: node
  linkType: hard

"synckit@npm:^0.9.1":
  version: 0.9.2
  resolution: "synckit@npm:0.9.2"
  dependencies:
    "@pkgr/core": "npm:^0.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e0c262817444e5b872708adb6f5ad37951ba33f6b2d1d4477d45db1f57573a784618ceed5e6614e0225db330632b1f6b95bb74d21e4d013e45ad4bde03d0cb59
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0, tapable@npm:^2.2.1":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 10c0/bc40e6efe1e554d075469cedaba69a30eeb373552aaf41caeaaa45bf56ffacc2674261b106245bd566b35d8f3329b52d838e851ee0a852120acae26e622925c9
  languageName: node
  linkType: hard

"tar-fs@npm:^2.0.0":
  version: 2.1.2
  resolution: "tar-fs@npm:2.1.2"
  dependencies:
    chownr: "npm:^1.1.1"
    mkdirp-classic: "npm:^0.5.2"
    pump: "npm:^3.0.0"
    tar-stream: "npm:^2.1.4"
  checksum: 10c0/9c704bd4a53be7565caf34ed001d1428532457fe3546d8fc1233f0f0882c3d2403f8602e8046e0b0adeb31fe95336572a69fb28851a391523126b697537670fc
  languageName: node
  linkType: hard

"tar-fs@npm:^3.0.4":
  version: 3.0.8
  resolution: "tar-fs@npm:3.0.8"
  dependencies:
    bare-fs: "npm:^4.0.1"
    bare-path: "npm:^3.0.0"
    pump: "npm:^3.0.0"
    tar-stream: "npm:^3.1.5"
  dependenciesMeta:
    bare-fs:
      optional: true
    bare-path:
      optional: true
  checksum: 10c0/b70bb2ad0490ab13b48edd10bd648bb54c52b681981cdcdc3aa4517e98ad94c94659ddca1925872ee658d781b9fcdd2b1c808050647f06b1bca157dd2fcae038
  languageName: node
  linkType: hard

"tar-stream@npm:^2.1.4":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: "npm:^4.0.3"
    end-of-stream: "npm:^1.4.1"
    fs-constants: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^3.1.1"
  checksum: 10c0/2f4c910b3ee7196502e1ff015a7ba321ec6ea837667220d7bcb8d0852d51cb04b87f7ae471008a6fb8f5b1a1b5078f62f3a82d30c706f20ada1238ac797e7692
  languageName: node
  linkType: hard

"tar-stream@npm:^3.1.5":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: "npm:^1.6.4"
    fast-fifo: "npm:^1.2.0"
    streamx: "npm:^2.15.0"
  checksum: 10c0/a09199d21f8714bd729993ac49b6c8efcb808b544b89f23378ad6ffff6d1cb540878614ba9d4cfec11a64ef39e1a6f009a5398371491eb1fda606ffc7f70f718
  languageName: node
  linkType: hard

"tar@npm:^6.1.11":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: 10c0/b1df969e3f3f7903f3426861887ed76ba3b495f63f6d0c8e1ce22588679d9384d336df6064210fda14e640ed422e2a17d5c40d901f60e161c99482d723f4d309
  languageName: node
  linkType: hard

"tempy@npm:^1.0.1":
  version: 1.0.1
  resolution: "tempy@npm:1.0.1"
  dependencies:
    del: "npm:^6.0.0"
    is-stream: "npm:^2.0.0"
    temp-dir: "npm:^2.0.0"
    type-fest: "npm:^0.16.0"
    unique-string: "npm:^2.0.0"
  checksum: 10c0/864a1cf1b5536dc21e84ae45dbbc3ba4dd2c7ec1674d895f99c349cf209df959a53d797ca38d0b2cf69c7684d565fde5cfc67faaa63b7208ffb21d454b957472
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.11":
  version: 5.3.14
  resolution: "terser-webpack-plugin@npm:5.3.14"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jest-worker: "npm:^27.4.5"
    schema-utils: "npm:^4.3.0"
    serialize-javascript: "npm:^6.0.2"
    terser: "npm:^5.31.1"
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 10c0/9b060947241af43bd6fd728456f60e646186aef492163672a35ad49be6fbc7f63b54a7356c3f6ff40a8f83f00a977edc26f044b8e106cc611c053c8c0eaf8569
  languageName: node
  linkType: hard

"terser@npm:5.39.0, terser@npm:^5.31.1":
  version: 5.39.0
  resolution: "terser@npm:5.39.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.8.2"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/83326545ea1aecd6261030568b6191ccfa4cb6aa61d9ea41746a52479f50017a78b77e4725fbbc207c5df841ffa66a773c5ac33636e95c7ab94fe7e0379ae5c7
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: "npm:^1.6.4"
  checksum: 10c0/569d776b9250158681c83656ef2c3e0a5d5c660c27ca69f87eedef921749a4fbf02095e5f9a0f862a25cf35258379b06e31dee9c125c9f72e273b7ca1a6d1977
  languageName: node
  linkType: hard

"text-extensions@npm:^1.0.0":
  version: 1.9.0
  resolution: "text-extensions@npm:1.9.0"
  checksum: 10c0/9ad5a9f723a871e2d884e132d7e93f281c60b5759c95f3f6b04704856548715d93a36c10dbaf5f12b91bf405f0cf3893bf169d4d143c0f5509563b992d385443
  languageName: node
  linkType: hard

"thingies@npm:^1.20.0":
  version: 1.21.0
  resolution: "thingies@npm:1.21.0"
  peerDependencies:
    tslib: ^2
  checksum: 10c0/7570ee855aecb73185a672ecf3eb1c287a6512bf5476449388433b2d4debcf78100bc8bfd439b0edd38d2bc3bfb8341de5ce85b8557dec66d0f27b962c9a8bc1
  languageName: node
  linkType: hard

"through2@npm:^2.0.0":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: "npm:~2.3.6"
    xtend: "npm:~4.0.1"
  checksum: 10c0/cbfe5b57943fa12b4f8c043658c2a00476216d79c014895cef1ac7a1d9a8b31f6b438d0e53eecbb81054b93128324a82ecd59ec1a4f91f01f7ac113dcb14eade
  languageName: node
  linkType: hard

"through2@npm:^4.0.0, through2@npm:^4.0.2":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: "npm:3"
  checksum: 10c0/3741564ae99990a4a79097fe7a4152c22348adc4faf2df9199a07a66c81ed2011da39f631e479fdc56483996a9d34a037ad64e76d79f18c782ab178ea9b6778c
  languageName: node
  linkType: hard

"through@npm:2, through@npm:>=2.2.7 <3":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 10c0/4b09f3774099de0d4df26d95c5821a62faee32c7e96fb1f4ebd54a2d7c11c57fe88b0a0d49cf375de5fee5ae6bf4eb56dbbf29d07366864e2ee805349970d3cc
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 10c0/369764f39de1ce1de2ba2fa922db4a3f92e9c7f33bcc9a713241bc1f4a5238b484c17e0d36d1d533c625efb00e9e82c3e45f80b47586945557b45abb890156d2
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.13
  resolution: "tinyglobby@npm:0.2.13"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/ef07dfaa7b26936601d3f6d999f7928a4d1c6234c5eb36896bb88681947c0d459b7ebe797022400e555fe4b894db06e922b95d0ce60cb05fd827a0a66326b18c
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10c0/69863947b8c29cabad43fe0ce65cec5bb4b481d15d4b4b21e036b060b3edbf3bc7a5541de1bacb437bb3f7c4538f669752627fdf9b4aaf034cebd172ba373408
  languageName: node
  linkType: hard

"tmp@npm:^0.2.1":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 10c0/3e809d9c2f46817475b452725c2aaa5d11985cf18d32a7a970ff25b568438e2c076c2e8609224feef3b7923fa9749b74428e3e634f6b8e520c534eef2fd24125
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"tree-dump@npm:^1.0.1":
  version: 1.0.2
  resolution: "tree-dump@npm:1.0.2"
  peerDependencies:
    tslib: 2
  checksum: 10c0/d1d180764e9c691b28332dbd74226c6b6af361dfb1e134bb11e60e17cb11c215894adee50ffc578da5dcf546006693947be8b6665eb1269b56e2f534926f1c1f
  languageName: node
  linkType: hard

"tree-kill@npm:1.2.2, tree-kill@npm:^1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 10c0/7b1b7c7f17608a8f8d20a162e7957ac1ef6cd1636db1aba92f4e072dc31818c2ff0efac1e3d91064ede67ed5dc57c565420531a8134090a12ac10cf792ab14d2
  languageName: node
  linkType: hard

"trim-newlines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-newlines@npm:3.0.1"
  checksum: 10c0/03cfefde6c59ff57138412b8c6be922ecc5aec30694d784f2a65ef8dcbd47faef580b7de0c949345abdc56ec4b4abf64dd1e5aea619b200316e471a3dd5bf1f6
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.0.1":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10c0/9806a38adea2db0f6aa217ccc6bc9c391ddba338a9fe3080676d0d50ed806d305bb90e8cef0276e793d28c8a929f400abb184ddd7ff83a416959c0f4d2ce754f
  languageName: node
  linkType: hard

"ts-node@npm:^10.2.1":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": "npm:^0.8.0"
    "@tsconfig/node10": "npm:^1.0.7"
    "@tsconfig/node12": "npm:^1.0.7"
    "@tsconfig/node14": "npm:^1.0.0"
    "@tsconfig/node16": "npm:^1.0.2"
    acorn: "npm:^8.4.1"
    acorn-walk: "npm:^8.1.1"
    arg: "npm:^4.1.0"
    create-require: "npm:^1.1.0"
    diff: "npm:^4.0.1"
    make-error: "npm:^1.1.1"
    v8-compile-cache-lib: "npm:^3.0.1"
    yn: "npm:3.1.1"
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 10c0/5f29938489f96982a25ba650b64218e83a3357d76f7bede80195c65ab44ad279c8357264639b7abdd5d7e75fc269a83daa0e9c62fd8637a3def67254ecc9ddc2
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/5b4f301a2b7a3766a986baf8fc0e177eb80bdba6e396792ff92dc23b5bca8bb279fc96517dcaaef63a3b49bebc6c4c833653ec58155780bc906bdbcf7dda0ef5
  languageName: node
  linkType: hard

"tslib@npm:2.6.2, tslib@npm:^2.4.0":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 10c0/e03a8a4271152c8b26604ed45535954c0a45296e32445b4b87f8a5abdb2421f40b59b4ca437c4346af0f28179780d604094eb64546bee2019d903d01c6c19bdb
  languageName: node
  linkType: hard

"tslib@npm:2.8.1, tslib@npm:^2.0.0, tslib@npm:^2.0.1, tslib@npm:^2.1.0, tslib@npm:^2.3.0, tslib@npm:^2.6.0, tslib@npm:^2.6.2, tslib@npm:^2.8.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tuf-js@npm:^3.0.1":
  version: 3.0.1
  resolution: "tuf-js@npm:3.0.1"
  dependencies:
    "@tufjs/models": "npm:3.0.1"
    debug: "npm:^4.3.6"
    make-fetch-happen: "npm:^14.0.1"
  checksum: 10c0/4214dd6bb1ec8a6cadbc5690e5a8556de0306f0e95022e54fc7c0ff9dbcc229ab379fd4b048511387f9c0023ea8f8c35acd8f7313f6cbc94a1b8af8b289f62ad
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/4c7a1b813e7beae66fdbf567a65ec6d46313643753d0beefb3c7973d66fcec3a1e7f39759f0a0b4465883499c6dc8b0750ab8b287399af2e583823e40410a17a
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-fest@npm:^0.16.0":
  version: 0.16.0
  resolution: "type-fest@npm:0.16.0"
  checksum: 10c0/6b4d846534e7bcb49a6160b068ffaed2b62570d989d909ac3f29df5ef1e993859f890a4242eebe023c9e923f96adbcb3b3e88a198c35a1ee9a731e147a6839c3
  languageName: node
  linkType: hard

"type-fest@npm:^0.18.0":
  version: 0.18.1
  resolution: "type-fest@npm:0.18.1"
  checksum: 10c0/303f5ecf40d03e1d5b635ce7660de3b33c18ed8ebc65d64920c02974d9e684c72483c23f9084587e9dd6466a2ece1da42ddc95b412a461794dd30baca95e2bac
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: 10c0/0c585c26416fce9ecb5691873a1301b5aff54673c7999b6f925691ed01f5b9232db408cdbb0bd003d19f5ae284322523f44092d1f81ca0a48f11f7cf0be8cd38
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: 10c0/dffbb99329da2aa840f506d376c863bd55f5636f4741ad6e65e82f5ce47e6914108f44f340a0b74009b0cb5d09d6752ae83203e53e98b1192cf80ecee5651636
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typed-assert@npm:^1.0.8":
  version: 1.0.9
  resolution: "typed-assert@npm:1.0.9"
  checksum: 10c0/9a31b03e6a5f07f13267f34dbbd125274b3b9e5107b906d76b2e401f6f60ebdea01124be8e3c064549938f57ac4e1b4f5a9c04e32bc8974b2f8cc74825e8b83e
  languageName: node
  linkType: hard

"typescript@npm:~5.6.3":
  version: 5.6.3
  resolution: "typescript@npm:5.6.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/44f61d3fb15c35359bc60399cb8127c30bae554cd555b8e2b46d68fa79d680354b83320ad419ff1b81a0bdf324197b29affe6cc28988cd6a74d4ac60c94f9799
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~5.6.3#optional!builtin<compat/typescript>":
  version: 5.6.3
  resolution: "typescript@patch:typescript@npm%3A5.6.3#optional!builtin<compat/typescript>::version=5.6.3&hash=8c6c40"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/7c9d2e07c81226d60435939618c91ec2ff0b75fbfa106eec3430f0fcf93a584bc6c73176676f532d78c3594fe28a54b36eb40b3d75593071a7ec91301533ace7
  languageName: node
  linkType: hard

"ua-parser-js@npm:^0.7.30":
  version: 0.7.40
  resolution: "ua-parser-js@npm:0.7.40"
  bin:
    ua-parser-js: script/cli.js
  checksum: 10c0/d114f0b71b5b0106dcc0cb7cc26a44690073e886fa1444f8c03131d4f57b3f6645f9fb7b308b0aaaa5a2774461f9e8fe1a2a1c3ff69aa531316fcf14cd44dbe3
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 10c0/83b0a90eca35f778e07cad9622b80c448b6aad457c9ff8e568afed978212b42930a95f9e1be943a1ffa4258a3340fbb899f41461131c05bb1d0a9c303aed8479
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.3.0":
  version: 0.3.0
  resolution: "unicorn-magic@npm:0.3.0"
  checksum: 10c0/0a32a997d6c15f1c2a077a15b1c4ca6f268d574cf5b8975e778bb98e6f8db4ef4e86dfcae4e158cd4c7e38fb4dd383b93b13eefddc7f178dea13d3ac8a603271
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: "npm:^2.0.0"
  checksum: 10c0/11820db0a4ba069d174bedfa96c588fc2c96b083066fafa186851e563951d0de78181ac79c744c1ed28b51f9d82ac5b8196ff3e4560d0178046ef455d8c2244b
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10c0/e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 10c0/d758e624c707d49f76f7511d75d09a8eda7f2020d231ec52b67ff4896bcf7013be3f9522d8375f57e586e9a2e827f5641c7e06ee46ab9c435fc2b2b2e9de517a
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/2eee5723b0fcce8256f5bfd3112af6c453b5471db00af9c3533e3d5a6e57de83513f9a145a570890457bd7abf2c2aa05797291d950ac666e5a074895dc63168b
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 10c0/bdc36fb8095d3b41df197f5fb6f11e3a26adf4059df3213e3baa93810d8f0cc76f9a74aaefc18b73e91fe7e19154ed6f134eda6fded2e0f1c8d2272ed2d2d391
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1, validate-npm-package-license@npm:^3.0.4":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: "npm:^3.0.0"
    spdx-expression-parse: "npm:^3.0.0"
  checksum: 10c0/7b91e455a8de9a0beaa9fe961e536b677da7f48c9a493edf4d4d4a87fd80a7a10267d438723364e432c2fcd00b5650b5378275cded362383ef570276e6312f4f
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^6.0.0":
  version: 6.0.0
  resolution: "validate-npm-package-name@npm:6.0.0"
  checksum: 10c0/35d1896d90a4f00291cfc17077b553910d45018b3562841acc6471731794eeebe39b409f678e8c1fee8ef1786e087cac8dea19abdd43649c30fd0b9c752afa2f
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vite@npm:6.2.5":
  version: 6.2.5
  resolution: "vite@npm:6.2.5"
  dependencies:
    esbuild: "npm:^0.25.0"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.5.3"
    rollup: "npm:^4.30.1"
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/226bb3c1875e1982559007007580e8d083b81f5289f18e28841d622ba030599e1bd9926adccc8264879e319e9f9e4f48a38a0dc52a5dfcdf2a9cb7313bfc1816
  languageName: node
  linkType: hard

"void-elements@npm:^2.0.0":
  version: 2.0.1
  resolution: "void-elements@npm:2.0.1"
  checksum: 10c0/23b4f35bbeabcaa5c87a9f638ae80862a9313dccbaa8973b0eada81dbe97488ae11baf4d8aa2846bc397d31456afdfd8d791bb44c542f83735e6d04af6996f4d
  languageName: node
  linkType: hard

"watchpack@npm:2.4.2, watchpack@npm:^2.4.1":
  version: 2.4.2
  resolution: "watchpack@npm:2.4.2"
  dependencies:
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.1.2"
  checksum: 10c0/ec60a5f0e9efaeca0102fd9126346b3b2d523e01c34030d3fddf5813a7125765121ebdc2552981136dcd2c852deb1af0b39340f2fcc235f292db5399d0283577
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: "npm:^1.0.0"
  checksum: 10c0/56edcc5ef2b3d30913ba8f1f5cccc364d180670b24d5f3f8849c1e6fb514e5c7e3a87548ae61227a82859eba6269c11393ae24ce12a2ea1ecb9b465718ddced7
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"weak-lru-cache@npm:^1.2.2":
  version: 1.2.2
  resolution: "weak-lru-cache@npm:1.2.2"
  checksum: 10c0/744847bd5b96ca86db1cb40d0aea7e92c02bbdb05f501181bf9c581e82fa2afbda32a327ffbe75749302b8492ab449f1c657ca02410d725f5d412d1e6c607d72
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:7.4.2, webpack-dev-middleware@npm:^7.4.2":
  version: 7.4.2
  resolution: "webpack-dev-middleware@npm:7.4.2"
  dependencies:
    colorette: "npm:^2.0.10"
    memfs: "npm:^4.6.0"
    mime-types: "npm:^2.1.31"
    on-finished: "npm:^2.4.1"
    range-parser: "npm:^1.2.1"
    schema-utils: "npm:^4.0.0"
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
  checksum: 10c0/2aa873ef57a7095d7fba09400737b6066adc3ded229fd6eba89a666f463c2614c68e01ae58f662c9cdd74f0c8da088523d972329bf4a054e470bc94feb8bcad0
  languageName: node
  linkType: hard

"webpack-dev-server@npm:5.2.0":
  version: 5.2.0
  resolution: "webpack-dev-server@npm:5.2.0"
  dependencies:
    "@types/bonjour": "npm:^3.5.13"
    "@types/connect-history-api-fallback": "npm:^1.5.4"
    "@types/express": "npm:^4.17.21"
    "@types/serve-index": "npm:^1.9.4"
    "@types/serve-static": "npm:^1.15.5"
    "@types/sockjs": "npm:^0.3.36"
    "@types/ws": "npm:^8.5.10"
    ansi-html-community: "npm:^0.0.8"
    bonjour-service: "npm:^1.2.1"
    chokidar: "npm:^3.6.0"
    colorette: "npm:^2.0.10"
    compression: "npm:^1.7.4"
    connect-history-api-fallback: "npm:^2.0.0"
    express: "npm:^4.21.2"
    graceful-fs: "npm:^4.2.6"
    http-proxy-middleware: "npm:^2.0.7"
    ipaddr.js: "npm:^2.1.0"
    launch-editor: "npm:^2.6.1"
    open: "npm:^10.0.3"
    p-retry: "npm:^6.2.0"
    schema-utils: "npm:^4.2.0"
    selfsigned: "npm:^2.4.1"
    serve-index: "npm:^1.9.1"
    sockjs: "npm:^0.3.24"
    spdy: "npm:^4.0.2"
    webpack-dev-middleware: "npm:^7.4.2"
    ws: "npm:^8.18.0"
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: 10c0/afb2e51945ac54ef3039e11e377241e1cb97a8d3f526f39f13c3fa924c530fb6063200c2c3ae4e33e6bcc110d4abed777c09ce18e2d261012853d81f3c5820ab
  languageName: node
  linkType: hard

"webpack-merge@npm:6.0.1":
  version: 6.0.1
  resolution: "webpack-merge@npm:6.0.1"
  dependencies:
    clone-deep: "npm:^4.0.1"
    flat: "npm:^5.0.2"
    wildcard: "npm:^2.0.1"
  checksum: 10c0/bf1429567858b353641801b8a2696ca0aac270fc8c55d4de8a7b586fe07d27fdcfc83099a98ab47e6162383db8dd63bb8cc25b1beb2ec82150422eec843b0dc0
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.0.0, webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 10c0/2ef63d77c4fad39de4a6db17323d75eb92897b32674e97d76f0a1e87c003882fc038571266ad0ef581ac734cbe20952912aaa26155f1905e96ce251adbb1eb4e
  languageName: node
  linkType: hard

"webpack-subresource-integrity@npm:5.1.0":
  version: 5.1.0
  resolution: "webpack-subresource-integrity@npm:5.1.0"
  dependencies:
    typed-assert: "npm:^1.0.8"
  peerDependencies:
    html-webpack-plugin: ">= 5.0.0-beta.1 < 6"
    webpack: ^5.12.0
  peerDependenciesMeta:
    html-webpack-plugin:
      optional: true
  checksum: 10c0/7def5d995a43fc5e60097084e68145359fbeb47b96cb9c87ee811d70c5eb99766c28974ef28f09ae6b1d1b3cd2b7c35838b36206f1c39d090a8f531cd1fbbf6a
  languageName: node
  linkType: hard

"webpack@npm:5.98.0":
  version: 5.98.0
  resolution: "webpack@npm:5.98.0"
  dependencies:
    "@types/eslint-scope": "npm:^3.7.7"
    "@types/estree": "npm:^1.0.6"
    "@webassemblyjs/ast": "npm:^1.14.1"
    "@webassemblyjs/wasm-edit": "npm:^1.14.1"
    "@webassemblyjs/wasm-parser": "npm:^1.14.1"
    acorn: "npm:^8.14.0"
    browserslist: "npm:^4.24.0"
    chrome-trace-event: "npm:^1.0.2"
    enhanced-resolve: "npm:^5.17.1"
    es-module-lexer: "npm:^1.2.1"
    eslint-scope: "npm:5.1.1"
    events: "npm:^3.2.0"
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.2.11"
    json-parse-even-better-errors: "npm:^2.3.1"
    loader-runner: "npm:^4.2.0"
    mime-types: "npm:^2.1.27"
    neo-async: "npm:^2.6.2"
    schema-utils: "npm:^4.3.0"
    tapable: "npm:^2.1.1"
    terser-webpack-plugin: "npm:^5.3.11"
    watchpack: "npm:^2.4.1"
    webpack-sources: "npm:^3.2.3"
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 10c0/bee4fa77f444802f0beafb2ff30eb5454a606163ad7d3cc9a5dcc9d24033c62407bed04601b25dea49ea3969b352c1b530a86c753246f42560a4a084eefb094e
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: "npm:>=0.5.1"
    safe-buffer: "npm:>=5.1.0"
    websocket-extensions: "npm:>=0.1.1"
  checksum: 10c0/5f09547912b27bdc57bac17b7b6527d8993aa4ac8a2d10588bb74aebaf785fdcf64fea034aae0c359b7adff2044dd66f3d03866e4685571f81b13e548f9021f1
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 10c0/bbc8c233388a0eb8a40786ee2e30d35935cacbfe26ab188b3e020987e85d519c2009fe07cfc37b7f718b85afdba7e54654c9153e6697301f72561bfe429177e0
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10c0/087038e7992649eaffa6c7a4f3158d5b53b14cf5b6c1f0e043dccfacb1ba179d12f17545d5b85ebd94a42ce280a6fe65d0cbcab70f4fc6daad1dfae85e0e6a3e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^1.2.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10c0/e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.1":
  version: 2.0.1
  resolution: "wildcard@npm:2.0.1"
  checksum: 10c0/08f70cd97dd9a20aea280847a1fe8148e17cae7d231640e41eb26d2388697cbe65b67fd9e68715251c39b080c5ae4f76d71a9a69fa101d897273efdfb1b58bf7
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 10c0/7ed2e44f3c33c5c3e3771134d2b0aee4314c9e49c749e37f464bf69f2bcdf0cbf9419ca638098e2717cff4875c47f56a007532f6111c3319f557a2ca91278e92
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrap-ansi@npm:^9.0.0":
  version: 9.0.0
  resolution: "wrap-ansi@npm:9.0.0"
  dependencies:
    ansi-styles: "npm:^6.2.1"
    string-width: "npm:^7.0.0"
    strip-ansi: "npm:^7.1.0"
  checksum: 10c0/a139b818da9573677548dd463bd626a5a5286271211eb6e4e82f34a4f643191d74e6d4a9bb0a3c26ec90e6f904f679e0569674ac099ea12378a8b98e20706066
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.1
  resolution: "ws@npm:8.18.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/e498965d6938c63058c4310ffb6967f07d4fa06789d3364829028af380d299fe05762961742971c764973dce3d1f6a2633fe8b2d9410c9b52e534b4b882a99fa
  languageName: node
  linkType: hard

"ws@npm:~8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/f4a49064afae4500be772abdc2211c8518f39e1c959640457dcee15d4488628620625c783902a52af2dd02f68558da2868fd06e6fd0e67ebcd09e6881b1b5bfe
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: "npm:^1.1.0"
    uuid: "npm:^7.0.3"
  checksum: 10c0/51bf35cee52909aeb18f868ecf9828f93b8042fadf968159320f9f11e757a52e43f6563a53b586986cfe5a34d576f3300c4c0cf1e14300084344ae206eaa53c3
  languageName: node
  linkType: hard

"xml-js@npm:^1.6.11":
  version: 1.6.11
  resolution: "xml-js@npm:1.6.11"
  dependencies:
    sax: "npm:^1.2.4"
  bin:
    xml-js: ./bin/cli.js
  checksum: 10c0/c83631057f10bf90ea785cee434a8a1a0030c7314fe737ad9bf568a281083b565b28b14c9e9ba82f11fc9dc582a3a907904956af60beb725be1c9ad4b030bc5a
  languageName: node
  linkType: hard

"xml2js@npm:^0.5.0":
  version: 0.5.0
  resolution: "xml2js@npm:0.5.0"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/c9cd07cd19c5e41c740913bbbf16999a37a204488e11f86eddc2999707d43967197e257014d7ed72c8fc4348c192fa47eb352d1d9d05637cefd0d2e24e9aa4c8
  languageName: node
  linkType: hard

"xml2js@npm:^0.6.2":
  version: 0.6.2
  resolution: "xml2js@npm:0.6.2"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/e98a84e9c172c556ee2c5afa0fc7161b46919e8b53ab20de140eedea19903ed82f7cd5b1576fb345c84f0a18da1982ddf65908129b58fc3d7cbc658ae232108f
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 10c0/665266a8916498ff8d82b3d46d3993913477a254b98149ff7cff060d9b7cc0db7cf5a3dae99aed92355254a808c0e2e3ec74ad1b04aa1061bdb8dfbea26c18b8
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"xpath@npm:0.0.27":
  version: 0.0.27
  resolution: "xpath@npm:0.0.27"
  checksum: 10c0/d51bc49435e807b640f6187f7aabd3e0c93073408c0636273c948d0d1a02243cb07d434a74d7b12509547053ee4bcc944c31c1afc6e0e6e9417d5312f5e58e5c
  languageName: node
  linkType: hard

"xpath@npm:^0.0.32":
  version: 0.0.32
  resolution: "xpath@npm:0.0.32"
  checksum: 10c0/3743ab91a8ec1b5eac1f27ddf2fbf696fcde8ce487215becde1502b85a309dcd1b0baeaac1ee7a730aea4787d049b67ae89e8aedbe03a5a07a71e62ec296d9de
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10c0/308a2efd7cc296ab2c0f3b9284fd4827be01cfeb647b3ba18230e3a416eb1bc887ac050de9f8c4fd9e7856b2e8246e05d190b53c96c5ad8d8cb56dffb6f81024
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10c0/25df918833592a83f52e7e4f91ba7d7bfaa2b891ebf7fe901923c2ee797534f23a176913ff6ff7ebbc1cc1725a044cc6a6539fed8bfd4e13b5b16376875f9499
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2, yargs-parser@npm:^20.2.3":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 10c0/0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:17.7.2, yargs@npm:^17.2.1":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10c0/f1ca680c974333a5822732825cca7e95306c5a1e7750eb7b973ce6dc4f97a6b0a8837203c8b194f461969bfe1fb1176d1d423036635285f6010b392fa498ab2d
  languageName: node
  linkType: hard

"yargs@npm:^16.1.1, yargs@npm:^16.2.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: 10c0/b1dbfefa679848442454b60053a6c95d62f2d2e21dd28def92b647587f415969173c6e99a0f3bab4f1b67ee8283bf735ebe3544013f09491186ba9e8a9a2b651
  languageName: node
  linkType: hard

"yauzl@npm:^2.10.0":
  version: 2.10.0
  resolution: "yauzl@npm:2.10.0"
  dependencies:
    buffer-crc32: "npm:~0.2.3"
    fd-slicer: "npm:~1.1.0"
  checksum: 10c0/f265002af7541b9ec3589a27f5fb8f11cf348b53cc15e2751272e3c062cd73f3e715bc72d43257de71bbaecae446c3f1b14af7559e8ab0261625375541816422
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 10c0/0732468dd7622ed8a274f640f191f3eaf1f39d5349a1b72836df484998d7d9807fbea094e2f5486d6b0cd2414aad5775972df0e68f8604db89a239f0f4bf7443
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"yocto-queue@npm:^1.0.0":
  version: 1.2.1
  resolution: "yocto-queue@npm:1.2.1"
  checksum: 10c0/5762caa3d0b421f4bdb7a1926b2ae2189fc6e4a14469258f183600028eb16db3e9e0306f46e8ebf5a52ff4b81a881f22637afefbef5399d6ad440824e9b27f9f
  languageName: node
  linkType: hard

"yoctocolors-cjs@npm:^2.1.2":
  version: 2.1.2
  resolution: "yoctocolors-cjs@npm:2.1.2"
  checksum: 10c0/a0e36eb88fea2c7981eab22d1ba45e15d8d268626e6c4143305e2c1628fa17ebfaa40cd306161a8ce04c0a60ee0262058eab12567493d5eb1409780853454c6f
  languageName: node
  linkType: hard

"zone.js@npm:~0.15.0":
  version: 0.15.0
  resolution: "zone.js@npm:0.15.0"
  checksum: 10c0/981b664c1978759a2854f6e6692d245d1d6334c6b20b7e2e5fa9b60eed74743b29c6a71f7472dc6d2790ab53d67e30475bcd92b9f7664e50aef8bbcd40379552
  languageName: node
  linkType: hard
